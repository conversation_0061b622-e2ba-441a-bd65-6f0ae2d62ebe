@echo off
title RuoYi Vue Pro - Rebuild with AI Module

echo ========================================
echo    RuoYi Vue Pro Rebuild with AI Module
echo ========================================
echo.

:: Configuration
set PROJECT_ROOT=G:\devAI\ruoyi-vue-pro-1735
set JAVA_HOME=E:\Java\jdk-17.0.16.8
set MAVEN_HOME=D:\maven\apache-maven-3.8.1
set MAVEN_SETTINGS=D:\maven\apache-maven-3.8.1\conf\settings_nis.xml
set MAVEN_REPO=D:\maven\nisRepository

:: Set environment
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%
set MAVEN_OPTS=-Xms512m -Xmx2048m

echo [INFO] Current Active Modules:
echo   ✓ yudao-dependencies
echo   ✓ yudao-framework
echo   ✓ yudao-server
echo   ✓ yudao-module-system
echo   ✓ yudao-module-infra
echo   ✓ yudao-module-report
echo   ✓ yudao-module-ai (AI Module - ENABLED)
echo.

echo [INFO] AI Module Status:
echo   ✓ Database tables created (12 AI tables)
echo   ✓ Module enabled in pom.xml
echo   ✓ Configuration files present
echo.

cd /d "%PROJECT_ROOT%"

echo [STEP 1/4] Cleaning project...
mvn clean -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -q
if errorlevel 1 (
    echo [ERROR] Clean failed
    pause
    exit /b 1
)
echo [SUCCESS] Clean completed

echo.
echo [STEP 2/4] Compiling with AI module...
mvn compile -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -T 1C
if errorlevel 1 (
    echo [ERROR] Compile failed
    echo [INFO] Check if AI module dependencies are correct
    pause
    exit /b 1
)
echo [SUCCESS] Compile completed

echo.
echo [STEP 3/4] Packaging with AI module...
mvn package -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -DskipTests -T 1C
if errorlevel 1 (
    echo [ERROR] Package failed
    pause
    exit /b 1
)
echo [SUCCESS] Package completed

echo.
echo [STEP 4/4] Verifying JAR file...
set JAR_FILE=%PROJECT_ROOT%\yudao-server\target\yudao-server.jar
if exist "%JAR_FILE%" (
    echo [SUCCESS] JAR file created: %JAR_FILE%
    for %%I in ("%JAR_FILE%") do echo [INFO] Size: %%~zI bytes
) else (
    echo [ERROR] JAR file not found
    pause
    exit /b 1
)

echo.
echo ========================================
echo [BUILD SUCCESS] Project with AI Module Ready!
echo ========================================
echo.

echo [INFO] AI Module Configuration:
echo   - Database: 12 AI tables created
echo   - Spring AI: 1.0.0
echo   - Alibaba AI: *******
echo   - TinyFlow: 1.0.2
echo.

echo [NEXT STEPS] Start the application:
echo   1. Run: start-ai.bat
echo   2. Access: http://localhost:48080
echo   3. Login and check AI module menu
echo.

echo [INFO] If you still see table structure error:
echo   1. Check application startup logs
echo   2. Verify database connection
echo   3. Ensure all AI tables exist
echo.
pause
