@echo off
title RuoYi Vue Pro - Start with AI Module

echo ========================================
echo    RuoYi Vue Pro Start with AI Module
echo ========================================
echo.

:: Configuration
set PROJECT_ROOT=G:\devAI\ruoyi-vue-pro-1735
set JAVA_HOME=E:\Java\jdk-17.0.16.8
set MAVEN_HOME=D:\maven\apache-maven-3.8.1

:: Set environment
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%

:: JVM parameters optimized for AI module
set JVM_OPTS=-Xms1024m -Xmx3072m
set JVM_OPTS=%JVM_OPTS% -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1024m
set JVM_OPTS=%JVM_OPTS% -XX:+UseG1GC
set JVM_OPTS=%JVM_OPTS% -Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Duser.timezone=Asia/Shanghai

:: Spring Boot parameters
set SPRING_OPTS=--spring.profiles.active=local
set SPRING_OPTS=%SPRING_OPTS% --server.port=48080
set SPRING_OPTS=%SPRING_OPTS% --logging.level.cn.iocoder=DEBUG
set SPRING_OPTS=%SPRING_OPTS% --logging.level.cn.iocoder.yudao.module.ai=DEBUG

echo [INFO] AI Module Startup Configuration:
echo   ✓ AI Module: ENABLED
echo   ✓ Database Tables: 12 AI tables ready
echo   ✓ Memory: 3GB allocated for AI processing
echo   ✓ Debug Logging: Enabled for AI module
echo.

echo [INFO] Environment check...
java -version 2>&1 | findstr "openjdk version"
echo.

cd /d "%PROJECT_ROOT%"

:: Check JAR file
set JAR_FILE=%PROJECT_ROOT%\yudao-server\target\yudao-server.jar
if exist "%JAR_FILE%" (
    echo [INFO] Using JAR file: %JAR_FILE%
    echo [INFO] JVM Options: %JVM_OPTS%
    echo [INFO] Spring Options: %SPRING_OPTS%
    echo.
    echo [INFO] Starting RuoYi Vue Pro with AI Module...
    echo.
    echo [INFO] Server will start at:
    echo   - Backend API: http://localhost:48080
    echo   - API Documentation: http://localhost:48080/doc.html
    echo   - Admin Panel: http://localhost:48080/admin
    echo.
    echo [INFO] AI Module Features:
    echo   - AI Chat: /admin-api/ai/chat/**
    echo   - AI Image: /admin-api/ai/image/**
    echo   - AI Music: /admin-api/ai/music/**
    echo   - AI Knowledge: /admin-api/ai/knowledge/**
    echo.
    echo [INFO] Press Ctrl+C to stop the service
    echo ========================================
    echo.
    
    java %JVM_OPTS% -jar "%JAR_FILE%" %SPRING_OPTS%
) else (
    echo [WARNING] JAR file not found, building first...
    echo [INFO] Running rebuild-with-ai.bat...
    call rebuild-with-ai.bat
    if errorlevel 1 (
        echo [ERROR] Build failed
        pause
        exit /b 1
    )
    
    echo [INFO] Starting with JAR file...
    java %JVM_OPTS% -jar "%JAR_FILE%" %SPRING_OPTS%
)

echo.
echo [INFO] Service stopped
pause
