import {
  MarkerModel_default,
  Mark<PERSON><PERSON>iew_default,
  checkMarkerInSeries,
  createMarkerDimValueGetter,
  dataFilter,
  dataTransform,
  getAxisInfo,
  numCalculate
} from "./chunk-ZR4V7UZP.js";
import {
  LineDraw_default
} from "./chunk-ZONBLEOS.js";
import {
  isCoordinateSystemType
} from "./chunk-QAR3K42R.js";
import {
  SeriesData_default,
  getStackedDimension,
  getVisualFromData
} from "./chunk-H732WCN4.js";
import {
  getECData,
  makeInner,
  parsePercent
} from "./chunk-Q47K3BNQ.js";
import {
  __extends,
  clone,
  curry,
  extend,
  filter,
  isArray,
  isNumber,
  logError,
  map,
  merge,
  retrieve,
  retrieve2
} from "./chunk-MUBQFVAI.js";

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkLineModel.js
var MarkLineModel = (
  /** @class */
  function(_super) {
    __extends(MarkLineModel2, _super);
    function MarkLineModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkLineModel2.type;
      return _this;
    }
    MarkLineModel2.prototype.createMarkerModelFromSeries = function(markerOpt, masterMarkerModel, ecModel) {
      return new MarkLineModel2(markerOpt, masterMarkerModel, ecModel);
    };
    MarkLineModel2.type = "markLine";
    MarkLineModel2.defaultOption = {
      // zlevel: 0,
      z: 5,
      symbol: ["circle", "arrow"],
      symbolSize: [8, 16],
      // symbolRotate: 0,
      symbolOffset: 0,
      precision: 2,
      tooltip: {
        trigger: "item"
      },
      label: {
        show: true,
        position: "end",
        distance: 5
      },
      lineStyle: {
        type: "dashed"
      },
      emphasis: {
        label: {
          show: true
        },
        lineStyle: {
          width: 3
        }
      },
      animationEasing: "linear"
    };
    return MarkLineModel2;
  }(MarkerModel_default)
);
var MarkLineModel_default = MarkLineModel;

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkLineView.js
var inner = makeInner();
var markLineTransform = function(seriesModel, coordSys, mlModel, item) {
  var data = seriesModel.getData();
  var itemArray;
  if (!isArray(item)) {
    var mlType = item.type;
    if (mlType === "min" || mlType === "max" || mlType === "average" || mlType === "median" || item.xAxis != null || item.yAxis != null) {
      var valueAxis = void 0;
      var value = void 0;
      if (item.yAxis != null || item.xAxis != null) {
        valueAxis = coordSys.getAxis(item.yAxis != null ? "y" : "x");
        value = retrieve(item.yAxis, item.xAxis);
      } else {
        var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);
        valueAxis = axisInfo.valueAxis;
        var valueDataDim = getStackedDimension(data, axisInfo.valueDataDim);
        value = numCalculate(data, valueDataDim, mlType);
      }
      var valueIndex = valueAxis.dim === "x" ? 0 : 1;
      var baseIndex = 1 - valueIndex;
      var mlFrom = clone(item);
      var mlTo = {
        coord: []
      };
      mlFrom.type = null;
      mlFrom.coord = [];
      mlFrom.coord[baseIndex] = -Infinity;
      mlTo.coord[baseIndex] = Infinity;
      var precision = mlModel.get("precision");
      if (precision >= 0 && isNumber(value)) {
        value = +value.toFixed(Math.min(precision, 20));
      }
      mlFrom.coord[valueIndex] = mlTo.coord[valueIndex] = value;
      itemArray = [mlFrom, mlTo, {
        type: mlType,
        valueIndex: item.valueIndex,
        // Force to use the value of calculated value.
        value
      }];
    } else {
      if (true) {
        logError("Invalid markLine data.");
      }
      itemArray = [];
    }
  } else {
    itemArray = item;
  }
  var normalizedItem = [dataTransform(seriesModel, itemArray[0]), dataTransform(seriesModel, itemArray[1]), extend({}, itemArray[2])];
  normalizedItem[2].type = normalizedItem[2].type || null;
  merge(normalizedItem[2], normalizedItem[0]);
  merge(normalizedItem[2], normalizedItem[1]);
  return normalizedItem;
};
function isInfinity(val) {
  return !isNaN(val) && !isFinite(val);
}
function ifMarkLineHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {
  var otherDimIndex = 1 - dimIndex;
  var dimName = coordSys.dimensions[dimIndex];
  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]) && fromCoord[dimIndex] === toCoord[dimIndex] && coordSys.getAxis(dimName).containData(fromCoord[dimIndex]);
}
function markLineFilter(coordSys, item) {
  if (coordSys.type === "cartesian2d") {
    var fromCoord = item[0].coord;
    var toCoord = item[1].coord;
    if (fromCoord && toCoord && (ifMarkLineHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkLineHasOnlyDim(0, fromCoord, toCoord, coordSys))) {
      return true;
    }
  }
  return dataFilter(coordSys, item[0]) && dataFilter(coordSys, item[1]);
}
function updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api) {
  var coordSys = seriesModel.coordinateSystem;
  var itemModel = data.getItemModel(idx);
  var point;
  var xPx = parsePercent(itemModel.get("x"), api.getWidth());
  var yPx = parsePercent(itemModel.get("y"), api.getHeight());
  if (!isNaN(xPx) && !isNaN(yPx)) {
    point = [xPx, yPx];
  } else {
    if (seriesModel.getMarkerPosition) {
      point = seriesModel.getMarkerPosition(data.getValues(data.dimensions, idx));
    } else {
      var dims = coordSys.dimensions;
      var x = data.get(dims[0], idx);
      var y = data.get(dims[1], idx);
      point = coordSys.dataToPoint([x, y]);
    }
    if (isCoordinateSystemType(coordSys, "cartesian2d")) {
      var xAxis = coordSys.getAxis("x");
      var yAxis = coordSys.getAxis("y");
      var dims = coordSys.dimensions;
      if (isInfinity(data.get(dims[0], idx))) {
        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[isFrom ? 0 : 1]);
      } else if (isInfinity(data.get(dims[1], idx))) {
        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[isFrom ? 0 : 1]);
      }
    }
    if (!isNaN(xPx)) {
      point[0] = xPx;
    }
    if (!isNaN(yPx)) {
      point[1] = yPx;
    }
  }
  data.setItemLayout(idx, point);
}
var MarkLineView = (
  /** @class */
  function(_super) {
    __extends(MarkLineView2, _super);
    function MarkLineView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkLineView2.type;
      return _this;
    }
    MarkLineView2.prototype.updateTransform = function(markLineModel, ecModel, api) {
      ecModel.eachSeries(function(seriesModel) {
        var mlModel = MarkerModel_default.getMarkerModelFromSeries(seriesModel, "markLine");
        if (mlModel) {
          var mlData_1 = mlModel.getData();
          var fromData_1 = inner(mlModel).from;
          var toData_1 = inner(mlModel).to;
          fromData_1.each(function(idx) {
            updateSingleMarkerEndLayout(fromData_1, idx, true, seriesModel, api);
            updateSingleMarkerEndLayout(toData_1, idx, false, seriesModel, api);
          });
          mlData_1.each(function(idx) {
            mlData_1.setItemLayout(idx, [fromData_1.getItemLayout(idx), toData_1.getItemLayout(idx)]);
          });
          this.markerGroupMap.get(seriesModel.id).updateLayout();
        }
      }, this);
    };
    MarkLineView2.prototype.renderSeries = function(seriesModel, mlModel, ecModel, api) {
      var coordSys = seriesModel.coordinateSystem;
      var seriesId = seriesModel.id;
      var seriesData = seriesModel.getData();
      var lineDrawMap = this.markerGroupMap;
      var lineDraw = lineDrawMap.get(seriesId) || lineDrawMap.set(seriesId, new LineDraw_default());
      this.group.add(lineDraw.group);
      var mlData = createList(coordSys, seriesModel, mlModel);
      var fromData = mlData.from;
      var toData = mlData.to;
      var lineData = mlData.line;
      inner(mlModel).from = fromData;
      inner(mlModel).to = toData;
      mlModel.setData(lineData);
      var symbolType = mlModel.get("symbol");
      var symbolSize = mlModel.get("symbolSize");
      var symbolRotate = mlModel.get("symbolRotate");
      var symbolOffset = mlModel.get("symbolOffset");
      if (!isArray(symbolType)) {
        symbolType = [symbolType, symbolType];
      }
      if (!isArray(symbolSize)) {
        symbolSize = [symbolSize, symbolSize];
      }
      if (!isArray(symbolRotate)) {
        symbolRotate = [symbolRotate, symbolRotate];
      }
      if (!isArray(symbolOffset)) {
        symbolOffset = [symbolOffset, symbolOffset];
      }
      mlData.from.each(function(idx) {
        updateDataVisualAndLayout(fromData, idx, true);
        updateDataVisualAndLayout(toData, idx, false);
      });
      lineData.each(function(idx) {
        var lineStyle = lineData.getItemModel(idx).getModel("lineStyle").getLineStyle();
        lineData.setItemLayout(idx, [fromData.getItemLayout(idx), toData.getItemLayout(idx)]);
        if (lineStyle.stroke == null) {
          lineStyle.stroke = fromData.getItemVisual(idx, "style").fill;
        }
        lineData.setItemVisual(idx, {
          fromSymbolKeepAspect: fromData.getItemVisual(idx, "symbolKeepAspect"),
          fromSymbolOffset: fromData.getItemVisual(idx, "symbolOffset"),
          fromSymbolRotate: fromData.getItemVisual(idx, "symbolRotate"),
          fromSymbolSize: fromData.getItemVisual(idx, "symbolSize"),
          fromSymbol: fromData.getItemVisual(idx, "symbol"),
          toSymbolKeepAspect: toData.getItemVisual(idx, "symbolKeepAspect"),
          toSymbolOffset: toData.getItemVisual(idx, "symbolOffset"),
          toSymbolRotate: toData.getItemVisual(idx, "symbolRotate"),
          toSymbolSize: toData.getItemVisual(idx, "symbolSize"),
          toSymbol: toData.getItemVisual(idx, "symbol"),
          style: lineStyle
        });
      });
      lineDraw.updateData(lineData);
      mlData.line.eachItemGraphicEl(function(el) {
        getECData(el).dataModel = mlModel;
        el.traverse(function(child) {
          getECData(child).dataModel = mlModel;
        });
      });
      function updateDataVisualAndLayout(data, idx, isFrom) {
        var itemModel = data.getItemModel(idx);
        updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api);
        var style = itemModel.getModel("itemStyle").getItemStyle();
        if (style.fill == null) {
          style.fill = getVisualFromData(seriesData, "color");
        }
        data.setItemVisual(idx, {
          symbolKeepAspect: itemModel.get("symbolKeepAspect"),
          // `0` should be considered as a valid value, so use `retrieve2` instead of `||`
          symbolOffset: retrieve2(itemModel.get("symbolOffset", true), symbolOffset[isFrom ? 0 : 1]),
          symbolRotate: retrieve2(itemModel.get("symbolRotate", true), symbolRotate[isFrom ? 0 : 1]),
          // TODO: when 2d array is supported, it should ignore parent
          symbolSize: retrieve2(itemModel.get("symbolSize"), symbolSize[isFrom ? 0 : 1]),
          symbol: retrieve2(itemModel.get("symbol", true), symbolType[isFrom ? 0 : 1]),
          style
        });
      }
      this.markKeep(lineDraw);
      lineDraw.group.silent = mlModel.get("silent") || seriesModel.get("silent");
    };
    MarkLineView2.type = "markLine";
    return MarkLineView2;
  }(MarkerView_default)
);
function createList(coordSys, seriesModel, mlModel) {
  var coordDimsInfos;
  if (coordSys) {
    coordDimsInfos = map(coordSys && coordSys.dimensions, function(coordDim) {
      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};
      return extend(extend({}, info), {
        name: coordDim,
        // DON'T use ordinalMeta to parse and collect ordinal.
        ordinalMeta: null
      });
    });
  } else {
    coordDimsInfos = [{
      name: "value",
      type: "float"
    }];
  }
  var fromData = new SeriesData_default(coordDimsInfos, mlModel);
  var toData = new SeriesData_default(coordDimsInfos, mlModel);
  var lineData = new SeriesData_default([], mlModel);
  var optData = map(mlModel.get("data"), curry(markLineTransform, seriesModel, coordSys, mlModel));
  if (coordSys) {
    optData = filter(optData, curry(markLineFilter, coordSys));
  }
  var dimValueGetter = createMarkerDimValueGetter(!!coordSys, coordDimsInfos);
  fromData.initData(map(optData, function(item) {
    return item[0];
  }), null, dimValueGetter);
  toData.initData(map(optData, function(item) {
    return item[1];
  }), null, dimValueGetter);
  lineData.initData(map(optData, function(item) {
    return item[2];
  }));
  lineData.hasItemOption = true;
  return {
    from: fromData,
    to: toData,
    line: lineData
  };
}
var MarkLineView_default = MarkLineView;

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/installMarkLine.js
function install(registers) {
  registers.registerComponentModel(MarkLineModel_default);
  registers.registerComponentView(MarkLineView_default);
  registers.registerPreprocessor(function(opt) {
    if (checkMarkerInSeries(opt.series, "markLine")) {
      opt.markLine = opt.markLine || {};
    }
  });
}

export {
  install
};
//# sourceMappingURL=chunk-6D5TN6IN.js.map
