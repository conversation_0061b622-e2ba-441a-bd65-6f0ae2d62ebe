{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkLineModel.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkLineView.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/installMarkLine.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkLineModel = /** @class */function (_super) {\n  __extends(MarkLineModel, _super);\n  function MarkLineModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkLineModel.type;\n    return _this;\n  }\n  MarkLineModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkLineModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkLineModel.type = 'markLine';\n  MarkLineModel.defaultOption = {\n    // zlevel: 0,\n    z: 5,\n    symbol: ['circle', 'arrow'],\n    symbolSize: [8, 16],\n    // symbolRotate: 0,\n    symbolOffset: 0,\n    precision: 2,\n    tooltip: {\n      trigger: 'item'\n    },\n    label: {\n      show: true,\n      position: 'end',\n      distance: 5\n    },\n    lineStyle: {\n      type: 'dashed'\n    },\n    emphasis: {\n      label: {\n        show: true\n      },\n      lineStyle: {\n        width: 3\n      }\n    },\n    animationEasing: 'linear'\n  };\n  return MarkLineModel;\n}(MarkerModel);\nexport default MarkLineModel;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as markerHelper from './markerHelper.js';\nimport LineDraw from '../../chart/helper/LineDraw.js';\nimport MarkerView from './MarkerView.js';\nimport { getStackedDimension } from '../../data/helper/dataStackHelper.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { getECData } from '../../util/innerStore.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isArray, retrieve, retrieve2, clone, extend, logError, merge, map, curry, filter, isNumber } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nvar inner = makeInner();\nvar markLineTransform = function (seriesModel, coordSys, mlModel, item) {\n  var data = seriesModel.getData();\n  var itemArray;\n  if (!isArray(item)) {\n    // Special type markLine like 'min', 'max', 'average', 'median'\n    var mlType = item.type;\n    if (mlType === 'min' || mlType === 'max' || mlType === 'average' || mlType === 'median'\n    // In case\n    // data: [{\n    //   yAxis: 10\n    // }]\n    || item.xAxis != null || item.yAxis != null) {\n      var valueAxis = void 0;\n      var value = void 0;\n      if (item.yAxis != null || item.xAxis != null) {\n        valueAxis = coordSys.getAxis(item.yAxis != null ? 'y' : 'x');\n        value = retrieve(item.yAxis, item.xAxis);\n      } else {\n        var axisInfo = markerHelper.getAxisInfo(item, data, coordSys, seriesModel);\n        valueAxis = axisInfo.valueAxis;\n        var valueDataDim = getStackedDimension(data, axisInfo.valueDataDim);\n        value = markerHelper.numCalculate(data, valueDataDim, mlType);\n      }\n      var valueIndex = valueAxis.dim === 'x' ? 0 : 1;\n      var baseIndex = 1 - valueIndex;\n      // Normized to 2d data with start and end point\n      var mlFrom = clone(item);\n      var mlTo = {\n        coord: []\n      };\n      mlFrom.type = null;\n      mlFrom.coord = [];\n      mlFrom.coord[baseIndex] = -Infinity;\n      mlTo.coord[baseIndex] = Infinity;\n      var precision = mlModel.get('precision');\n      if (precision >= 0 && isNumber(value)) {\n        value = +value.toFixed(Math.min(precision, 20));\n      }\n      mlFrom.coord[valueIndex] = mlTo.coord[valueIndex] = value;\n      itemArray = [mlFrom, mlTo, {\n        type: mlType,\n        valueIndex: item.valueIndex,\n        // Force to use the value of calculated value.\n        value: value\n      }];\n    } else {\n      // Invalid data\n      if (process.env.NODE_ENV !== 'production') {\n        logError('Invalid markLine data.');\n      }\n      itemArray = [];\n    }\n  } else {\n    itemArray = item;\n  }\n  var normalizedItem = [markerHelper.dataTransform(seriesModel, itemArray[0]), markerHelper.dataTransform(seriesModel, itemArray[1]), extend({}, itemArray[2])];\n  // Avoid line data type is extended by from(to) data type\n  normalizedItem[2].type = normalizedItem[2].type || null;\n  // Merge from option and to option into line option\n  merge(normalizedItem[2], normalizedItem[0]);\n  merge(normalizedItem[2], normalizedItem[1]);\n  return normalizedItem;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markLine has one dim\nfunction ifMarkLineHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  var dimName = coordSys.dimensions[dimIndex];\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]) && fromCoord[dimIndex] === toCoord[dimIndex] && coordSys.getAxis(dimName).containData(fromCoord[dimIndex]);\n}\nfunction markLineFilter(coordSys, item) {\n  if (coordSys.type === 'cartesian2d') {\n    var fromCoord = item[0].coord;\n    var toCoord = item[1].coord;\n    // In case\n    // {\n    //  markLine: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkLineHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkLineHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n  }\n  return markerHelper.dataFilter(coordSys, item[0]) && markerHelper.dataFilter(coordSys, item[1]);\n}\nfunction updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(data.getValues(data.dimensions, idx));\n    } else {\n      var dims = coordSys.dimensions;\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Expand line to the edge of grid if value on one axis is Inifnity\n    // In case\n    //  markLine: {\n    //    data: [{\n    //      yAxis: 2\n    //      // or\n    //      type: 'average'\n    //    }]\n    //  }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var dims = coordSys.dimensions;\n      if (isInfinity(data.get(dims[0], idx))) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[isFrom ? 0 : 1]);\n      } else if (isInfinity(data.get(dims[1], idx))) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[isFrom ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  data.setItemLayout(idx, point);\n}\nvar MarkLineView = /** @class */function (_super) {\n  __extends(MarkLineView, _super);\n  function MarkLineView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkLineView.type;\n    return _this;\n  }\n  MarkLineView.prototype.updateTransform = function (markLineModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mlModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markLine');\n      if (mlModel) {\n        var mlData_1 = mlModel.getData();\n        var fromData_1 = inner(mlModel).from;\n        var toData_1 = inner(mlModel).to;\n        // Update visual and layout of from symbol and to symbol\n        fromData_1.each(function (idx) {\n          updateSingleMarkerEndLayout(fromData_1, idx, true, seriesModel, api);\n          updateSingleMarkerEndLayout(toData_1, idx, false, seriesModel, api);\n        });\n        // Update layout of line\n        mlData_1.each(function (idx) {\n          mlData_1.setItemLayout(idx, [fromData_1.getItemLayout(idx), toData_1.getItemLayout(idx)]);\n        });\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkLineView.prototype.renderSeries = function (seriesModel, mlModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var lineDrawMap = this.markerGroupMap;\n    var lineDraw = lineDrawMap.get(seriesId) || lineDrawMap.set(seriesId, new LineDraw());\n    this.group.add(lineDraw.group);\n    var mlData = createList(coordSys, seriesModel, mlModel);\n    var fromData = mlData.from;\n    var toData = mlData.to;\n    var lineData = mlData.line;\n    inner(mlModel).from = fromData;\n    inner(mlModel).to = toData;\n    // Line data for tooltip and formatter\n    mlModel.setData(lineData);\n    // TODO\n    // Functionally, `symbolSize` & `symbolOffset` can also be 2D array now.\n    // But the related logic and type definition are not finished yet.\n    // Finish it if required\n    var symbolType = mlModel.get('symbol');\n    var symbolSize = mlModel.get('symbolSize');\n    var symbolRotate = mlModel.get('symbolRotate');\n    var symbolOffset = mlModel.get('symbolOffset');\n    // TODO: support callback function like markPoint\n    if (!isArray(symbolType)) {\n      symbolType = [symbolType, symbolType];\n    }\n    if (!isArray(symbolSize)) {\n      symbolSize = [symbolSize, symbolSize];\n    }\n    if (!isArray(symbolRotate)) {\n      symbolRotate = [symbolRotate, symbolRotate];\n    }\n    if (!isArray(symbolOffset)) {\n      symbolOffset = [symbolOffset, symbolOffset];\n    }\n    // Update visual and layout of from symbol and to symbol\n    mlData.from.each(function (idx) {\n      updateDataVisualAndLayout(fromData, idx, true);\n      updateDataVisualAndLayout(toData, idx, false);\n    });\n    // Update visual and layout of line\n    lineData.each(function (idx) {\n      var lineStyle = lineData.getItemModel(idx).getModel('lineStyle').getLineStyle();\n      // lineData.setItemVisual(idx, {\n      //     color: lineColor || fromData.getItemVisual(idx, 'color')\n      // });\n      lineData.setItemLayout(idx, [fromData.getItemLayout(idx), toData.getItemLayout(idx)]);\n      if (lineStyle.stroke == null) {\n        lineStyle.stroke = fromData.getItemVisual(idx, 'style').fill;\n      }\n      lineData.setItemVisual(idx, {\n        fromSymbolKeepAspect: fromData.getItemVisual(idx, 'symbolKeepAspect'),\n        fromSymbolOffset: fromData.getItemVisual(idx, 'symbolOffset'),\n        fromSymbolRotate: fromData.getItemVisual(idx, 'symbolRotate'),\n        fromSymbolSize: fromData.getItemVisual(idx, 'symbolSize'),\n        fromSymbol: fromData.getItemVisual(idx, 'symbol'),\n        toSymbolKeepAspect: toData.getItemVisual(idx, 'symbolKeepAspect'),\n        toSymbolOffset: toData.getItemVisual(idx, 'symbolOffset'),\n        toSymbolRotate: toData.getItemVisual(idx, 'symbolRotate'),\n        toSymbolSize: toData.getItemVisual(idx, 'symbolSize'),\n        toSymbol: toData.getItemVisual(idx, 'symbol'),\n        style: lineStyle\n      });\n    });\n    lineDraw.updateData(lineData);\n    // Set host model for tooltip\n    // FIXME\n    mlData.line.eachItemGraphicEl(function (el) {\n      getECData(el).dataModel = mlModel;\n      el.traverse(function (child) {\n        getECData(child).dataModel = mlModel;\n      });\n    });\n    function updateDataVisualAndLayout(data, idx, isFrom) {\n      var itemModel = data.getItemModel(idx);\n      updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api);\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      if (style.fill == null) {\n        style.fill = getVisualFromData(seriesData, 'color');\n      }\n      data.setItemVisual(idx, {\n        symbolKeepAspect: itemModel.get('symbolKeepAspect'),\n        // `0` should be considered as a valid value, so use `retrieve2` instead of `||`\n        symbolOffset: retrieve2(itemModel.get('symbolOffset', true), symbolOffset[isFrom ? 0 : 1]),\n        symbolRotate: retrieve2(itemModel.get('symbolRotate', true), symbolRotate[isFrom ? 0 : 1]),\n        // TODO: when 2d array is supported, it should ignore parent\n        symbolSize: retrieve2(itemModel.get('symbolSize'), symbolSize[isFrom ? 0 : 1]),\n        symbol: retrieve2(itemModel.get('symbol', true), symbolType[isFrom ? 0 : 1]),\n        style: style\n      });\n    }\n    this.markKeep(lineDraw);\n    lineDraw.group.silent = mlModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkLineView.type = 'markLine';\n  return MarkLineView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, mlModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var fromData = new SeriesData(coordDimsInfos, mlModel);\n  var toData = new SeriesData(coordDimsInfos, mlModel);\n  // No dimensions\n  var lineData = new SeriesData([], mlModel);\n  var optData = map(mlModel.get('data'), curry(markLineTransform, seriesModel, coordSys, mlModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markLineFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  fromData.initData(map(optData, function (item) {\n    return item[0];\n  }), null, dimValueGetter);\n  toData.initData(map(optData, function (item) {\n    return item[1];\n  }), null, dimValueGetter);\n  lineData.initData(map(optData, function (item) {\n    return item[2];\n  }));\n  lineData.hasItemOption = true;\n  return {\n    from: fromData,\n    to: toData,\n    line: lineData\n  };\n}\nexport default MarkLineView;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkLineModel from './MarkLineModel.js';\nimport MarkLineView from './MarkLineView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkLineModel);\n  registers.registerComponentView(MarkLineView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markLine')) {\n      // Make sure markLine component is enabled\n      opt.markLine = opt.markLine || {};\n    }\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUA,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACvB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,eAAc;AAC3B,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,8BAA8B,SAAU,WAAW,mBAAmB,SAAS;AACrG,aAAO,IAAIA,eAAc,WAAW,mBAAmB,OAAO;AAAA,IAChE;AACA,IAAAA,eAAc,OAAO;AACrB,IAAAA,eAAc,gBAAgB;AAAA;AAAA,MAE5B,GAAG;AAAA,MACH,QAAQ,CAAC,UAAU,OAAO;AAAA,MAC1B,YAAY,CAAC,GAAG,EAAE;AAAA;AAAA,MAElB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,WAAW;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,UAAU;AAAA,QACR,OAAO;AAAA,UACL,MAAM;AAAA,QACR;AAAA,QACA,WAAW;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,IACnB;AACA,WAAOA;AAAA,EACT,EAAE,mBAAW;AAAA;AACb,IAAO,wBAAQ;;;AC/Bf,IAAI,QAAQ,UAAU;AACtB,IAAI,oBAAoB,SAAU,aAAa,UAAU,SAAS,MAAM;AACtE,MAAI,OAAO,YAAY,QAAQ;AAC/B,MAAI;AACJ,MAAI,CAAC,QAAQ,IAAI,GAAG;AAElB,QAAI,SAAS,KAAK;AAClB,QAAI,WAAW,SAAS,WAAW,SAAS,WAAW,aAAa,WAAW,YAK5E,KAAK,SAAS,QAAQ,KAAK,SAAS,MAAM;AAC3C,UAAI,YAAY;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,SAAS,QAAQ,KAAK,SAAS,MAAM;AAC5C,oBAAY,SAAS,QAAQ,KAAK,SAAS,OAAO,MAAM,GAAG;AAC3D,gBAAQ,SAAS,KAAK,OAAO,KAAK,KAAK;AAAA,MACzC,OAAO;AACL,YAAI,WAAwB,YAAY,MAAM,MAAM,UAAU,WAAW;AACzE,oBAAY,SAAS;AACrB,YAAI,eAAe,oBAAoB,MAAM,SAAS,YAAY;AAClE,gBAAqB,aAAa,MAAM,cAAc,MAAM;AAAA,MAC9D;AACA,UAAI,aAAa,UAAU,QAAQ,MAAM,IAAI;AAC7C,UAAI,YAAY,IAAI;AAEpB,UAAI,SAAS,MAAM,IAAI;AACvB,UAAI,OAAO;AAAA,QACT,OAAO,CAAC;AAAA,MACV;AACA,aAAO,OAAO;AACd,aAAO,QAAQ,CAAC;AAChB,aAAO,MAAM,SAAS,IAAI;AAC1B,WAAK,MAAM,SAAS,IAAI;AACxB,UAAI,YAAY,QAAQ,IAAI,WAAW;AACvC,UAAI,aAAa,KAAK,SAAS,KAAK,GAAG;AACrC,gBAAQ,CAAC,MAAM,QAAQ,KAAK,IAAI,WAAW,EAAE,CAAC;AAAA,MAChD;AACA,aAAO,MAAM,UAAU,IAAI,KAAK,MAAM,UAAU,IAAI;AACpD,kBAAY,CAAC,QAAQ,MAAM;AAAA,QACzB,MAAM;AAAA,QACN,YAAY,KAAK;AAAA;AAAA,QAEjB;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AAEL,UAAI,MAAuC;AACzC,iBAAS,wBAAwB;AAAA,MACnC;AACA,kBAAY,CAAC;AAAA,IACf;AAAA,EACF,OAAO;AACL,gBAAY;AAAA,EACd;AACA,MAAI,iBAAiB,CAAc,cAAc,aAAa,UAAU,CAAC,CAAC,GAAgB,cAAc,aAAa,UAAU,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;AAE5J,iBAAe,CAAC,EAAE,OAAO,eAAe,CAAC,EAAE,QAAQ;AAEnD,QAAM,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AAC1C,QAAM,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AAC1C,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,GAAG;AACrC;AAEA,SAAS,qBAAqB,UAAU,WAAW,SAAS,UAAU;AACpE,MAAI,gBAAgB,IAAI;AACxB,MAAI,UAAU,SAAS,WAAW,QAAQ;AAC1C,SAAO,WAAW,UAAU,aAAa,CAAC,KAAK,WAAW,QAAQ,aAAa,CAAC,KAAK,UAAU,QAAQ,MAAM,QAAQ,QAAQ,KAAK,SAAS,QAAQ,OAAO,EAAE,YAAY,UAAU,QAAQ,CAAC;AAC7L;AACA,SAAS,eAAe,UAAU,MAAM;AACtC,MAAI,SAAS,SAAS,eAAe;AACnC,QAAI,YAAY,KAAK,CAAC,EAAE;AACxB,QAAI,UAAU,KAAK,CAAC,EAAE;AAOtB,QAAI,aAAa,YAAY,qBAAqB,GAAG,WAAW,SAAS,QAAQ,KAAK,qBAAqB,GAAG,WAAW,SAAS,QAAQ,IAAI;AAC5I,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAoB,WAAW,UAAU,KAAK,CAAC,CAAC,KAAkB,WAAW,UAAU,KAAK,CAAC,CAAC;AAChG;AACA,SAAS,4BAA4B,MAAM,KAAK,QAAQ,aAAa,KAAK;AACxE,MAAI,WAAW,YAAY;AAC3B,MAAI,YAAY,KAAK,aAAa,GAAG;AACrC,MAAI;AACJ,MAAI,MAAiB,aAAa,UAAU,IAAI,GAAG,GAAG,IAAI,SAAS,CAAC;AACpE,MAAI,MAAiB,aAAa,UAAU,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC;AACrE,MAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,YAAQ,CAAC,KAAK,GAAG;AAAA,EACnB,OAAO;AAEL,QAAI,YAAY,mBAAmB;AAEjC,cAAQ,YAAY,kBAAkB,KAAK,UAAU,KAAK,YAAY,GAAG,CAAC;AAAA,IAC5E,OAAO;AACL,UAAI,OAAO,SAAS;AACpB,UAAI,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;AAC7B,UAAI,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;AAC7B,cAAQ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC;AAAA,IACrC;AAUA,QAAI,uBAAuB,UAAU,aAAa,GAAG;AAEnD,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAChC,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAChC,UAAI,OAAO,SAAS;AACpB,UAAI,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG;AACtC,cAAM,CAAC,IAAI,MAAM,cAAc,MAAM,UAAU,EAAE,SAAS,IAAI,CAAC,CAAC;AAAA,MAClE,WAAW,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG;AAC7C,cAAM,CAAC,IAAI,MAAM,cAAc,MAAM,UAAU,EAAE,SAAS,IAAI,CAAC,CAAC;AAAA,MAClE;AAAA,IACF;AAEA,QAAI,CAAC,MAAM,GAAG,GAAG;AACf,YAAM,CAAC,IAAI;AAAA,IACb;AACA,QAAI,CAAC,MAAM,GAAG,GAAG;AACf,YAAM,CAAC,IAAI;AAAA,IACb;AAAA,EACF;AACA,OAAK,cAAc,KAAK,KAAK;AAC/B;AACA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACtB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,cAAa;AAC1B,aAAO;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,kBAAkB,SAAU,eAAe,SAAS,KAAK;AAC9E,cAAQ,WAAW,SAAU,aAAa;AACxC,YAAI,UAAU,oBAAY,yBAAyB,aAAa,UAAU;AAC1E,YAAI,SAAS;AACX,cAAI,WAAW,QAAQ,QAAQ;AAC/B,cAAI,aAAa,MAAM,OAAO,EAAE;AAChC,cAAI,WAAW,MAAM,OAAO,EAAE;AAE9B,qBAAW,KAAK,SAAU,KAAK;AAC7B,wCAA4B,YAAY,KAAK,MAAM,aAAa,GAAG;AACnE,wCAA4B,UAAU,KAAK,OAAO,aAAa,GAAG;AAAA,UACpE,CAAC;AAED,mBAAS,KAAK,SAAU,KAAK;AAC3B,qBAAS,cAAc,KAAK,CAAC,WAAW,cAAc,GAAG,GAAG,SAAS,cAAc,GAAG,CAAC,CAAC;AAAA,UAC1F,CAAC;AACD,eAAK,eAAe,IAAI,YAAY,EAAE,EAAE,aAAa;AAAA,QACvD;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,eAAe,SAAU,aAAa,SAAS,SAAS,KAAK;AAClF,UAAI,WAAW,YAAY;AAC3B,UAAI,WAAW,YAAY;AAC3B,UAAI,aAAa,YAAY,QAAQ;AACrC,UAAI,cAAc,KAAK;AACvB,UAAI,WAAW,YAAY,IAAI,QAAQ,KAAK,YAAY,IAAI,UAAU,IAAI,iBAAS,CAAC;AACpF,WAAK,MAAM,IAAI,SAAS,KAAK;AAC7B,UAAI,SAAS,WAAW,UAAU,aAAa,OAAO;AACtD,UAAI,WAAW,OAAO;AACtB,UAAI,SAAS,OAAO;AACpB,UAAI,WAAW,OAAO;AACtB,YAAM,OAAO,EAAE,OAAO;AACtB,YAAM,OAAO,EAAE,KAAK;AAEpB,cAAQ,QAAQ,QAAQ;AAKxB,UAAI,aAAa,QAAQ,IAAI,QAAQ;AACrC,UAAI,aAAa,QAAQ,IAAI,YAAY;AACzC,UAAI,eAAe,QAAQ,IAAI,cAAc;AAC7C,UAAI,eAAe,QAAQ,IAAI,cAAc;AAE7C,UAAI,CAAC,QAAQ,UAAU,GAAG;AACxB,qBAAa,CAAC,YAAY,UAAU;AAAA,MACtC;AACA,UAAI,CAAC,QAAQ,UAAU,GAAG;AACxB,qBAAa,CAAC,YAAY,UAAU;AAAA,MACtC;AACA,UAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,uBAAe,CAAC,cAAc,YAAY;AAAA,MAC5C;AACA,UAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,uBAAe,CAAC,cAAc,YAAY;AAAA,MAC5C;AAEA,aAAO,KAAK,KAAK,SAAU,KAAK;AAC9B,kCAA0B,UAAU,KAAK,IAAI;AAC7C,kCAA0B,QAAQ,KAAK,KAAK;AAAA,MAC9C,CAAC;AAED,eAAS,KAAK,SAAU,KAAK;AAC3B,YAAI,YAAY,SAAS,aAAa,GAAG,EAAE,SAAS,WAAW,EAAE,aAAa;AAI9E,iBAAS,cAAc,KAAK,CAAC,SAAS,cAAc,GAAG,GAAG,OAAO,cAAc,GAAG,CAAC,CAAC;AACpF,YAAI,UAAU,UAAU,MAAM;AAC5B,oBAAU,SAAS,SAAS,cAAc,KAAK,OAAO,EAAE;AAAA,QAC1D;AACA,iBAAS,cAAc,KAAK;AAAA,UAC1B,sBAAsB,SAAS,cAAc,KAAK,kBAAkB;AAAA,UACpE,kBAAkB,SAAS,cAAc,KAAK,cAAc;AAAA,UAC5D,kBAAkB,SAAS,cAAc,KAAK,cAAc;AAAA,UAC5D,gBAAgB,SAAS,cAAc,KAAK,YAAY;AAAA,UACxD,YAAY,SAAS,cAAc,KAAK,QAAQ;AAAA,UAChD,oBAAoB,OAAO,cAAc,KAAK,kBAAkB;AAAA,UAChE,gBAAgB,OAAO,cAAc,KAAK,cAAc;AAAA,UACxD,gBAAgB,OAAO,cAAc,KAAK,cAAc;AAAA,UACxD,cAAc,OAAO,cAAc,KAAK,YAAY;AAAA,UACpD,UAAU,OAAO,cAAc,KAAK,QAAQ;AAAA,UAC5C,OAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AACD,eAAS,WAAW,QAAQ;AAG5B,aAAO,KAAK,kBAAkB,SAAU,IAAI;AAC1C,kBAAU,EAAE,EAAE,YAAY;AAC1B,WAAG,SAAS,SAAU,OAAO;AAC3B,oBAAU,KAAK,EAAE,YAAY;AAAA,QAC/B,CAAC;AAAA,MACH,CAAC;AACD,eAAS,0BAA0B,MAAM,KAAK,QAAQ;AACpD,YAAI,YAAY,KAAK,aAAa,GAAG;AACrC,oCAA4B,MAAM,KAAK,QAAQ,aAAa,GAAG;AAC/D,YAAI,QAAQ,UAAU,SAAS,WAAW,EAAE,aAAa;AACzD,YAAI,MAAM,QAAQ,MAAM;AACtB,gBAAM,OAAO,kBAAkB,YAAY,OAAO;AAAA,QACpD;AACA,aAAK,cAAc,KAAK;AAAA,UACtB,kBAAkB,UAAU,IAAI,kBAAkB;AAAA;AAAA,UAElD,cAAc,UAAU,UAAU,IAAI,gBAAgB,IAAI,GAAG,aAAa,SAAS,IAAI,CAAC,CAAC;AAAA,UACzF,cAAc,UAAU,UAAU,IAAI,gBAAgB,IAAI,GAAG,aAAa,SAAS,IAAI,CAAC,CAAC;AAAA;AAAA,UAEzF,YAAY,UAAU,UAAU,IAAI,YAAY,GAAG,WAAW,SAAS,IAAI,CAAC,CAAC;AAAA,UAC7E,QAAQ,UAAU,UAAU,IAAI,UAAU,IAAI,GAAG,WAAW,SAAS,IAAI,CAAC,CAAC;AAAA,UAC3E;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,SAAS,QAAQ;AACtB,eAAS,MAAM,SAAS,QAAQ,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ;AAAA,IAC3E;AACA,IAAAA,cAAa,OAAO;AACpB,WAAOA;AAAA,EACT,EAAE,kBAAU;AAAA;AACZ,SAAS,WAAW,UAAU,aAAa,SAAS;AAClD,MAAI;AACJ,MAAI,UAAU;AACZ,qBAAiB,IAAI,YAAY,SAAS,YAAY,SAAU,UAAU;AACxE,UAAI,OAAO,YAAY,QAAQ,EAAE,iBAAiB,YAAY,QAAQ,EAAE,aAAa,QAAQ,CAAC,KAAK,CAAC;AAEpG,aAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,QAC9B,MAAM;AAAA;AAAA,QAEN,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,OAAO;AACL,qBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,MAAI,WAAW,IAAI,mBAAW,gBAAgB,OAAO;AACrD,MAAI,SAAS,IAAI,mBAAW,gBAAgB,OAAO;AAEnD,MAAI,WAAW,IAAI,mBAAW,CAAC,GAAG,OAAO;AACzC,MAAI,UAAU,IAAI,QAAQ,IAAI,MAAM,GAAG,MAAM,mBAAmB,aAAa,UAAU,OAAO,CAAC;AAC/F,MAAI,UAAU;AACZ,cAAU,OAAO,SAAS,MAAM,gBAAgB,QAAQ,CAAC;AAAA,EAC3D;AACA,MAAI,iBAA8B,2BAA2B,CAAC,CAAC,UAAU,cAAc;AACvF,WAAS,SAAS,IAAI,SAAS,SAAU,MAAM;AAC7C,WAAO,KAAK,CAAC;AAAA,EACf,CAAC,GAAG,MAAM,cAAc;AACxB,SAAO,SAAS,IAAI,SAAS,SAAU,MAAM;AAC3C,WAAO,KAAK,CAAC;AAAA,EACf,CAAC,GAAG,MAAM,cAAc;AACxB,WAAS,SAAS,IAAI,SAAS,SAAU,MAAM;AAC7C,WAAO,KAAK,CAAC;AAAA,EACf,CAAC,CAAC;AACF,WAAS,gBAAgB;AACzB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM;AAAA,EACR;AACF;AACA,IAAO,uBAAQ;;;AC9UR,SAAS,QAAQ,WAAW;AACjC,YAAU,uBAAuB,qBAAa;AAC9C,YAAU,sBAAsB,oBAAY;AAC5C,YAAU,qBAAqB,SAAU,KAAK;AAC5C,QAAI,oBAAoB,IAAI,QAAQ,UAAU,GAAG;AAE/C,UAAI,WAAW,IAAI,YAAY,CAAC;AAAA,IAClC;AAAA,EACF,CAAC;AACH;", "names": ["MarkLineModel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}