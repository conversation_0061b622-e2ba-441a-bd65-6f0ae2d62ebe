{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/labelHelper.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/Symbol.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/SymbolDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { isArray } from 'zrender/lib/core/util.js';\n/**\n * @return label string. Not null/undefined\n */\nexport function getDefaultLabel(data, dataIndex) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  var len = labelDims.length;\n  // Simple optimization (in lots of cases, label dims length is 1)\n  if (len === 1) {\n    var rawVal = retrieveRawValue(data, dataIndex, labelDims[0]);\n    return rawVal != null ? rawVal + '' : null;\n  } else if (len) {\n    var vals = [];\n    for (var i = 0; i < labelDims.length; i++) {\n      vals.push(retrieveRawValue(data, dataIndex, labelDims[i]));\n    }\n    return vals.join(' ');\n  }\n}\nexport function getDefaultInterpolatedLabel(data, interpolatedValue) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  if (!isArray(interpolatedValue)) {\n    return interpolatedValue + '';\n  }\n  var vals = [];\n  for (var i = 0; i < labelDims.length; i++) {\n    var dimIndex = data.getDimensionIndex(labelDims[i]);\n    if (dimIndex >= 0) {\n      vals.push(interpolatedValue[dimIndex]);\n    }\n  }\n  return vals.join(' ');\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { enterEmphasis, leaveEmphasis, toggleHoverEmphasis } from '../../util/states.js';\nimport { getDefaultLabel } from './labelHelper.js';\nimport { extend } from 'zrender/lib/core/util.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar Symbol = /** @class */function (_super) {\n  __extends(Symbol, _super);\n  function Symbol(data, idx, seriesScope, opts) {\n    var _this = _super.call(this) || this;\n    _this.updateData(data, idx, seriesScope, opts);\n    return _this;\n  }\n  Symbol.prototype._createSymbol = function (symbolType, data, idx, symbolSize, keepAspect) {\n    // Remove paths created before\n    this.removeAll();\n    // let symbolPath = createSymbol(\n    //     symbolType, -0.5, -0.5, 1, 1, color\n    // );\n    // If width/height are set too small (e.g., set to 1) on ios10\n    // and macOS Sierra, a circle stroke become a rect, no matter what\n    // the scale is set. So we set width/height as 2. See #4150.\n    var symbolPath = createSymbol(symbolType, -1, -1, 2, 2, null, keepAspect);\n    symbolPath.attr({\n      z2: 100,\n      culling: true,\n      scaleX: symbolSize[0] / 2,\n      scaleY: symbolSize[1] / 2\n    });\n    // Rewrite drift method\n    symbolPath.drift = driftSymbol;\n    this._symbolType = symbolType;\n    this.add(symbolPath);\n  };\n  /**\n   * Stop animation\n   * @param {boolean} toLastFrame\n   */\n  Symbol.prototype.stopSymbolAnimation = function (toLastFrame) {\n    this.childAt(0).stopAnimation(null, toLastFrame);\n  };\n  Symbol.prototype.getSymbolType = function () {\n    return this._symbolType;\n  };\n  /**\n   * FIXME:\n   * Caution: This method breaks the encapsulation of this module,\n   * but it indeed brings convenience. So do not use the method\n   * unless you detailedly know all the implements of `Symbol`,\n   * especially animation.\n   *\n   * Get symbol path element.\n   */\n  Symbol.prototype.getSymbolPath = function () {\n    return this.childAt(0);\n  };\n  /**\n   * Highlight symbol\n   */\n  Symbol.prototype.highlight = function () {\n    enterEmphasis(this.childAt(0));\n  };\n  /**\n   * Downplay symbol\n   */\n  Symbol.prototype.downplay = function () {\n    leaveEmphasis(this.childAt(0));\n  };\n  /**\n   * @param {number} zlevel\n   * @param {number} z\n   */\n  Symbol.prototype.setZ = function (zlevel, z) {\n    var symbolPath = this.childAt(0);\n    symbolPath.zlevel = zlevel;\n    symbolPath.z = z;\n  };\n  Symbol.prototype.setDraggable = function (draggable, hasCursorOption) {\n    var symbolPath = this.childAt(0);\n    symbolPath.draggable = draggable;\n    symbolPath.cursor = !hasCursorOption && draggable ? 'move' : symbolPath.cursor;\n  };\n  /**\n   * Update symbol properties\n   */\n  Symbol.prototype.updateData = function (data, idx, seriesScope, opts) {\n    this.silent = false;\n    var symbolType = data.getItemVisual(idx, 'symbol') || 'circle';\n    var seriesModel = data.hostModel;\n    var symbolSize = Symbol.getSymbolSize(data, idx);\n    var isInit = symbolType !== this._symbolType;\n    var disableAnimation = opts && opts.disableAnimation;\n    if (isInit) {\n      var keepAspect = data.getItemVisual(idx, 'symbolKeepAspect');\n      this._createSymbol(symbolType, data, idx, symbolSize, keepAspect);\n    } else {\n      var symbolPath = this.childAt(0);\n      symbolPath.silent = false;\n      var target = {\n        scaleX: symbolSize[0] / 2,\n        scaleY: symbolSize[1] / 2\n      };\n      disableAnimation ? symbolPath.attr(target) : graphic.updateProps(symbolPath, target, seriesModel, idx);\n      saveOldStyle(symbolPath);\n    }\n    this._updateCommon(data, idx, symbolSize, seriesScope, opts);\n    if (isInit) {\n      var symbolPath = this.childAt(0);\n      if (!disableAnimation) {\n        var target = {\n          scaleX: this._sizeX,\n          scaleY: this._sizeY,\n          style: {\n            // Always fadeIn. Because it has fadeOut animation when symbol is removed..\n            opacity: symbolPath.style.opacity\n          }\n        };\n        symbolPath.scaleX = symbolPath.scaleY = 0;\n        symbolPath.style.opacity = 0;\n        graphic.initProps(symbolPath, target, seriesModel, idx);\n      }\n    }\n    if (disableAnimation) {\n      // Must stop leave transition manually if don't call initProps or updateProps.\n      this.childAt(0).stopAnimation('leave');\n    }\n  };\n  Symbol.prototype._updateCommon = function (data, idx, symbolSize, seriesScope, opts) {\n    var symbolPath = this.childAt(0);\n    var seriesModel = data.hostModel;\n    var emphasisItemStyle;\n    var blurItemStyle;\n    var selectItemStyle;\n    var focus;\n    var blurScope;\n    var emphasisDisabled;\n    var labelStatesModels;\n    var hoverScale;\n    var cursorStyle;\n    if (seriesScope) {\n      emphasisItemStyle = seriesScope.emphasisItemStyle;\n      blurItemStyle = seriesScope.blurItemStyle;\n      selectItemStyle = seriesScope.selectItemStyle;\n      focus = seriesScope.focus;\n      blurScope = seriesScope.blurScope;\n      labelStatesModels = seriesScope.labelStatesModels;\n      hoverScale = seriesScope.hoverScale;\n      cursorStyle = seriesScope.cursorStyle;\n      emphasisDisabled = seriesScope.emphasisDisabled;\n    }\n    if (!seriesScope || data.hasItemOption) {\n      var itemModel = seriesScope && seriesScope.itemModel ? seriesScope.itemModel : data.getItemModel(idx);\n      var emphasisModel = itemModel.getModel('emphasis');\n      emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n      selectItemStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n      blurItemStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n      focus = emphasisModel.get('focus');\n      blurScope = emphasisModel.get('blurScope');\n      emphasisDisabled = emphasisModel.get('disabled');\n      labelStatesModels = getLabelStatesModels(itemModel);\n      hoverScale = emphasisModel.getShallow('scale');\n      cursorStyle = itemModel.getShallow('cursor');\n    }\n    var symbolRotate = data.getItemVisual(idx, 'symbolRotate');\n    symbolPath.attr('rotation', (symbolRotate || 0) * Math.PI / 180 || 0);\n    var symbolOffset = normalizeSymbolOffset(data.getItemVisual(idx, 'symbolOffset'), symbolSize);\n    if (symbolOffset) {\n      symbolPath.x = symbolOffset[0];\n      symbolPath.y = symbolOffset[1];\n    }\n    cursorStyle && symbolPath.attr('cursor', cursorStyle);\n    var symbolStyle = data.getItemVisual(idx, 'style');\n    var visualColor = symbolStyle.fill;\n    if (symbolPath instanceof ZRImage) {\n      var pathStyle = symbolPath.style;\n      symbolPath.useStyle(extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolStyle));\n    } else {\n      if (symbolPath.__isEmptyBrush) {\n        // fill and stroke will be swapped if it's empty.\n        // So we cloned a new style to avoid it affecting the original style in visual storage.\n        // TODO Better implementation. No empty logic!\n        symbolPath.useStyle(extend({}, symbolStyle));\n      } else {\n        symbolPath.useStyle(symbolStyle);\n      }\n      // Disable decal because symbol scale will been applied on the decal.\n      symbolPath.style.decal = null;\n      symbolPath.setColor(visualColor, opts && opts.symbolInnerColor);\n      symbolPath.style.strokeNoScale = true;\n    }\n    var liftZ = data.getItemVisual(idx, 'liftZ');\n    var z2Origin = this._z2;\n    if (liftZ != null) {\n      if (z2Origin == null) {\n        this._z2 = symbolPath.z2;\n        symbolPath.z2 += liftZ;\n      }\n    } else if (z2Origin != null) {\n      symbolPath.z2 = z2Origin;\n      this._z2 = null;\n    }\n    var useNameLabel = opts && opts.useNameLabel;\n    setLabelStyle(symbolPath, labelStatesModels, {\n      labelFetcher: seriesModel,\n      labelDataIndex: idx,\n      defaultText: getLabelDefaultText,\n      inheritColor: visualColor,\n      defaultOpacity: symbolStyle.opacity\n    });\n    // Do not execute util needed.\n    function getLabelDefaultText(idx) {\n      return useNameLabel ? data.getName(idx) : getDefaultLabel(data, idx);\n    }\n    this._sizeX = symbolSize[0] / 2;\n    this._sizeY = symbolSize[1] / 2;\n    var emphasisState = symbolPath.ensureState('emphasis');\n    emphasisState.style = emphasisItemStyle;\n    symbolPath.ensureState('select').style = selectItemStyle;\n    symbolPath.ensureState('blur').style = blurItemStyle;\n    // null / undefined / true means to use default strategy.\n    // 0 / false / negative number / NaN / Infinity means no scale.\n    var scaleRatio = hoverScale == null || hoverScale === true ? Math.max(1.1, 3 / this._sizeY)\n    // PENDING: restrict hoverScale > 1? It seems unreasonable to scale down\n    : isFinite(hoverScale) && hoverScale > 0 ? +hoverScale : 1;\n    // always set scale to allow resetting\n    emphasisState.scaleX = this._sizeX * scaleRatio;\n    emphasisState.scaleY = this._sizeY * scaleRatio;\n    this.setSymbolScale(1);\n    toggleHoverEmphasis(this, focus, blurScope, emphasisDisabled);\n  };\n  Symbol.prototype.setSymbolScale = function (scale) {\n    this.scaleX = this.scaleY = scale;\n  };\n  Symbol.prototype.fadeOut = function (cb, seriesModel, opt) {\n    var symbolPath = this.childAt(0);\n    var dataIndex = getECData(this).dataIndex;\n    var animationOpt = opt && opt.animation;\n    // Avoid mistaken hover when fading out\n    this.silent = symbolPath.silent = true;\n    // Not show text when animating\n    if (opt && opt.fadeLabel) {\n      var textContent = symbolPath.getTextContent();\n      if (textContent) {\n        graphic.removeElement(textContent, {\n          style: {\n            opacity: 0\n          }\n        }, seriesModel, {\n          dataIndex: dataIndex,\n          removeOpt: animationOpt,\n          cb: function () {\n            symbolPath.removeTextContent();\n          }\n        });\n      }\n    } else {\n      symbolPath.removeTextContent();\n    }\n    graphic.removeElement(symbolPath, {\n      style: {\n        opacity: 0\n      },\n      scaleX: 0,\n      scaleY: 0\n    }, seriesModel, {\n      dataIndex: dataIndex,\n      cb: cb,\n      removeOpt: animationOpt\n    });\n  };\n  Symbol.getSymbolSize = function (data, idx) {\n    return normalizeSymbolSize(data.getItemVisual(idx, 'symbolSize'));\n  };\n  return Symbol;\n}(graphic.Group);\nfunction driftSymbol(dx, dy) {\n  this.parent.drift(dx, dy);\n}\nexport default Symbol;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as graphic from '../../util/graphic.js';\nimport SymbolClz from './Symbol.js';\nimport { isObject } from 'zrender/lib/core/util.js';\nimport { getLabelStatesModels } from '../../label/labelStyle.js';\nfunction symbolNeedsDraw(data, point, idx, opt) {\n  return point && !isNaN(point[0]) && !isNaN(point[1]) && !(opt.isIgnore && opt.isIgnore(idx))\n  // We do not set clipShape on group, because it will cut part of\n  // the symbol element shape. We use the same clip shape here as\n  // the line clip.\n  && !(opt.clipShape && !opt.clipShape.contain(point[0], point[1])) && data.getItemVisual(idx, 'symbol') !== 'none';\n}\nfunction normalizeUpdateOpt(opt) {\n  if (opt != null && !isObject(opt)) {\n    opt = {\n      isIgnore: opt\n    };\n  }\n  return opt || {};\n}\nfunction makeSeriesScope(data) {\n  var seriesModel = data.hostModel;\n  var emphasisModel = seriesModel.getModel('emphasis');\n  return {\n    emphasisItemStyle: emphasisModel.getModel('itemStyle').getItemStyle(),\n    blurItemStyle: seriesModel.getModel(['blur', 'itemStyle']).getItemStyle(),\n    selectItemStyle: seriesModel.getModel(['select', 'itemStyle']).getItemStyle(),\n    focus: emphasisModel.get('focus'),\n    blurScope: emphasisModel.get('blurScope'),\n    emphasisDisabled: emphasisModel.get('disabled'),\n    hoverScale: emphasisModel.get('scale'),\n    labelStatesModels: getLabelStatesModels(seriesModel),\n    cursorStyle: seriesModel.get('cursor')\n  };\n}\nvar SymbolDraw = /** @class */function () {\n  function SymbolDraw(SymbolCtor) {\n    this.group = new graphic.Group();\n    this._SymbolCtor = SymbolCtor || SymbolClz;\n  }\n  /**\n   * Update symbols draw by new data\n   */\n  SymbolDraw.prototype.updateData = function (data, opt) {\n    // Remove progressive els.\n    this._progressiveEls = null;\n    opt = normalizeUpdateOpt(opt);\n    var group = this.group;\n    var seriesModel = data.hostModel;\n    var oldData = this._data;\n    var SymbolCtor = this._SymbolCtor;\n    var disableAnimation = opt.disableAnimation;\n    var seriesScope = makeSeriesScope(data);\n    var symbolUpdateOpt = {\n      disableAnimation: disableAnimation\n    };\n    var getSymbolPoint = opt.getSymbolPoint || function (idx) {\n      return data.getItemLayout(idx);\n    };\n    // There is no oldLineData only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!oldData) {\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      var point = getSymbolPoint(newIdx);\n      if (symbolNeedsDraw(data, point, newIdx, opt)) {\n        var symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);\n        symbolEl.setPosition(point);\n        data.setItemGraphicEl(newIdx, symbolEl);\n        group.add(symbolEl);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      var point = getSymbolPoint(newIdx);\n      if (!symbolNeedsDraw(data, point, newIdx, opt)) {\n        group.remove(symbolEl);\n        return;\n      }\n      var newSymbolType = data.getItemVisual(newIdx, 'symbol') || 'circle';\n      var oldSymbolType = symbolEl && symbolEl.getSymbolType && symbolEl.getSymbolType();\n      if (!symbolEl\n      // Create a new if symbol type changed.\n      || oldSymbolType && oldSymbolType !== newSymbolType) {\n        group.remove(symbolEl);\n        symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);\n        symbolEl.setPosition(point);\n      } else {\n        symbolEl.updateData(data, newIdx, seriesScope, symbolUpdateOpt);\n        var target = {\n          x: point[0],\n          y: point[1]\n        };\n        disableAnimation ? symbolEl.attr(target) : graphic.updateProps(symbolEl, target, seriesModel);\n      }\n      // Add back\n      group.add(symbolEl);\n      data.setItemGraphicEl(newIdx, symbolEl);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && el.fadeOut(function () {\n        group.remove(el);\n      }, seriesModel);\n    }).execute();\n    this._getSymbolPoint = getSymbolPoint;\n    this._data = data;\n  };\n  ;\n  SymbolDraw.prototype.updateLayout = function () {\n    var _this = this;\n    var data = this._data;\n    if (data) {\n      // Not use animation\n      data.eachItemGraphicEl(function (el, idx) {\n        var point = _this._getSymbolPoint(idx);\n        el.setPosition(point);\n        el.markRedraw();\n      });\n    }\n  };\n  ;\n  SymbolDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this._seriesScope = makeSeriesScope(data);\n    this._data = null;\n    this.group.removeAll();\n  };\n  ;\n  /**\n   * Update symbols draw by new data\n   */\n  SymbolDraw.prototype.incrementalUpdate = function (taskParams, data, opt) {\n    // Clear\n    this._progressiveEls = [];\n    opt = normalizeUpdateOpt(opt);\n    function updateIncrementalAndHover(el) {\n      if (!el.isGroup) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = taskParams.start; idx < taskParams.end; idx++) {\n      var point = data.getItemLayout(idx);\n      if (symbolNeedsDraw(data, point, idx, opt)) {\n        var el = new this._SymbolCtor(data, idx, this._seriesScope);\n        el.traverse(updateIncrementalAndHover);\n        el.setPosition(point);\n        this.group.add(el);\n        data.setItemGraphicEl(idx, el);\n        this._progressiveEls.push(el);\n      }\n    }\n  };\n  ;\n  SymbolDraw.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  SymbolDraw.prototype.remove = function (enableAnimation) {\n    var group = this.group;\n    var data = this._data;\n    // Incremental model do not have this._data.\n    if (data && enableAnimation) {\n      data.eachItemGraphicEl(function (el) {\n        el.fadeOut(function () {\n          group.remove(el);\n        }, data.hostModel);\n      });\n    } else {\n      group.removeAll();\n    }\n  };\n  ;\n  return SymbolDraw;\n}();\nexport default SymbolDraw;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDO,SAAS,gBAAgB,MAAM,WAAW;AAC/C,MAAI,YAAY,KAAK,iBAAiB,gBAAgB;AACtD,MAAI,MAAM,UAAU;AAEpB,MAAI,QAAQ,GAAG;AACb,QAAI,SAAS,iBAAiB,MAAM,WAAW,UAAU,CAAC,CAAC;AAC3D,WAAO,UAAU,OAAO,SAAS,KAAK;AAAA,EACxC,WAAW,KAAK;AACd,QAAI,OAAO,CAAC;AACZ,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,WAAK,KAAK,iBAAiB,MAAM,WAAW,UAAU,CAAC,CAAC,CAAC;AAAA,IAC3D;AACA,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AACF;AACO,SAAS,4BAA4B,MAAM,mBAAmB;AACnE,MAAI,YAAY,KAAK,iBAAiB,gBAAgB;AACtD,MAAI,CAAC,QAAQ,iBAAiB,GAAG;AAC/B,WAAO,oBAAoB;AAAA,EAC7B;AACA,MAAI,OAAO,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,WAAW,KAAK,kBAAkB,UAAU,CAAC,CAAC;AAClD,QAAI,YAAY,GAAG;AACjB,WAAK,KAAK,kBAAkB,QAAQ,CAAC;AAAA,IACvC;AAAA,EACF;AACA,SAAO,KAAK,KAAK,GAAG;AACtB;;;ACvBA,IAAI;AAAA;AAAA,EAAsB,SAAU,QAAQ;AAC1C,cAAUA,SAAQ,MAAM;AACxB,aAASA,QAAO,MAAM,KAAK,aAAa,MAAM;AAC5C,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,WAAW,MAAM,KAAK,aAAa,IAAI;AAC7C,aAAO;AAAA,IACT;AACA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,YAAY,MAAM,KAAK,YAAY,YAAY;AAExF,WAAK,UAAU;AAOf,UAAI,aAAa,aAAa,YAAY,IAAI,IAAI,GAAG,GAAG,MAAM,UAAU;AACxE,iBAAW,KAAK;AAAA,QACd,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ,WAAW,CAAC,IAAI;AAAA,QACxB,QAAQ,WAAW,CAAC,IAAI;AAAA,MAC1B,CAAC;AAED,iBAAW,QAAQ;AACnB,WAAK,cAAc;AACnB,WAAK,IAAI,UAAU;AAAA,IACrB;AAKA,IAAAA,QAAO,UAAU,sBAAsB,SAAU,aAAa;AAC5D,WAAK,QAAQ,CAAC,EAAE,cAAc,MAAM,WAAW;AAAA,IACjD;AACA,IAAAA,QAAO,UAAU,gBAAgB,WAAY;AAC3C,aAAO,KAAK;AAAA,IACd;AAUA,IAAAA,QAAO,UAAU,gBAAgB,WAAY;AAC3C,aAAO,KAAK,QAAQ,CAAC;AAAA,IACvB;AAIA,IAAAA,QAAO,UAAU,YAAY,WAAY;AACvC,oBAAc,KAAK,QAAQ,CAAC,CAAC;AAAA,IAC/B;AAIA,IAAAA,QAAO,UAAU,WAAW,WAAY;AACtC,oBAAc,KAAK,QAAQ,CAAC,CAAC;AAAA,IAC/B;AAKA,IAAAA,QAAO,UAAU,OAAO,SAAU,QAAQ,GAAG;AAC3C,UAAI,aAAa,KAAK,QAAQ,CAAC;AAC/B,iBAAW,SAAS;AACpB,iBAAW,IAAI;AAAA,IACjB;AACA,IAAAA,QAAO,UAAU,eAAe,SAAU,WAAW,iBAAiB;AACpE,UAAI,aAAa,KAAK,QAAQ,CAAC;AAC/B,iBAAW,YAAY;AACvB,iBAAW,SAAS,CAAC,mBAAmB,YAAY,SAAS,WAAW;AAAA,IAC1E;AAIA,IAAAA,QAAO,UAAU,aAAa,SAAU,MAAM,KAAK,aAAa,MAAM;AACpE,WAAK,SAAS;AACd,UAAI,aAAa,KAAK,cAAc,KAAK,QAAQ,KAAK;AACtD,UAAI,cAAc,KAAK;AACvB,UAAI,aAAaA,QAAO,cAAc,MAAM,GAAG;AAC/C,UAAI,SAAS,eAAe,KAAK;AACjC,UAAI,mBAAmB,QAAQ,KAAK;AACpC,UAAI,QAAQ;AACV,YAAI,aAAa,KAAK,cAAc,KAAK,kBAAkB;AAC3D,aAAK,cAAc,YAAY,MAAM,KAAK,YAAY,UAAU;AAAA,MAClE,OAAO;AACL,YAAI,aAAa,KAAK,QAAQ,CAAC;AAC/B,mBAAW,SAAS;AACpB,YAAI,SAAS;AAAA,UACX,QAAQ,WAAW,CAAC,IAAI;AAAA,UACxB,QAAQ,WAAW,CAAC,IAAI;AAAA,QAC1B;AACA,2BAAmB,WAAW,KAAK,MAAM,IAAY,YAAY,YAAY,QAAQ,aAAa,GAAG;AACrG,qBAAa,UAAU;AAAA,MACzB;AACA,WAAK,cAAc,MAAM,KAAK,YAAY,aAAa,IAAI;AAC3D,UAAI,QAAQ;AACV,YAAI,aAAa,KAAK,QAAQ,CAAC;AAC/B,YAAI,CAAC,kBAAkB;AACrB,cAAI,SAAS;AAAA,YACX,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO;AAAA;AAAA,cAEL,SAAS,WAAW,MAAM;AAAA,YAC5B;AAAA,UACF;AACA,qBAAW,SAAS,WAAW,SAAS;AACxC,qBAAW,MAAM,UAAU;AAC3B,UAAQ,UAAU,YAAY,QAAQ,aAAa,GAAG;AAAA,QACxD;AAAA,MACF;AACA,UAAI,kBAAkB;AAEpB,aAAK,QAAQ,CAAC,EAAE,cAAc,OAAO;AAAA,MACvC;AAAA,IACF;AACA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,MAAM,KAAK,YAAY,aAAa,MAAM;AACnF,UAAI,aAAa,KAAK,QAAQ,CAAC;AAC/B,UAAI,cAAc,KAAK;AACvB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa;AACf,4BAAoB,YAAY;AAChC,wBAAgB,YAAY;AAC5B,0BAAkB,YAAY;AAC9B,gBAAQ,YAAY;AACpB,oBAAY,YAAY;AACxB,4BAAoB,YAAY;AAChC,qBAAa,YAAY;AACzB,sBAAc,YAAY;AAC1B,2BAAmB,YAAY;AAAA,MACjC;AACA,UAAI,CAAC,eAAe,KAAK,eAAe;AACtC,YAAI,YAAY,eAAe,YAAY,YAAY,YAAY,YAAY,KAAK,aAAa,GAAG;AACpG,YAAI,gBAAgB,UAAU,SAAS,UAAU;AACjD,4BAAoB,cAAc,SAAS,WAAW,EAAE,aAAa;AACrE,0BAAkB,UAAU,SAAS,CAAC,UAAU,WAAW,CAAC,EAAE,aAAa;AAC3E,wBAAgB,UAAU,SAAS,CAAC,QAAQ,WAAW,CAAC,EAAE,aAAa;AACvE,gBAAQ,cAAc,IAAI,OAAO;AACjC,oBAAY,cAAc,IAAI,WAAW;AACzC,2BAAmB,cAAc,IAAI,UAAU;AAC/C,4BAAoB,qBAAqB,SAAS;AAClD,qBAAa,cAAc,WAAW,OAAO;AAC7C,sBAAc,UAAU,WAAW,QAAQ;AAAA,MAC7C;AACA,UAAI,eAAe,KAAK,cAAc,KAAK,cAAc;AACzD,iBAAW,KAAK,aAAa,gBAAgB,KAAK,KAAK,KAAK,OAAO,CAAC;AACpE,UAAI,eAAe,sBAAsB,KAAK,cAAc,KAAK,cAAc,GAAG,UAAU;AAC5F,UAAI,cAAc;AAChB,mBAAW,IAAI,aAAa,CAAC;AAC7B,mBAAW,IAAI,aAAa,CAAC;AAAA,MAC/B;AACA,qBAAe,WAAW,KAAK,UAAU,WAAW;AACpD,UAAI,cAAc,KAAK,cAAc,KAAK,OAAO;AACjD,UAAI,cAAc,YAAY;AAC9B,UAAI,sBAAsB,eAAS;AACjC,YAAI,YAAY,WAAW;AAC3B,mBAAW,SAAS,OAAO;AAAA;AAAA,UAEzB,OAAO,UAAU;AAAA,UACjB,GAAG,UAAU;AAAA,UACb,GAAG,UAAU;AAAA,UACb,OAAO,UAAU;AAAA,UACjB,QAAQ,UAAU;AAAA,QACpB,GAAG,WAAW,CAAC;AAAA,MACjB,OAAO;AACL,YAAI,WAAW,gBAAgB;AAI7B,qBAAW,SAAS,OAAO,CAAC,GAAG,WAAW,CAAC;AAAA,QAC7C,OAAO;AACL,qBAAW,SAAS,WAAW;AAAA,QACjC;AAEA,mBAAW,MAAM,QAAQ;AACzB,mBAAW,SAAS,aAAa,QAAQ,KAAK,gBAAgB;AAC9D,mBAAW,MAAM,gBAAgB;AAAA,MACnC;AACA,UAAI,QAAQ,KAAK,cAAc,KAAK,OAAO;AAC3C,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,MAAM;AACjB,YAAI,YAAY,MAAM;AACpB,eAAK,MAAM,WAAW;AACtB,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,WAAW,YAAY,MAAM;AAC3B,mBAAW,KAAK;AAChB,aAAK,MAAM;AAAA,MACb;AACA,UAAI,eAAe,QAAQ,KAAK;AAChC,oBAAc,YAAY,mBAAmB;AAAA,QAC3C,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB,YAAY;AAAA,MAC9B,CAAC;AAED,eAAS,oBAAoBC,MAAK;AAChC,eAAO,eAAe,KAAK,QAAQA,IAAG,IAAI,gBAAgB,MAAMA,IAAG;AAAA,MACrE;AACA,WAAK,SAAS,WAAW,CAAC,IAAI;AAC9B,WAAK,SAAS,WAAW,CAAC,IAAI;AAC9B,UAAI,gBAAgB,WAAW,YAAY,UAAU;AACrD,oBAAc,QAAQ;AACtB,iBAAW,YAAY,QAAQ,EAAE,QAAQ;AACzC,iBAAW,YAAY,MAAM,EAAE,QAAQ;AAGvC,UAAI,aAAa,cAAc,QAAQ,eAAe,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAExF,SAAS,UAAU,KAAK,aAAa,IAAI,CAAC,aAAa;AAEzD,oBAAc,SAAS,KAAK,SAAS;AACrC,oBAAc,SAAS,KAAK,SAAS;AACrC,WAAK,eAAe,CAAC;AACrB,0BAAoB,MAAM,OAAO,WAAW,gBAAgB;AAAA,IAC9D;AACA,IAAAD,QAAO,UAAU,iBAAiB,SAAU,OAAO;AACjD,WAAK,SAAS,KAAK,SAAS;AAAA,IAC9B;AACA,IAAAA,QAAO,UAAU,UAAU,SAAU,IAAI,aAAa,KAAK;AACzD,UAAI,aAAa,KAAK,QAAQ,CAAC;AAC/B,UAAI,YAAY,UAAU,IAAI,EAAE;AAChC,UAAI,eAAe,OAAO,IAAI;AAE9B,WAAK,SAAS,WAAW,SAAS;AAElC,UAAI,OAAO,IAAI,WAAW;AACxB,YAAI,cAAc,WAAW,eAAe;AAC5C,YAAI,aAAa;AACf,UAAQ,cAAc,aAAa;AAAA,YACjC,OAAO;AAAA,cACL,SAAS;AAAA,YACX;AAAA,UACF,GAAG,aAAa;AAAA,YACd;AAAA,YACA,WAAW;AAAA,YACX,IAAI,WAAY;AACd,yBAAW,kBAAkB;AAAA,YAC/B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,mBAAW,kBAAkB;AAAA,MAC/B;AACA,MAAQ,cAAc,YAAY;AAAA,QAChC,OAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,GAAG,aAAa;AAAA,QACd;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AACA,IAAAA,QAAO,gBAAgB,SAAU,MAAM,KAAK;AAC1C,aAAO,oBAAoB,KAAK,cAAc,KAAK,YAAY,CAAC;AAAA,IAClE;AACA,WAAOA;AAAA,EACT,EAAU,aAAK;AAAA;AACf,SAAS,YAAY,IAAI,IAAI;AAC3B,OAAK,OAAO,MAAM,IAAI,EAAE;AAC1B;AACA,IAAO,iBAAQ;;;AC7Rf,SAAS,gBAAgB,MAAM,OAAO,KAAK,KAAK;AAC9C,SAAO,SAAS,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,YAAY,IAAI,SAAS,GAAG,MAIvF,EAAE,IAAI,aAAa,CAAC,IAAI,UAAU,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,MAAM,KAAK,cAAc,KAAK,QAAQ,MAAM;AAC7G;AACA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,OAAO,QAAQ,CAAC,SAAS,GAAG,GAAG;AACjC,UAAM;AAAA,MACJ,UAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO,OAAO,CAAC;AACjB;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,cAAc,KAAK;AACvB,MAAI,gBAAgB,YAAY,SAAS,UAAU;AACnD,SAAO;AAAA,IACL,mBAAmB,cAAc,SAAS,WAAW,EAAE,aAAa;AAAA,IACpE,eAAe,YAAY,SAAS,CAAC,QAAQ,WAAW,CAAC,EAAE,aAAa;AAAA,IACxE,iBAAiB,YAAY,SAAS,CAAC,UAAU,WAAW,CAAC,EAAE,aAAa;AAAA,IAC5E,OAAO,cAAc,IAAI,OAAO;AAAA,IAChC,WAAW,cAAc,IAAI,WAAW;AAAA,IACxC,kBAAkB,cAAc,IAAI,UAAU;AAAA,IAC9C,YAAY,cAAc,IAAI,OAAO;AAAA,IACrC,mBAAmB,qBAAqB,WAAW;AAAA,IACnD,aAAa,YAAY,IAAI,QAAQ;AAAA,EACvC;AACF;AACA,IAAI;AAAA;AAAA,EAA0B,WAAY;AACxC,aAASE,YAAW,YAAY;AAC9B,WAAK,QAAQ,IAAY,cAAM;AAC/B,WAAK,cAAc,cAAc;AAAA,IACnC;AAIA,IAAAA,YAAW,UAAU,aAAa,SAAU,MAAM,KAAK;AAErD,WAAK,kBAAkB;AACvB,YAAM,mBAAmB,GAAG;AAC5B,UAAI,QAAQ,KAAK;AACjB,UAAI,cAAc,KAAK;AACvB,UAAI,UAAU,KAAK;AACnB,UAAI,aAAa,KAAK;AACtB,UAAI,mBAAmB,IAAI;AAC3B,UAAI,cAAc,gBAAgB,IAAI;AACtC,UAAI,kBAAkB;AAAA,QACpB;AAAA,MACF;AACA,UAAI,iBAAiB,IAAI,kBAAkB,SAAU,KAAK;AACxD,eAAO,KAAK,cAAc,GAAG;AAAA,MAC/B;AAGA,UAAI,CAAC,SAAS;AACZ,cAAM,UAAU;AAAA,MAClB;AACA,WAAK,KAAK,OAAO,EAAE,IAAI,SAAU,QAAQ;AACvC,YAAI,QAAQ,eAAe,MAAM;AACjC,YAAI,gBAAgB,MAAM,OAAO,QAAQ,GAAG,GAAG;AAC7C,cAAI,WAAW,IAAI,WAAW,MAAM,QAAQ,aAAa,eAAe;AACxE,mBAAS,YAAY,KAAK;AAC1B,eAAK,iBAAiB,QAAQ,QAAQ;AACtC,gBAAM,IAAI,QAAQ;AAAA,QACpB;AAAA,MACF,CAAC,EAAE,OAAO,SAAU,QAAQ,QAAQ;AAClC,YAAI,WAAW,QAAQ,iBAAiB,MAAM;AAC9C,YAAI,QAAQ,eAAe,MAAM;AACjC,YAAI,CAAC,gBAAgB,MAAM,OAAO,QAAQ,GAAG,GAAG;AAC9C,gBAAM,OAAO,QAAQ;AACrB;AAAA,QACF;AACA,YAAI,gBAAgB,KAAK,cAAc,QAAQ,QAAQ,KAAK;AAC5D,YAAI,gBAAgB,YAAY,SAAS,iBAAiB,SAAS,cAAc;AACjF,YAAI,CAAC,YAEF,iBAAiB,kBAAkB,eAAe;AACnD,gBAAM,OAAO,QAAQ;AACrB,qBAAW,IAAI,WAAW,MAAM,QAAQ,aAAa,eAAe;AACpE,mBAAS,YAAY,KAAK;AAAA,QAC5B,OAAO;AACL,mBAAS,WAAW,MAAM,QAAQ,aAAa,eAAe;AAC9D,cAAI,SAAS;AAAA,YACX,GAAG,MAAM,CAAC;AAAA,YACV,GAAG,MAAM,CAAC;AAAA,UACZ;AACA,6BAAmB,SAAS,KAAK,MAAM,IAAY,YAAY,UAAU,QAAQ,WAAW;AAAA,QAC9F;AAEA,cAAM,IAAI,QAAQ;AAClB,aAAK,iBAAiB,QAAQ,QAAQ;AAAA,MACxC,CAAC,EAAE,OAAO,SAAU,QAAQ;AAC1B,YAAI,KAAK,QAAQ,iBAAiB,MAAM;AACxC,cAAM,GAAG,QAAQ,WAAY;AAC3B,gBAAM,OAAO,EAAE;AAAA,QACjB,GAAG,WAAW;AAAA,MAChB,CAAC,EAAE,QAAQ;AACX,WAAK,kBAAkB;AACvB,WAAK,QAAQ;AAAA,IACf;AACA;AACA,IAAAA,YAAW,UAAU,eAAe,WAAY;AAC9C,UAAI,QAAQ;AACZ,UAAI,OAAO,KAAK;AAChB,UAAI,MAAM;AAER,aAAK,kBAAkB,SAAU,IAAI,KAAK;AACxC,cAAI,QAAQ,MAAM,gBAAgB,GAAG;AACrC,aAAG,YAAY,KAAK;AACpB,aAAG,WAAW;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AACA;AACA,IAAAA,YAAW,UAAU,2BAA2B,SAAU,MAAM;AAC9D,WAAK,eAAe,gBAAgB,IAAI;AACxC,WAAK,QAAQ;AACb,WAAK,MAAM,UAAU;AAAA,IACvB;AACA;AAIA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,YAAY,MAAM,KAAK;AAExE,WAAK,kBAAkB,CAAC;AACxB,YAAM,mBAAmB,GAAG;AAC5B,eAAS,0BAA0BC,KAAI;AACrC,YAAI,CAACA,IAAG,SAAS;AACf,UAAAA,IAAG,cAAc;AACjB,UAAAA,IAAG,YAAY,UAAU,EAAE,aAAa;AAAA,QAC1C;AAAA,MACF;AACA,eAAS,MAAM,WAAW,OAAO,MAAM,WAAW,KAAK,OAAO;AAC5D,YAAI,QAAQ,KAAK,cAAc,GAAG;AAClC,YAAI,gBAAgB,MAAM,OAAO,KAAK,GAAG,GAAG;AAC1C,cAAI,KAAK,IAAI,KAAK,YAAY,MAAM,KAAK,KAAK,YAAY;AAC1D,aAAG,SAAS,yBAAyB;AACrC,aAAG,YAAY,KAAK;AACpB,eAAK,MAAM,IAAI,EAAE;AACjB,eAAK,iBAAiB,KAAK,EAAE;AAC7B,eAAK,gBAAgB,KAAK,EAAE;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AACA;AACA,IAAAD,YAAW,UAAU,eAAe,SAAU,IAAI;AAChD,MAAQ,iBAAiB,KAAK,mBAAmB,KAAK,OAAO,EAAE;AAAA,IACjE;AACA,IAAAA,YAAW,UAAU,SAAS,SAAU,iBAAiB;AACvD,UAAI,QAAQ,KAAK;AACjB,UAAI,OAAO,KAAK;AAEhB,UAAI,QAAQ,iBAAiB;AAC3B,aAAK,kBAAkB,SAAU,IAAI;AACnC,aAAG,QAAQ,WAAY;AACrB,kBAAM,OAAO,EAAE;AAAA,UACjB,GAAG,KAAK,SAAS;AAAA,QACnB,CAAC;AAAA,MACH,OAAO;AACL,cAAM,UAAU;AAAA,MAClB;AAAA,IACF;AACA;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAO,qBAAQ;", "names": ["Symbol", "idx", "SymbolDraw", "el"]}