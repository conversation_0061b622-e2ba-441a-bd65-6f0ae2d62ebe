import {
  getBusinessObject,
  getDi,
  is
} from "./chunk-FNF472WR.js";
import {
  some
} from "./chunk-YTJ5ESGD.js";

// node_modules/.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/util/DiUtil.js
function isExpanded(element, di) {
  if (is(element, "bpmn:CallActivity")) {
    return false;
  }
  if (is(element, "bpmn:SubProcess")) {
    di = di || getDi(element);
    if (di && is(di, "bpmndi:BPMNPlane")) {
      return true;
    }
    return di && !!di.isExpanded;
  }
  if (is(element, "bpmn:Participant")) {
    return !!getBusinessObject(element).processRef;
  }
  return true;
}
function isHorizontal(element) {
  if (!is(element, "bpmn:Participant") && !is(element, "bpmn:Lane")) {
    return void 0;
  }
  var isHorizontal2 = getDi(element).isHorizontal;
  if (isHorizontal2 === void 0) {
    return true;
  }
  return isHorizontal2;
}
function isInterrupting(element) {
  return element && getBusinessObject(element).isInterrupting !== false;
}
function isEventSubProcess(element) {
  return element && !!getBusinessObject(element).triggeredByEvent;
}
function hasEventDefinition(element, eventType) {
  var eventDefinitions = getBusinessObject(element).eventDefinitions;
  return some(eventDefinitions, function(event) {
    return is(event, eventType);
  });
}
function hasErrorEventDefinition(element) {
  return hasEventDefinition(element, "bpmn:ErrorEventDefinition");
}
function hasEscalationEventDefinition(element) {
  return hasEventDefinition(element, "bpmn:EscalationEventDefinition");
}
function hasCompensateEventDefinition(element) {
  return hasEventDefinition(element, "bpmn:CompensateEventDefinition");
}

export {
  isExpanded,
  isHorizontal,
  isInterrupting,
  isEventSubProcess,
  hasEventDefinition,
  hasErrorEventDefinition,
  hasEscalationEventDefinition,
  hasCompensateEventDefinition
};
//# sourceMappingURL=chunk-7C6J56BH.js.map
