import {
  computed,
  createApp,
  createBaseVNode,
  createElement<PERSON><PERSON>,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentInstance,
  h,
  inject,
  isVNode,
  markRaw,
  mergeProps,
  nextTick,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  onUpdated,
  openBlock,
  provide,
  reactive,
  ref,
  resolveComponent,
  resolveDirective,
  toRef,
  toRefs,
  watch,
  watchEffect,
  withDirectives
} from "./chunk-GTWINWNV.js";

// node_modules/.pnpm/@form-create+element-ui@3.2.14_vue@3.5.12_typescript@5.3.3_/node_modules/@form-create/element-ui/dist/form-create.esm.js
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) {
      symbols = symbols.filter(function(sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }
    keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function(key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function(key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}
function _typeof(obj) {
  "@babel/helpers - typeof";
  if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
    _typeof = function(obj2) {
      return typeof obj2;
    };
  } else {
    _typeof = function(obj2) {
      return obj2 && typeof Symbol === "function" && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
    };
  }
  return _typeof(obj);
}
function _classCallCheck(instance2, Constructor) {
  if (!(instance2 instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, {
    constructor: {
      value: subClass,
      writable: true,
      configurable: true
    }
  });
  if (superClass)
    _setPrototypeOf(subClass, superClass);
}
function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf2(o2) {
    return o2.__proto__ || Object.getPrototypeOf(o2);
  };
  return _getPrototypeOf(o);
}
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf2(o2, p2) {
    o2.__proto__ = p2;
    return o2;
  };
  return _setPrototypeOf(o, p);
}
function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct)
    return false;
  if (Reflect.construct.sham)
    return false;
  if (typeof Proxy === "function")
    return true;
  try {
    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
    return true;
  } catch (e) {
    return false;
  }
}
function _assertThisInitialized(self) {
  if (self === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return self;
}
function _possibleConstructorReturn(self, call) {
  if (call && (typeof call === "object" || typeof call === "function")) {
    return call;
  } else if (call !== void 0) {
    throw new TypeError("Derived constructors may only return object or undefined");
  }
  return _assertThisInitialized(self);
}
function _createSuper(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived), result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}
function _toConsumableArray(arr) {
  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
}
function _arrayWithoutHoles(arr) {
  if (Array.isArray(arr))
    return _arrayLikeToArray(arr);
}
function _iterableToArray(iter) {
  if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null)
    return Array.from(iter);
}
function _unsupportedIterableToArray(o, minLen) {
  if (!o)
    return;
  if (typeof o === "string")
    return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor)
    n = o.constructor.name;
  if (n === "Map" || n === "Set")
    return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
    return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length)
    len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++)
    arr2[i] = arr[i];
  return arr2;
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function getSlot(slots, exclude) {
  return Object.keys(slots).reduce(function(lst, name2) {
    if (!exclude || exclude.indexOf(name2) === -1) {
      lst.push(slots[name2]);
    }
    return lst;
  }, []);
}
function toArray(value) {
  return Array.isArray(value) ? value : [null, void 0, ""].indexOf(value) > -1 ? [] : [value];
}
var NAME$8 = "fcCheckbox";
var Checkbox = defineComponent({
  name: NAME$8,
  inheritAttrs: false,
  props: {
    formCreateInject: Object,
    modelValue: {
      type: Array,
      "default": function _default() {
        return [];
      }
    },
    type: String,
    options: Array,
    input: Boolean,
    inputValue: String
  },
  emits: ["update:modelValue", "fc.el"],
  setup: function setup(props, _7) {
    var options = toRef(props.formCreateInject, "options", []);
    var opt = toRef(props, "options");
    var value = toRef(props, "modelValue");
    var inputValue = toRef(props, "inputValue", "");
    var customValue = ref(inputValue.value);
    var input4 = toRef(props, "input", false);
    var updateCustomValue = function updateCustomValue2(n) {
      var _value2 = _toConsumableArray(toArray(value.value));
      var idx = _value2.indexOf(customValue.value);
      customValue.value = n;
      if (idx > -1) {
        _value2.splice(idx, 1);
        _value2.push(n);
        onInput(_value2);
      }
    };
    watch(inputValue, function(n) {
      if (!input4.value) {
        customValue.value = n;
        return void 0;
      }
      updateCustomValue(n);
    });
    var _options = computed(function() {
      var arr = options.value || [];
      if (opt.value) {
        arr = opt.value || [];
      }
      return Array.isArray(arr) ? arr : [];
    });
    watch(value, function(n) {
      var value2 = null;
      if (!inputValue.value && n != null && Array.isArray(n) && n.length > 0 && input4.value) {
        var values = _options.value.map(function(item) {
          return item.value;
        });
        n.forEach(function(val) {
          if (values.indexOf(val) === -1) {
            value2 = val;
          }
        });
      }
      if (value2 != null) {
        customValue.value = value2;
      }
    }, {
      immediate: true
    });
    var onInput = function onInput2(n) {
      _7.emit("update:modelValue", n);
    };
    return {
      options: _options,
      value,
      onInput,
      updateCustomValue,
      makeInput: function makeInput2(Type) {
        if (!input4.value) {
          return void 0;
        }
        return createVNode(Type, {
          "value": customValue.value || void 0,
          "label": customValue.value || void 0
        }, {
          "default": function _default15() {
            return [createVNode(resolveComponent("ElInput"), {
              "size": "small",
              "modelValue": customValue.value,
              "onUpdate:modelValue": updateCustomValue
            }, null)];
          }
        });
      }
    };
  },
  render: function render() {
    var _this$$slots$default, _this$$slots, _this = this;
    var name2 = this.type === "button" ? "ElCheckboxButton" : "ElCheckbox";
    var Type = resolveComponent(name2);
    return createVNode(resolveComponent("ElCheckboxGroup"), mergeProps(this.$attrs, {
      "modelValue": this.value,
      "onUpdate:modelValue": this.onInput,
      "ref": "el"
    }), _objectSpread2({
      "default": function _default15() {
        return [_this.options.map(function(opt, index) {
          var props = _objectSpread2({}, opt);
          var value = props.value;
          var label = props.label;
          delete props.value;
          delete props.label;
          return createVNode(Type, mergeProps(props, {
            "label": value,
            "value": value,
            "key": name2 + index + "-" + value
          }), {
            "default": function _default16() {
              return [label || value || ""];
            }
          });
        }), (_this$$slots$default = (_this$$slots = _this.$slots)["default"]) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots), _this.makeInput(Type)];
      }
    }, getSlot(this.$slots, ["default"])));
  },
  mounted: function mounted() {
    this.$emit("fc.el", this.$refs.el);
  }
});
function Mitt(all) {
  all = all || /* @__PURE__ */ new Map();
  var mitt = {
    $on: function $on(type2, handler3) {
      var handlers = all.get(type2);
      var added = handlers && handlers.push(handler3);
      if (!added) {
        all.set(type2, [handler3]);
      }
    },
    $once: function $once(type2, handler3) {
      handler3._once = true;
      mitt.$on(type2, handler3);
    },
    $off: function $off(type2, handler3) {
      var handlers = all.get(type2);
      if (handlers) {
        handlers.splice(handlers.indexOf(handler3) >>> 0, 1);
      }
    },
    $emit: function $emit(type2) {
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      (all.get(type2) || []).slice().map(function(handler3) {
        if (handler3._once) {
          mitt.$off(type2, handler3);
          delete handler3._once;
        }
        handler3.apply(void 0, args);
      });
      (all.get("*") || []).slice().map(function(handler3) {
        handler3(type2, args);
      });
    }
  };
  return mitt;
}
function styleInject(css, ref2) {
  if (ref2 === void 0)
    ref2 = {};
  var insertAt = ref2.insertAt;
  if (!css || typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z$3 = "._fc-frame ._fc-files img{display:inline-block;height:100%;vertical-align:top;width:100%}._fc-frame ._fc-upload-btn{border:1px dashed #c0ccda;cursor:pointer}._fc-frame._fc-disabled ._fc-upload-btn,._fc-frame._fc-disabled .el-button{color:#999;cursor:not-allowed!important}._fc-frame ._fc-upload-cover{background:rgba(0,0,0,.6);bottom:0;left:0;opacity:0;position:absolute;right:0;top:0;-webkit-transition:opacity .3s;-o-transition:opacity .3s;transition:opacity .3s}._fc-frame ._fc-upload-cover i{color:#fff;cursor:pointer;font-size:20px;margin:0 2px}._fc-frame ._fc-files:hover ._fc-upload-cover{opacity:1}._fc-frame .el-upload{display:block}._fc-frame ._fc-upload-icon{cursor:pointer}._fc-files,._fc-frame ._fc-upload-btn{background:#fff;border:1px solid #c0ccda;border-radius:4px;-webkit-box-shadow:2px 2px 5px rgba(0,0,0,.1);box-shadow:2px 2px 5px rgba(0,0,0,.1);-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;height:58px;line-height:58px;margin-right:4px;overflow:hidden;position:relative;text-align:center;width:58px}";
styleInject(css_248z$3);
var script$6 = {
  name: "IconCircleClose"
};
var _hoisted_1$6 = {
  "class": "icon",
  viewBox: "0 0 1024 1024",
  xmlns: "http://www.w3.org/2000/svg"
};
var _hoisted_2$6 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M466.752 512l-90.496-90.496a32 32 0 0145.248-45.248L512 466.752l90.496-90.496a32 32 0 1145.248 45.248L557.248 512l90.496 90.496a32 32 0 11-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 01-45.248-45.248L466.752 512z"
}, null, -1);
var _hoisted_3$6 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M512 896a384 384 0 100-768 384 384 0 000 768zm0 64a448 448 0 110-896 448 448 0 010 896z"
}, null, -1);
var _hoisted_4 = [_hoisted_2$6, _hoisted_3$6];
function render$6(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", _hoisted_1$6, _hoisted_4);
}
script$6.render = render$6;
var script$5 = {
  name: "IconDocument"
};
var _hoisted_1$5 = {
  "class": "icon",
  viewBox: "0 0 1024 1024",
  xmlns: "http://www.w3.org/2000/svg"
};
var _hoisted_2$5 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM160 64h480l256 256v608a32 32 0 01-32 32H160a32 32 0 01-32-32V96a32 32 0 0132-32zm160 448h384v64H320v-64zm0-192h160v64H320v-64zm0 384h384v64H320v-64z"
}, null, -1);
var _hoisted_3$5 = [_hoisted_2$5];
function render$5(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", _hoisted_1$5, _hoisted_3$5);
}
script$5.render = render$5;
var script$4 = {
  name: "IconDelete"
};
var _hoisted_1$4 = {
  "class": "icon",
  viewBox: "0 0 1024 1024",
  xmlns: "http://www.w3.org/2000/svg"
};
var _hoisted_2$4 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M160 256H96a32 32 0 010-64h256V95.936a32 32 0 0132-32h256a32 32 0 0132 32V192h256a32 32 0 110 64h-64v672a32 32 0 01-32 32H192a32 32 0 01-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 01-32-32V416a32 32 0 0164 0v320a32 32 0 01-32 32zm192 0a32 32 0 01-32-32V416a32 32 0 0164 0v320a32 32 0 01-32 32z"
}, null, -1);
var _hoisted_3$4 = [_hoisted_2$4];
function render$4(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", _hoisted_1$4, _hoisted_3$4);
}
script$4.render = render$4;
var script$3 = {
  name: "IconView"
};
var _hoisted_1$3 = {
  "class": "icon",
  viewBox: "0 0 1024 1024",
  xmlns: "http://www.w3.org/2000/svg"
};
var _hoisted_2$3 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M512 160c320 0 512 352 512 352S832 864 512 864 0 512 0 512s192-352 512-352zm0 64c-225.28 0-384.128 208.064-436.8 288 52.608 79.872 211.456 288 436.8 288 225.28 0 384.128-208.064 436.8-288-52.608-79.872-211.456-288-436.8-288zm0 64a224 224 0 110 448 224 224 0 010-448zm0 64a160.192 160.192 0 00-160 160c0 88.192 71.744 160 160 160s160-71.808 160-160-71.744-160-160-160z"
}, null, -1);
var _hoisted_3$3 = [_hoisted_2$3];
function render$3(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", _hoisted_1$3, _hoisted_3$3);
}
script$3.render = render$3;
var script$2 = {
  name: "IconFolderOpened"
};
var _hoisted_1$2 = {
  "class": "icon",
  viewBox: "0 0 1024 1024",
  xmlns: "http://www.w3.org/2000/svg"
};
var _hoisted_2$2 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M878.08 448H241.92l-96 384h636.16l96-384zM832 384v-64H485.76L357.504 192H128v448l57.92-231.744A32 32 0 01216.96 384H832zm-24.96 512H96a32 32 0 01-32-32V160a32 32 0 0132-32h287.872l128.384 128H864a32 32 0 0132 32v96h23.04a32 32 0 0131.04 39.744l-112 448A32 32 0 01807.04 896z"
}, null, -1);
var _hoisted_3$2 = [_hoisted_2$2];
function render$2(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", _hoisted_1$2, _hoisted_3$2);
}
script$2.render = render$2;
function _isSlot(s) {
  return typeof s === "function" || Object.prototype.toString.call(s) === "[object Object]" && !isVNode(s);
}
var NAME$7 = "fcFrame";
var Frame = defineComponent({
  name: NAME$7,
  props: {
    type: {
      type: String,
      "default": "input"
    },
    field: String,
    helper: {
      type: Boolean,
      "default": true
    },
    disabled: {
      type: Boolean,
      "default": false
    },
    src: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      "default": "IconFolderOpened"
    },
    width: {
      type: String,
      "default": "500px"
    },
    height: {
      type: String,
      "default": "370px"
    },
    maxLength: {
      type: Number,
      "default": 0
    },
    okBtnText: {
      type: String,
      "default": "确定"
    },
    closeBtnText: {
      type: String,
      "default": "关闭"
    },
    modalTitle: String,
    handleIcon: {
      type: [String, Boolean],
      "default": void 0
    },
    title: String,
    allowRemove: {
      type: Boolean,
      "default": true
    },
    onOpen: {
      type: Function,
      "default": function _default2() {
      }
    },
    onOk: {
      type: Function,
      "default": function _default3() {
      }
    },
    onCancel: {
      type: Function,
      "default": function _default4() {
      }
    },
    onLoad: {
      type: Function,
      "default": function _default5() {
      }
    },
    onBeforeRemove: {
      type: Function,
      "default": function _default6() {
      }
    },
    onRemove: {
      type: Function,
      "default": function _default7() {
      }
    },
    onHandle: Function,
    modal: {
      type: Object,
      "default": function _default8() {
        return {};
      }
    },
    srcKey: [String, Number],
    modelValue: [Array, String, Number, Object],
    previewMask: void 0,
    footer: {
      type: Boolean,
      "default": true
    },
    reload: {
      type: Boolean,
      "default": true
    },
    closeBtn: {
      type: Boolean,
      "default": true
    },
    okBtn: {
      type: Boolean,
      "default": true
    },
    formCreateInject: Object
  },
  emits: ["update:modelValue", "change"],
  components: {
    IconFolderOpened: script$2,
    IconView: script$3
  },
  data: function data() {
    return {
      fileList: toArray(this.modelValue),
      previewVisible: false,
      frameVisible: false,
      previewImage: "",
      bus: new Mitt()
    };
  },
  watch: {
    modelValue: function modelValue(n) {
      this.fileList = toArray(n);
    }
  },
  methods: {
    close: function close() {
      this.closeModel(true);
    },
    closeModel: function closeModel(close2) {
      this.bus.$emit(close2 ? "$close" : "$ok");
      if (this.reload) {
        this.bus.$off("$ok");
        this.bus.$off("$close");
      }
      this.frameVisible = false;
    },
    handleCancel: function handleCancel() {
      this.previewVisible = false;
    },
    showModel: function showModel() {
      if (this.disabled || false === this.onOpen()) {
        return;
      }
      this.frameVisible = true;
    },
    input: function input() {
      var n = this.fileList;
      var val = this.maxLength === 1 ? n[0] || "" : n;
      this.$emit("update:modelValue", val);
      this.$emit("change", val);
    },
    makeInput: function makeInput() {
      var _this = this;
      return createVNode(resolveComponent("ElInput"), mergeProps({
        type: "text",
        modelValue: this.fileList.map(function(v) {
          return _this.getSrc(v);
        }).toString(),
        readonly: true
      }, {
        "key": 1
      }), {
        append: function append() {
          return createVNode(resolveComponent("ElButton"), {
            "icon": resolveComponent(_this.icon),
            "onClick": function onClick() {
              return _this.showModel();
            }
          }, null);
        },
        suffix: function suffix() {
          return _this.fileList.length && !_this.disabled ? createVNode(resolveComponent("ElIcon"), {
            "class": "el-input__icon _fc-upload-icon",
            "onClick": function onClick() {
              _this.fileList = [];
              _this.input();
            }
          }, {
            "default": function _default15() {
              return [createVNode(script$6, null, null)];
            }
          }) : null;
        }
      });
    },
    makeGroup: function makeGroup(children) {
      if (!this.maxLength || this.fileList.length < this.maxLength) {
        children.push(this.makeBtn());
      }
      return createVNode("div", {
        "key": 2
      }, [children]);
    },
    makeItem: function makeItem(index, children) {
      return createVNode("div", {
        "class": "_fc-files",
        "key": "3" + index
      }, [children]);
    },
    valid: function valid(f) {
      var field = this.formCreateInject.field || this.field;
      if (field && f !== field) {
        throw new Error("[frame]无效的字段值");
      }
    },
    makeIcons: function makeIcons(val, index) {
      if (this.handleIcon !== false || this.allowRemove === true) {
        var icons = [];
        if (this.type !== "file" && this.handleIcon !== false || this.type === "file" && this.handleIcon) {
          icons.push(this.makeHandleIcon(val, index));
        }
        if (this.allowRemove) {
          icons.push(this.makeRemoveIcon(val, index));
        }
        return createVNode("div", {
          "class": "_fc-upload-cover",
          "key": 4
        }, [icons]);
      }
    },
    makeHandleIcon: function makeHandleIcon(val, index) {
      var _this2 = this;
      var Type = resolveComponent(this.handleIcon === true || this.handleIcon === void 0 ? "icon-view" : this.handleIcon);
      return createVNode(resolveComponent("ElIcon"), {
        "onClick": function onClick() {
          return _this2.handleClick(val);
        },
        "key": "5" + index
      }, {
        "default": function _default15() {
          return [createVNode(Type, null, null)];
        }
      });
    },
    makeRemoveIcon: function makeRemoveIcon(val, index) {
      var _this3 = this;
      return createVNode(resolveComponent("ElIcon"), {
        "onClick": function onClick() {
          return _this3.handleRemove(val);
        },
        "key": "6" + index
      }, {
        "default": function _default15() {
          return [createVNode(script$4, null, null)];
        }
      });
    },
    makeFiles: function makeFiles() {
      var _this4 = this;
      return this.makeGroup(this.fileList.map(function(src, index) {
        return _this4.makeItem(index, [createVNode(resolveComponent("ElIcon"), {
          "onClick": function onClick() {
            return _this4.handleClick(src);
          }
        }, {
          "default": function _default15() {
            return [createVNode(script$5, null, null)];
          }
        }), _this4.makeIcons(src, index)]);
      }));
    },
    makeImages: function makeImages() {
      var _this5 = this;
      return this.makeGroup(this.fileList.map(function(src, index) {
        return _this5.makeItem(index, [createVNode("img", {
          "src": _this5.getSrc(src)
        }, null), _this5.makeIcons(src, index)]);
      }));
    },
    makeBtn: function makeBtn() {
      var _this6 = this;
      var Type = resolveComponent(this.icon);
      return createVNode("div", {
        "class": "_fc-upload-btn",
        "onClick": function onClick() {
          return _this6.showModel();
        },
        "key": 7
      }, [createVNode(resolveComponent("ElIcon"), null, {
        "default": function _default15() {
          return [createVNode(Type, null, null)];
        }
      })]);
    },
    handleClick: function handleClick(src) {
      if (this.onHandle) {
        return this.onHandle(src);
      } else {
        this.previewImage = this.getSrc(src);
        this.previewVisible = true;
      }
    },
    handleRemove: function handleRemove(src) {
      if (this.disabled) {
        return;
      }
      if (false !== this.onBeforeRemove(src)) {
        this.fileList.splice(this.fileList.indexOf(src), 1);
        this.input();
        this.onRemove(src);
      }
    },
    getSrc: function getSrc(src) {
      return !this.srcKey ? src : src[this.srcKey];
    },
    frameLoad: function frameLoad(iframe) {
      var _this7 = this;
      this.onLoad(iframe);
      try {
        if (this.helper === true) {
          iframe["form_create_helper"] = {
            api: this.formCreateInject.api,
            close: function close2(field) {
              _this7.valid(field);
              _this7.closeModel();
            },
            set: function set(field, value) {
              _this7.valid(field);
              !_this7.disabled && _this7.$emit("update:modelValue", value);
            },
            get: function get(field) {
              _this7.valid(field);
              return _this7.modelValue;
            },
            onOk: function onOk(fn) {
              return _this7.bus.$on("$ok", fn);
            },
            onClose: function onClose(fn) {
              return _this7.bus.$on("$close", fn);
            }
          };
        }
      } catch (e) {
        console.error(e);
      }
    },
    makeFooter: function makeFooter() {
      var _this8 = this;
      var _this$$props = this.$props, okBtnText = _this$$props.okBtnText, closeBtnText = _this$$props.closeBtnText, closeBtn = _this$$props.closeBtn, okBtn = _this$$props.okBtn, footer = _this$$props.footer;
      if (!footer) {
        return;
      }
      return createVNode("div", null, [closeBtn ? createVNode(resolveComponent("ElButton"), {
        "onClick": function onClick() {
          return _this8.onCancel() !== false && (_this8.frameVisible = false);
        }
      }, _isSlot(closeBtnText) ? closeBtnText : {
        "default": function _default15() {
          return [closeBtnText];
        }
      }) : null, okBtn ? createVNode(resolveComponent("ElButton"), {
        "type": "primary",
        "onClick": function onClick() {
          return _this8.onOk() !== false && _this8.closeModel();
        }
      }, _isSlot(okBtnText) ? okBtnText : {
        "default": function _default15() {
          return [okBtnText];
        }
      }) : null]);
    }
  },
  render: function render2() {
    var _this9 = this;
    var type2 = this.type;
    var node;
    if (type2 === "input") {
      node = this.makeInput();
    } else if (type2 === "image") {
      node = this.makeImages();
    } else {
      node = this.makeFiles();
    }
    var _this$$props2 = this.$props, _this$$props2$width = _this$$props2.width, width = _this$$props2$width === void 0 ? "30%" : _this$$props2$width, height = _this$$props2.height, src = _this$$props2.src, title = _this$$props2.title, modalTitle = _this$$props2.modalTitle;
    nextTick(function() {
      if (_this9.$refs.frame) {
        _this9.frameLoad(_this9.$refs.frame.contentWindow || {});
      }
    });
    return createVNode("div", {
      "class": {
        "_fc-frame": true,
        "_fc-disabled": this.disabled
      }
    }, [node, createVNode(resolveComponent("ElDialog"), {
      "appendToBody": true,
      "modal": this.previewMask,
      "title": modalTitle,
      "modelValue": this.previewVisible,
      "onClose": this.handleCancel
    }, {
      "default": function _default15() {
        return [createVNode("img", {
          "style": "width: 100%",
          "src": _this9.previewImage
        }, null)];
      }
    }), createVNode(resolveComponent("ElDialog"), mergeProps({
      "appendToBody": true
    }, _objectSpread2({
      width,
      title
    }, this.modal), {
      "modelValue": this.frameVisible,
      "onClose": function onClose() {
        return _this9.closeModel(true);
      }
    }), {
      "default": function _default15() {
        return [_this9.frameVisible || !_this9.reload ? createVNode("iframe", {
          "ref": "frame",
          "src": src,
          "frameBorder": "0",
          "style": {
            height,
            "border": "0 none",
            "width": "100%"
          }
        }, null) : null];
      },
      footer: function footer() {
        return _this9.makeFooter();
      }
    })]);
  },
  beforeMount: function beforeMount() {
    var _this$formCreateInjec = this.formCreateInject, name2 = _this$formCreateInjec.name, field = _this$formCreateInjec.field, api = _this$formCreateInjec.api;
    name2 && api.on("fc:closeModal:" + name2, this.close);
    field && api.on("fc:closeModal:" + field, this.close);
  },
  beforeUnmount: function beforeUnmount() {
    var _this$formCreateInjec2 = this.formCreateInject, name2 = _this$formCreateInjec2.name, field = _this$formCreateInjec2.field, api = _this$formCreateInjec2.api;
    name2 && api.off("fc:closeModal:" + name2, this.close);
    field && api.off("fc:closeModal:" + field, this.close);
  }
});
var NAME$6 = "fcRadio";
var Radio = defineComponent({
  name: NAME$6,
  inheritAttrs: false,
  props: {
    formCreateInject: Object,
    modelValue: {
      type: [String, Number, Boolean],
      "default": ""
    },
    options: Array,
    type: String,
    input: Boolean,
    inputValue: String
  },
  emits: ["update:modelValue", "fc.el"],
  setup: function setup2(props, _7) {
    var options = toRef(props.formCreateInject, "options", []);
    var opt = toRef(props, "options");
    var value = toRef(props, "modelValue");
    var inputValue = toRef(props, "inputValue", "");
    var customValue = ref(inputValue.value);
    var input4 = toRef(props, "input", false);
    watch(inputValue, function(n) {
      if (!input4.value) {
        customValue.value = n;
        return void 0;
      }
      updateCustomValue(n);
    });
    var _options = computed(function() {
      var arr = options.value || [];
      if (opt.value) {
        arr = opt.value || [];
      }
      return Array.isArray(arr) ? arr : [];
    });
    watch(value, function(n) {
      var flag = false;
      if (!inputValue.value && n != null && input4.value) {
        flag = _options.value.map(function(item) {
          return item.value;
        }).indexOf(n) === -1;
      }
      if (flag) {
        customValue.value = n;
      }
    }, {
      immediate: true
    });
    var onInput = function onInput2(n) {
      _7.emit("update:modelValue", n);
    };
    var updateCustomValue = function updateCustomValue2(n) {
      var o = customValue.value;
      customValue.value = n;
      if (value.value === o) {
        onInput(n);
      }
    };
    return {
      options: _options,
      value,
      onInput,
      updateCustomValue,
      customValue,
      makeInput: function makeInput2(Type) {
        if (!input4.value) {
          return void 0;
        }
        return createVNode(Type, {
          "checked": false,
          "value": customValue.value || void 0,
          "label": customValue.value || void 0
        }, {
          "default": function _default15() {
            return [createVNode(resolveComponent("ElInput"), {
              "size": "small",
              "modelValue": customValue.value,
              "onUpdate:modelValue": updateCustomValue
            }, null)];
          }
        });
      }
    };
  },
  render: function render3() {
    var _this$$slots$default, _this$$slots, _this = this;
    var name2 = this.type === "button" ? "ElRadioButton" : "ElRadio";
    var Type = resolveComponent(name2);
    return createVNode(resolveComponent("ElRadioGroup"), mergeProps(this.$attrs, {
      "modelValue": this.value,
      "onUpdate:modelValue": this.onInput,
      "ref": "el"
    }), _objectSpread2({
      "default": function _default15() {
        return [_this.options.map(function(opt, index) {
          var props = _objectSpread2({}, opt);
          var value = props.value;
          var label = props.label;
          delete props.value;
          delete props.label;
          return createVNode(Type, mergeProps(props, {
            "label": value,
            "value": value,
            "key": name2 + index + "-" + value
          }), {
            "default": function _default16() {
              return [label || value || ""];
            }
          });
        }), (_this$$slots$default = (_this$$slots = _this.$slots)["default"]) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots), _this.makeInput(Type)];
      }
    }, getSlot(this.$slots, ["default"])));
  },
  mounted: function mounted2() {
    this.$emit("fc.el", this.$refs.el);
  }
});
var is = {
  type: function type(arg, _type) {
    return Object.prototype.toString.call(arg) === "[object " + _type + "]";
  },
  Undef: function Undef(v) {
    return v === void 0 || v === null;
  },
  Element: function Element(arg) {
    return _typeof(arg) === "object" && arg !== null && arg.nodeType === 1 && !is.Object(arg);
  },
  trueArray: function trueArray(data5) {
    return Array.isArray(data5) && data5.length > 0;
  },
  Function: function Function2(v) {
    var type2 = this.getType(v);
    return type2 === "Function" || type2 === "AsyncFunction";
  },
  getType: function getType(v) {
    var str = Object.prototype.toString.call(v);
    return /^\[object (.*)\]$/.exec(str)[1];
  },
  empty: function empty(value) {
    if (value === void 0 || value === null) {
      return true;
    }
    if (Array.isArray(value) && Array.isArray(value) && !value.length) {
      return true;
    }
    return typeof value === "string" && !value;
  }
};
["Date", "Object", "String", "Boolean", "Array", "Number"].forEach(function(t3) {
  is[t3] = function(arg) {
    return is.type(arg, t3);
  };
});
function hasProperty(rule, k) {
  return {}.hasOwnProperty.call(rule, k);
}
var NAME$5 = "fcSelect";
var Select = defineComponent({
  name: NAME$5,
  inheritAttrs: false,
  props: {
    formCreateInject: Object,
    modelValue: {
      type: [Array, String, Number, Boolean, Object],
      "default": void 0
    },
    type: String
  },
  emits: ["update:modelValue", "fc.el"],
  setup: function setup3(props) {
    var options = toRef(props.formCreateInject, "options", []);
    var value = toRef(props, "modelValue");
    var _options = function _options2() {
      return Array.isArray(options.value) ? options.value : [];
    };
    return {
      options: _options,
      value
    };
  },
  render: function render4() {
    var _this = this, _this$$slots$default, _this$$slots;
    var makeOption = function makeOption2(props, index) {
      return createVNode(resolveComponent("ElOption"), mergeProps(props, {
        "key": "" + index + "-" + props.value
      }), null);
    };
    var makeOptionGroup = function makeOptionGroup2(props, index) {
      return createVNode(resolveComponent("ElOptionGroup"), {
        "label": props.label,
        "key": "" + index + "-" + props.label
      }, {
        "default": function _default15() {
          return [is.trueArray(props.options) && props.options.map(function(v, index2) {
            return makeOption(v, index2);
          })];
        }
      });
    };
    var options = this.options();
    return createVNode(resolveComponent("ElSelect"), mergeProps(this.$attrs, {
      "modelValue": this.value,
      "onUpdate:modelValue": function onUpdateModelValue(v) {
        return _this.$emit("update:modelValue", v);
      },
      "ref": "el"
    }), _objectSpread2({
      "default": function _default15() {
        return [options.map(function(props, index) {
          return hasProperty(props || "", "options") ? makeOptionGroup(props, index) : makeOption(props, index);
        }), (_this$$slots$default = (_this$$slots = _this.$slots)["default"]) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots)];
      }
    }, getSlot(this.$slots, ["default"])));
  },
  mounted: function mounted3() {
    this.$emit("fc.el", this.$refs.el);
  }
});
var NAME$4 = "fcTree";
var Tree = defineComponent({
  name: NAME$4,
  inheritAttrs: false,
  formCreateParser: {
    mergeProp: function mergeProp(ctx) {
      var props = ctx.prop.props;
      if (!props.nodeKey)
        props.nodeKey = "id";
      if (!props.props)
        props.props = {
          label: "title"
        };
    }
  },
  props: {
    type: String,
    modelValue: {
      type: [Array, String, Number],
      "default": function _default9() {
        return [];
      }
    }
  },
  emits: ["update:modelValue", "fc.el"],
  watch: {
    modelValue: function modelValue2() {
      this.setValue();
    }
  },
  methods: {
    updateValue: function updateValue() {
      if (!this.$refs.tree)
        return;
      var value;
      if (this.type === "selected") {
        value = this.$refs.tree.getCurrentKey();
      } else {
        value = this.$refs.tree.getCheckedKeys();
      }
      this.$emit("update:modelValue", value);
    },
    setValue: function setValue() {
      if (!this.$refs.tree)
        return;
      var type2 = this.type;
      if (type2 === "selected") {
        this.$refs.tree.setCurrentKey(this.modelValue);
      } else {
        this.$refs.tree.setCheckedKeys(toArray(this.modelValue));
      }
    }
  },
  render: function render5() {
    return createVNode(resolveComponent("ElTree"), mergeProps(this.$attrs, {
      "ref": "tree",
      "onCheck": this.updateValue,
      "onNodeClick": this.updateValue
    }), this.$slots);
  },
  mounted: function mounted4() {
    this.setValue();
    this.$emit("fc.el", this.$refs.tree);
  }
});
var css_248z$2 = "._fc-upload{width:100%}._fc-exceed .el-upload{display:none}.el-upload-list.is-disabled .el-upload{cursor:not-allowed!important}";
styleInject(css_248z$2);
var script$1 = {
  name: "IconUpload"
};
var _hoisted_1$1 = {
  "class": "icon",
  viewBox: "0 0 1024 1024",
  xmlns: "http://www.w3.org/2000/svg"
};
var _hoisted_2$1 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M160 832h704a32 32 0 110 64H160a32 32 0 110-64zm384-578.304V704h-64V247.296L237.248 490.048 192 444.8 508.8 128l316.8 316.8-45.312 45.248L544 253.696z"
}, null, -1);
var _hoisted_3$1 = [_hoisted_2$1];
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", _hoisted_1$1, _hoisted_3$1);
}
script$1.render = render$1;
function parseFile(file, i) {
  if (_typeof(file) === "object") {
    return file;
  }
  return {
    url: file,
    is_string: true,
    name: getFileName(file),
    uid: i
  };
}
function parseUpload(file) {
  return _objectSpread2(_objectSpread2({}, file), {}, {
    file,
    value: file
  });
}
function getFileName(file) {
  return ("" + file).split("/").pop();
}
var NAME$3 = "fcUpload";
var Upload = defineComponent({
  name: NAME$3,
  inheritAttrs: false,
  formCreateParser: {
    toFormValue: function toFormValue(value) {
      return toArray(value);
    },
    toValue: function toValue(formValue, ctx) {
      return ctx.prop.props.limit === 1 ? formValue[0] || "" : formValue;
    }
  },
  props: {
    previewMask: void 0,
    onPreview: Function,
    modalTitle: String,
    listType: String,
    modelValue: [Array, String]
  },
  emits: ["update:modelValue", "change", "remove", "fc.el"],
  data: function data2() {
    return {
      previewVisible: false,
      previewImage: "",
      fileList: []
    };
  },
  created: function created() {
    this.fileList = toArray(this.modelValue).map(parseFile).map(parseUpload);
  },
  watch: {
    modelValue: function modelValue3(n) {
      this.fileList = toArray(n).map(parseFile).map(parseUpload);
    }
  },
  methods: {
    handlePreview: function handlePreview(file) {
      if (this.onPreview) {
        this.onPreview.apply(this, arguments);
      } else {
        if ("text" === this.listType) {
          window.open(file.url);
        } else {
          this.previewImage = file.url;
          this.previewVisible = true;
        }
      }
    },
    update: function update(fileList) {
      var files = fileList.map(function(v) {
        return v.is_string ? v.url : v.value || v.url;
      }).filter(function(url) {
        return url !== void 0 && url.indexOf("blob:") !== 0;
      });
      this.$emit("update:modelValue", files);
    },
    handleCancel: function handleCancel2() {
      this.previewVisible = false;
    },
    handleChange: function handleChange(file, fileList) {
      this.$emit.apply(this, ["change"].concat(Array.prototype.slice.call(arguments)));
      if (file.status === "success") {
        this.update(fileList);
      }
    },
    handleRemove: function handleRemove2(file, fileList) {
      this.$emit.apply(this, ["remove"].concat(Array.prototype.slice.call(arguments)));
      this.update(fileList);
    }
  },
  render: function render6() {
    var _this$$slots$default, _this$$slots, _this = this;
    var len = toArray(this.modelValue).length;
    return createVNode("div", {
      "class": "_fc-upload"
    }, [createVNode(resolveComponent("ElUpload"), mergeProps({
      "key": len
    }, this.$attrs, {
      "listType": this.listType || "picture-card",
      "class": {
        "_fc-exceed": this.$attrs.limit ? this.$attrs.limit <= len : false
      },
      "onPreview": this.handlePreview,
      "onChange": this.handleChange,
      "onRemove": this.handleRemove,
      "fileList": this.fileList,
      "ref": "upload"
    }), _objectSpread2({
      "default": function _default15() {
        return [((_this$$slots$default = (_this$$slots = _this.$slots)["default"]) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots)) || (["text", "picture"].indexOf(_this.listType) === -1 ? createVNode(resolveComponent("ElIcon"), null, {
          "default": function _default16() {
            return [createVNode(script$1, null, null)];
          }
        }) : createVNode(resolveComponent("ElButton"), {
          "type": "primary"
        }, {
          "default": function _default16() {
            return [createTextVNode("点击上传")];
          }
        }))];
      }
    }, getSlot(this.$slots, ["default"]))), createVNode(resolveComponent("ElDialog"), {
      "appendToBody": true,
      "modal": this.previewMask,
      "title": this.modalTitle,
      "modelValue": this.previewVisible,
      "onClose": this.handleCancel
    }, {
      "default": function _default15() {
        return [createVNode("img", {
          "style": "width: 100%",
          "src": _this.previewImage
        }, null)];
      }
    })]);
  },
  mounted: function mounted5() {
    this.$emit("fc.el", this.$refs.upload);
  }
});
function $set(target, field, value) {
  target[field] = value;
}
function $del(target, field) {
  delete target[field];
}
function deepExtend(origin) {
  var target = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  var mode = arguments.length > 2 ? arguments[2] : void 0;
  var isArr = false;
  for (var key in target) {
    if (Object.prototype.hasOwnProperty.call(target, key)) {
      var clone = target[key];
      if ((isArr = Array.isArray(clone)) || is.Object(clone)) {
        var nst = origin[key] === void 0;
        if (isArr) {
          isArr = false;
          nst && $set(origin, key, []);
        } else if (clone._clone && mode !== void 0) {
          if (mode) {
            clone = clone.getRule();
            nst && $set(origin, key, {});
          } else {
            $set(origin, key, clone._clone());
            continue;
          }
        } else {
          nst && $set(origin, key, {});
        }
        origin[key] = deepExtend(origin[key], clone, mode);
      } else {
        $set(origin, key, clone);
        if (!is.Undef(clone)) {
          if (!is.Undef(clone.__json)) {
            origin[key].__json = clone.__json;
          }
          if (!is.Undef(clone.__origin)) {
            origin[key].__origin = clone.__origin;
          }
        }
      }
    }
  }
  return mode !== void 0 && Array.isArray(origin) ? origin.filter(function(v) {
    return !v || !v.__ctrl;
  }) : origin;
}
function deepCopy(value) {
  return deepExtend({}, {
    value
  }).value;
}
var _extends = Object.assign || function(a) {
  for (var b, c = 1; c < arguments.length; c++) {
    for (var d in b = arguments[c], b) {
      Object.prototype.hasOwnProperty.call(b, d) && $set(a, d, b[d]);
    }
  }
  return a;
};
function extend() {
  return _extends.apply(this, arguments);
}
function copy$1(obj) {
  if (_typeof(obj) !== "object" || obj === null)
    return obj;
  return obj instanceof Array ? _toConsumableArray(obj) : _objectSpread2({}, obj);
}
var css_248z$1 = '._fc-group{display:flex;flex-direction:column;justify-content:center;min-height:38px;width:100%}._fc-group-disabled ._fc-group-add,._fc-group-disabled ._fc-group-btn{cursor:not-allowed}._fc-group-handle{background-color:#fff;border:1px dashed #d9d9d9;border-radius:15px;bottom:-15px;display:flex;flex-direction:row;padding:3px 8px;position:absolute;right:30px}._fc-group-btn{cursor:pointer}._fc-group-idx{align-items:center;background:#eee;border-radius:15px;bottom:-15px;display:flex;font-weight:700;height:30px;justify-content:center;left:10px;position:absolute;width:30px}._fc-group-handle ._fc-group-btn+._fc-group-btn{margin-left:7px}._fc-group-container{border:1px dashed #d9d9d9;border-radius:5px;display:flex;flex-direction:column;margin:5px 5px 25px;padding:20px 20px 25px;position:relative}._fc-group-arrow{height:20px;position:relative;width:20px}._fc-group-arrow:before{border-left:2px solid #999;border-top:2px solid #999;content:"";height:9px;left:5px;position:absolute;top:8px;transform:rotate(45deg);width:9px}._fc-group-arrow._fc-group-down{transform:rotate(180deg)}._fc-group-plus-minus{cursor:pointer;height:20px;position:relative;width:20px}._fc-group-plus-minus:after,._fc-group-plus-minus:before{background-color:#409eff;content:"";height:2px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:60%}._fc-group-plus-minus:before{transform:translate(-50%,-50%) rotate(90deg)}._fc-group-plus-minus._fc-group-minus:before{display:none}._fc-group-plus-minus._fc-group-minus:after{background-color:#f56c6c}._fc-group-add{border:1px solid rgba(64,158,255,.5);border-radius:15px;cursor:pointer;height:25px;width:25px}._fc-group-add._fc-group-plus-minus:after,._fc-group-add._fc-group-plus-minus:before{width:50%}';
styleInject(css_248z$1);
var NAME$2 = "fcGroup";
var Group = defineComponent({
  name: NAME$2,
  props: {
    field: String,
    rule: Array,
    expand: Number,
    options: Object,
    button: {
      type: Boolean,
      "default": true
    },
    max: {
      type: Number,
      "default": 0
    },
    min: {
      type: Number,
      "default": 0
    },
    modelValue: {
      type: Array,
      "default": function _default10() {
        return [];
      }
    },
    defaultValue: Object,
    sortBtn: {
      type: Boolean,
      "default": true
    },
    disabled: {
      type: Boolean,
      "default": false
    },
    syncDisabled: {
      type: Boolean,
      "default": true
    },
    onBeforeRemove: {
      type: Function,
      "default": function _default11() {
      }
    },
    onBeforeAdd: {
      type: Function,
      "default": function _default12() {
      }
    },
    formCreateInject: Object,
    parse: Function
  },
  data: function data3() {
    return {
      len: 0,
      cacheRule: {},
      cacheValue: {},
      sort: [],
      form: markRaw(this.formCreateInject.form.$form())
    };
  },
  emits: ["update:modelValue", "change", "itemMounted", "remove", "add"],
  watch: {
    rule: {
      handler: function handler(n, o) {
        var _this = this;
        Object.keys(this.cacheRule).forEach(function(v) {
          var item = _this.cacheRule[v];
          if (item.$f) {
            var val = item.$f.formData();
            if (n === o) {
              item.$f.deferSyncValue(function() {
                deepExtend(item.rule, n);
                item.$f.setValue(val);
              }, true);
            } else {
              var _val = item.$f.formData();
              item.$f.once("reloading", function() {
                item.$f.setValue(_val);
              });
              item.rule = deepCopy(n);
            }
          }
        });
      },
      deep: true
    },
    expand: function expand(n) {
      var d = n - this.modelValue.length;
      if (d > 0) {
        this.expandRule(d);
      }
    },
    modelValue: {
      handler: function handler2(n) {
        var _this2 = this;
        n = n || [];
        var keys = this.sort, total = keys.length, len = total - n.length;
        if (len < 0) {
          for (var i = len; i < 0; i++) {
            this.addRule(n.length + i, true);
          }
          for (var _i = 0; _i < total; _i++) {
            this.setValue(keys[_i], n[_i]);
          }
        } else {
          if (len > 0) {
            for (var _i2 = 0; _i2 < len; _i2++) {
              this.removeRule(keys[total - _i2 - 1]);
            }
          }
          n.forEach(function(val, i2) {
            _this2.setValue(keys[i2], n[i2]);
          });
        }
      },
      deep: true
    }
  },
  methods: {
    _value: function _value(v) {
      return v && hasProperty(v, this.field) ? v[this.field] : v;
    },
    cache: function cache(k, val) {
      this.cacheValue[k] = JSON.stringify(val);
    },
    input: function input2(value) {
      this.$emit("update:modelValue", value);
      this.$emit("change", value);
    },
    formData: function formData(key, _formData) {
      var _this3 = this;
      var cacheRule = this.cacheRule;
      var keys = this.sort;
      if (keys.filter(function(k) {
        return cacheRule[k].$f;
      }).length !== keys.length) {
        return;
      }
      var value = keys.map(function(k) {
        var data5 = key === k ? _formData : _objectSpread2({}, _this3.cacheRule[k].$f.form);
        var value2 = _this3.field ? data5[_this3.field] || null : data5;
        _this3.cache(k, value2);
        return value2;
      });
      this.input(value);
    },
    setValue: function setValue2(key, value) {
      var field = this.field;
      if (field) {
        value = _defineProperty({}, field, this._value(value));
      }
      if (this.cacheValue[key] === JSON.stringify(field ? value[field] : value)) {
        return;
      }
      this.cache(key, value);
    },
    addRule: function addRule(i, emit) {
      var _this4 = this;
      var rule = this.formCreateInject.form.copyRules(this.rule || []);
      var options = this.options ? _objectSpread2({}, this.options) : {
        submitBtn: false,
        resetBtn: false
      };
      if (this.defaultValue) {
        if (!options.formData)
          options.formData = {};
        var defVal = deepCopy(this.defaultValue);
        extend(options.formData, this.field ? _defineProperty({}, this.field, defVal) : defVal);
      }
      this.parse && this.parse({
        rule,
        options,
        index: this.sort.length
      });
      this.cacheRule[++this.len] = {
        rule,
        options
      };
      if (emit) {
        nextTick(function() {
          return _this4.$emit("add", rule, Object.keys(_this4.cacheRule).length - 1);
        });
      }
    },
    add$f: function add$f(i, key, $f) {
      var _this5 = this;
      this.cacheRule[key].$f = $f;
      nextTick(function() {
        _this5.$emit("itemMounted", $f, Object.keys(_this5.cacheRule).indexOf(key));
      });
    },
    removeRule: function removeRule(key, emit) {
      var _this6 = this;
      var index = Object.keys(this.cacheRule).indexOf(key);
      delete this.cacheRule[key];
      delete this.cacheValue[key];
      if (emit) {
        nextTick(function() {
          return _this6.$emit("remove", index);
        });
      }
    },
    add: function add(i) {
      if (this.disabled || false === this.onBeforeAdd(this.modelValue)) {
        return;
      }
      var value = _toConsumableArray(this.modelValue);
      value.push(this.defaultValue ? deepCopy(this.defaultValue) : this.field ? null : {});
      this.input(value);
    },
    del: function del(index, key) {
      if (this.disabled || false === this.onBeforeRemove(this.modelValue, index)) {
        return;
      }
      this.removeRule(key, true);
      var value = _toConsumableArray(this.modelValue);
      value.splice(index, 1);
      this.input(value);
    },
    addIcon: function addIcon(key) {
      return createVNode("div", {
        "class": "_fc-group-btn _fc-group-plus-minus",
        "onClick": this.add
      }, null);
    },
    delIcon: function delIcon(index, key) {
      var _this7 = this;
      return createVNode("div", {
        "class": "_fc-group-btn _fc-group-plus-minus _fc-group-minus",
        "onClick": function onClick() {
          return _this7.del(index, key);
        }
      }, null);
    },
    sortUpIcon: function sortUpIcon(index) {
      var _this8 = this;
      return createVNode("div", {
        "class": "_fc-group-btn _fc-group-arrow _fc-group-up",
        "onClick": function onClick() {
          return _this8.changeSort(index, -1);
        }
      }, null);
    },
    sortDownIcon: function sortDownIcon(index) {
      var _this9 = this;
      return createVNode("div", {
        "class": "_fc-group-btn _fc-group-arrow _fc-group-down",
        "onClick": function onClick() {
          return _this9.changeSort(index, 1);
        }
      }, null);
    },
    changeSort: function changeSort(index, sort) {
      var _this10 = this;
      var a = this.sort[index];
      this.sort[index] = this.sort[index + sort];
      this.sort[index + sort] = a;
      this.formCreateInject.subForm(this.sort.map(function(k) {
        return _this10.cacheRule[k].$f;
      }));
      this.formData(0);
    },
    makeIcon: function makeIcon(total, index, key) {
      var _this11 = this;
      if (this.$slots.button) {
        return this.$slots.button({
          total,
          index,
          vm: this,
          key,
          del: function del2() {
            return _this11.del(index, key);
          },
          add: this.add
        });
      }
      var btn = [];
      if ((!this.max || total < this.max) && total === index + 1) {
        btn.push(this.addIcon(key));
      }
      if (total > this.min) {
        btn.push(this.delIcon(index, key));
      }
      if (this.sortBtn && index) {
        btn.push(this.sortUpIcon(index));
      }
      if (this.sortBtn && index !== total - 1) {
        btn.push(this.sortDownIcon(index));
      }
      return btn;
    },
    emitEvent: function emitEvent(name2, args, index, key) {
      this.$emit.apply(this, [name2].concat(_toConsumableArray(args), [this.cacheRule[key].$f, index]));
    },
    expandRule: function expandRule(n) {
      for (var i = 0; i < n; i++) {
        this.addRule(i);
      }
    }
  },
  created: function created2() {
    var _this12 = this;
    watch(function() {
      return _objectSpread2({}, _this12.cacheRule);
    }, function(n) {
      _this12.sort = Object.keys(n);
    }, {
      immediate: true
    });
    var d = (this.expand || 0) - this.modelValue.length;
    for (var i = 0; i < this.modelValue.length; i++) {
      this.addRule(i);
    }
    if (d > 0) {
      this.expandRule(d);
    }
  },
  render: function render7() {
    var _this13 = this;
    var keys = this.sort;
    var button = this.button;
    var Type = this.form;
    var disabled = this.disabled;
    var children = keys.length === 0 ? this.$slots["default"] ? this.$slots["default"]({
      vm: this,
      add: this.add
    }) : createVNode("div", {
      "key": "a_def",
      "class": "_fc-group-plus-minus _fc-group-add fc-clock",
      "onClick": this.add
    }, null) : keys.map(function(key, index) {
      var _this13$cacheRule$key = _this13.cacheRule[key], rule = _this13$cacheRule$key.rule, options = _this13$cacheRule$key.options;
      var btn = button && !disabled ? _this13.makeIcon(keys.length, index, key) : [];
      return createVNode("div", {
        "class": "_fc-group-container",
        "key": key
      }, [createVNode(Type, mergeProps({
        "key": key
      }, {
        disabled,
        "onUpdate:modelValue": function onUpdateModelValue(formData3) {
          return _this13.formData(key, formData3);
        },
        "onEmit-event": function onEmitEvent(name2) {
          for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
            args[_key - 1] = arguments[_key];
          }
          return _this13.emitEvent(name2, args, index, key);
        },
        "onUpdate:api": function onUpdateApi($f) {
          return _this13.add$f(index, key, $f);
        },
        inFor: true,
        modelValue: _this13.field ? _defineProperty({}, _this13.field, _this13._value(_this13.modelValue[index])) : _this13.modelValue[index],
        rule,
        option: options,
        extendOption: true
      }), null), createVNode("div", {
        "class": "_fc-group-idx"
      }, [index + 1]), btn.length ? createVNode("div", {
        "class": "_fc-group-handle fc-clock"
      }, [btn]) : null]);
    });
    return createVNode("div", {
      "key": "con",
      "class": "_fc-group " + (disabled ? "_fc-group-disabled" : "")
    }, [children]);
  }
});
var NAME$1 = "fcSubForm";
var Sub = defineComponent({
  name: NAME$1,
  props: {
    rule: Array,
    options: {
      type: Object,
      "default": function _default13() {
        return reactive({
          submitBtn: false,
          resetBtn: false
        });
      }
    },
    modelValue: {
      type: Object,
      "default": function _default14() {
        return {};
      }
    },
    disabled: {
      type: Boolean,
      "default": false
    },
    syncDisabled: {
      type: Boolean,
      "default": true
    },
    formCreateInject: Object
  },
  data: function data4() {
    return {
      cacheValue: {},
      subApi: {},
      form: markRaw(this.formCreateInject.form.$form())
    };
  },
  emits: ["fc:subform", "update:modelValue", "change", "itemMounted"],
  watch: {
    modelValue: function modelValue4(n) {
      this.setValue(n);
    }
  },
  methods: {
    formData: function formData2(value) {
      this.cacheValue = JSON.stringify(value);
      this.$emit("update:modelValue", value);
      this.$emit("change", value);
    },
    setValue: function setValue3(value) {
      var str = JSON.stringify(value);
      if (this.cacheValue === str) {
        return;
      }
      this.cacheValue = str;
      this.subApi.coverValue(value || {});
    },
    add$f: function add$f2(api) {
      var _this = this;
      this.subApi = api;
      nextTick(function() {
        _this.$emit("itemMounted", api);
      });
    }
  },
  render: function render8() {
    var Type = this.form;
    return createVNode(Type, {
      "disabled": this.disabled,
      "onUpdate:modelValue": this.formData,
      "modelValue": this.modelValue,
      "onEmit-event": this.$emit,
      "onUpdate:api": this.add$f,
      "rule": this.rule,
      "option": this.options,
      "extendOption": true
    }, null);
  }
});
var script = {
  name: "IconWarning"
};
var _hoisted_1 = {
  "class": "icon",
  viewBox: "0 0 1024 1024",
  xmlns: "http://www.w3.org/2000/svg"
};
var _hoisted_2 = createBaseVNode("path", {
  fill: "currentColor",
  d: "M512 64a448 448 0 110 896 448 448 0 010-896zm0 832a384 384 0 000-768 384 384 0 000 768zm48-176a48 48 0 11-96 0 48 48 0 0196 0zm-48-464a32 32 0 0132 32v288a32 32 0 01-64 0V288a32 32 0 0132-32z"
}, null, -1);
var _hoisted_3 = [_hoisted_2];
function render9(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", _hoisted_1, _hoisted_3);
}
script.render = render9;
var components = [Checkbox, Frame, Radio, Select, Tree, Upload, Group, Sub, script];
function debounce(fn, wait) {
  var timeout = null;
  return function() {
    var _this = this;
    for (var _len = arguments.length, arg = new Array(_len), _key = 0; _key < _len; _key++) {
      arg[_key] = arguments[_key];
    }
    if (timeout !== null)
      clearTimeout(timeout);
    timeout = setTimeout(function() {
      return fn.call.apply(fn, [_this].concat(arg));
    }, wait);
  };
}
function toLine(name2) {
  var line = name2.replace(/([A-Z])/g, "-$1").toLocaleLowerCase();
  if (line.indexOf("-") === 0)
    line = line.substr(1);
  return line;
}
function upper(str) {
  return str.replace(str[0], str[0].toLocaleUpperCase());
}
var _getGroupInject = function getGroupInject(vm, parent) {
  if (!vm || vm === parent) {
    return;
  }
  if (vm.props.formCreateInject) {
    return vm.props.formCreateInject;
  }
  if (vm.parent) {
    return _getGroupInject(vm.parent, parent);
  }
};
function $FormCreate(FormCreate2, components2, directives) {
  return defineComponent({
    name: "FormCreate" + (FormCreate2.isMobile ? "Mobile" : ""),
    components: components2,
    directives,
    props: {
      rule: {
        type: Array,
        required: true,
        "default": function _default15() {
          return [];
        }
      },
      option: {
        type: Object,
        "default": function _default15() {
          return {};
        }
      },
      extendOption: Boolean,
      driver: [String, Object],
      modelValue: Object,
      disabled: {
        type: Boolean,
        "default": void 0
      },
      preview: {
        type: Boolean,
        "default": void 0
      },
      index: [String, Number],
      api: Object,
      locale: [String, Object],
      name: String,
      subForm: {
        type: Boolean,
        "default": true
      },
      inFor: Boolean
    },
    emits: ["update:api", "update:modelValue", "mounted", "submit", "reset", "change", "emit-event", "control", "remove-rule", "remove-field", "sync", "reload", "repeat-field", "update", "validate-field-fail", "validate-fail", "created"],
    render: function render17() {
      return this.fc.render();
    },
    setup: function setup4(props) {
      var vm = getCurrentInstance();
      provide("parentFC", vm);
      var parent = inject("parentFC", null);
      var top = parent;
      if (parent) {
        while (top.setupState.parent) {
          top = top.setupState.parent;
        }
      } else {
        top = vm;
      }
      var _toRefs = toRefs(props), rule = _toRefs.rule, modelValue5 = _toRefs.modelValue, subForm = _toRefs.subForm, inFor = _toRefs.inFor;
      var data5 = reactive({
        ctxInject: {},
        destroyed: false,
        isShow: true,
        unique: 1,
        renderRule: _toConsumableArray(rule.value || []),
        updateValue: JSON.stringify(modelValue5.value || {})
      });
      var fc = new FormCreate2(vm);
      var fapi = fc.api();
      var isMore = inFor.value;
      var addSubForm = function addSubForm2() {
        if (parent) {
          var _inject = _getGroupInject(vm, parent);
          if (_inject) {
            var sub;
            if (isMore) {
              sub = toArray(_inject.getSubForm());
              sub.push(fapi);
            } else {
              sub = fapi;
            }
            _inject.subForm(sub);
          }
        }
      };
      var rmSubForm = function rmSubForm2() {
        var inject2 = _getGroupInject(vm, parent);
        if (inject2) {
          if (isMore) {
            var sub = toArray(inject2.getSubForm());
            var idx = sub.indexOf(fapi);
            if (idx > -1) {
              sub.splice(idx, 1);
            }
          } else {
            inject2.subForm();
          }
        }
      };
      var styleEl = null;
      onBeforeMount(function() {
        watchEffect(function() {
          var content = "";
          var globalClass = props.option && props.option.globalClass || {};
          Object.keys(globalClass).forEach(function(k) {
            var subCss = "";
            globalClass[k].style && Object.keys(globalClass[k].style).forEach(function(key) {
              subCss += toLine(key) + ":" + globalClass[k].style[key] + ";";
            });
            if (globalClass[k].content) {
              subCss += globalClass[k].content + ";";
            }
            if (subCss) {
              content += ".".concat(k, "{").concat(subCss, "}");
            }
          });
          if (props.option && props.option.style) {
            content += props.option.style;
          }
          if (!styleEl) {
            styleEl = document.createElement("style");
            styleEl.type = "text/css";
            document.head.appendChild(styleEl);
          }
          styleEl.innerHTML = content || "";
        });
      });
      var emit$topForm = debounce(function() {
        fc.bus.$emit("$loadData.$topForm");
      }, 100);
      var emit$form = debounce(function() {
        fc.bus.$emit("$loadData.$form");
      }, 100);
      var emit$change = function emit$change2(field) {
        fc.bus.$emit("change-$form." + field);
      };
      onMounted(function() {
        if (parent) {
          fapi.top.bus.$on("$loadData.$form", emit$topForm);
          fapi.top.bus.$on("change", emit$change);
        }
        fc.mounted();
      });
      onBeforeUnmount(function() {
        if (parent) {
          fapi.top.bus.$off("$loadData.$form", emit$topForm);
          fapi.top.bus.$off("change", emit$change);
        }
        styleEl && document.head.removeChild(styleEl);
        rmSubForm();
        data5.destroyed = true;
        fc.unmount();
      });
      onUpdated(function() {
        fc.updated();
      });
      watch(subForm, function(n) {
        n ? addSubForm() : rmSubForm();
      }, {
        immediate: true
      });
      watch(function() {
        return _toConsumableArray(rule.value);
      }, function(n) {
        if (fc.$handle.isBreakWatch() || n.length === data5.renderRule.length && n.every(function(v) {
          return data5.renderRule.indexOf(v) > -1;
        }))
          return;
        fc.$handle.updateAppendData();
        fc.$handle.reloadRule(rule.value);
        vm.setupState.renderRule();
      });
      watch(function() {
        return props.option;
      }, function() {
        fc.initOptions();
        fapi.refresh();
      }, {
        deep: true
      });
      watch(function() {
        return [props.disabled, props.preview];
      }, function() {
        fapi.refresh();
      });
      watch(modelValue5, function(n) {
        if (JSON.stringify(n || {}) === data5.updateValue)
          return;
        if (fapi.config.forceCoverValue) {
          fapi.coverValue(n || {});
        } else {
          fapi.setValue(n || {});
        }
      }, {
        deep: true,
        flush: "post"
      });
      watch(function() {
        return props.index;
      }, function() {
        fapi.coverValue({});
        fc.$handle.updateAppendData();
        nextTick(function() {
          nextTick(function() {
            fapi.clearValidateState();
          });
        });
      }, {
        flush: "sync"
      });
      return _objectSpread2(_objectSpread2({
        fc: markRaw(fc),
        parent: parent ? markRaw(parent) : parent,
        top: markRaw(top),
        fapi: markRaw(fapi)
      }, toRefs(data5)), {}, {
        getGroupInject: function getGroupInject2() {
          return _getGroupInject(vm, parent);
        },
        refresh: function refresh() {
          ++data5.unique;
        },
        renderRule: function renderRule() {
          data5.renderRule = _toConsumableArray(rule.value || []);
        },
        updateValue: function updateValue2(value) {
          if (data5.destroyed)
            return;
          var json = JSON.stringify(value);
          if (data5.updateValue === json) {
            return;
          }
          data5.updateValue = json;
          vm.emit("update:modelValue", value);
          nextTick(function() {
            emit$form();
            if (!parent) {
              emit$topForm();
            }
          });
        }
      });
    },
    created: function created3() {
      var vm = getCurrentInstance();
      vm.emit("update:api", vm.setupState.fapi);
      vm.setupState.fc.init();
    }
  });
}
var normalMerge = ["props"];
var toArrayMerge = ["class", "style", "directives"];
var functionalMerge = ["on", "hook"];
var mergeProps2 = function mergeProps3(objects) {
  var initial = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  var opt = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
  var _normalMerge = [].concat(normalMerge, _toConsumableArray(opt["normal"] || []));
  var _toArrayMerge = [].concat(toArrayMerge, _toConsumableArray(opt["array"] || []));
  var _functionalMerge = [].concat(functionalMerge, _toConsumableArray(opt["functional"] || []));
  var propsMerge = opt["props"] || [];
  return objects.reduce(function(a, b) {
    for (var key in b) {
      if (a[key]) {
        if (propsMerge.indexOf(key) > -1) {
          a[key] = mergeProps3([b[key]], a[key]);
        } else if (_normalMerge.indexOf(key) > -1) {
          a[key] = _objectSpread2(_objectSpread2({}, a[key]), b[key]);
        } else if (_toArrayMerge.indexOf(key) > -1) {
          var arrA = a[key] instanceof Array ? a[key] : [a[key]];
          var arrB = b[key] instanceof Array ? b[key] : [b[key]];
          a[key] = [].concat(_toConsumableArray(arrA), _toConsumableArray(arrB));
        } else if (_functionalMerge.indexOf(key) > -1) {
          for (var event in b[key]) {
            if (a[key][event]) {
              var _arrA = a[key][event] instanceof Array ? a[key][event] : [a[key][event]];
              var _arrB = b[key][event] instanceof Array ? b[key][event] : [b[key][event]];
              a[key][event] = [].concat(_toConsumableArray(_arrA), _toConsumableArray(_arrB));
            } else {
              a[key][event] = b[key][event];
            }
          }
        } else if (key === "hook") {
          for (var hook in b[key]) {
            if (a[key][hook]) {
              a[key][hook] = mergeFn(a[key][hook], b[key][hook]);
            } else {
              a[key][hook] = b[key][hook];
            }
          }
        } else {
          a[key] = b[key];
        }
      } else {
        if (_normalMerge.indexOf(key) > -1 || _functionalMerge.indexOf(key) > -1 || propsMerge.indexOf(key) > -1) {
          a[key] = _objectSpread2({}, b[key]);
        } else if (_toArrayMerge.indexOf(key) > -1) {
          a[key] = b[key] instanceof Array ? _toConsumableArray(b[key]) : _typeof(b[key]) === "object" ? _objectSpread2({}, b[key]) : b[key];
        } else
          a[key] = b[key];
      }
    }
    return a;
  }, initial);
};
var mergeFn = function mergeFn2(fn1, fn2) {
  return function() {
    fn1 && fn1.apply(this, arguments);
    fn2 && fn2.apply(this, arguments);
  };
};
var keyAttrs = ["type", "slot", "ignore", "emitPrefix", "value", "name", "native", "hidden", "display", "inject", "options", "emit", "link", "prefix", "suffix", "update", "sync", "optionsTo", "key", "slotUpdate", "computed", "preview", "component", "cache", "modelEmit"];
var arrayAttrs = ["validate", "children", "control"];
var normalAttrs = ["effect", "deep"];
function attrs() {
  return [].concat(keyAttrs, _toConsumableArray(normalMerge), _toConsumableArray(toArrayMerge), _toConsumableArray(functionalMerge), arrayAttrs, normalAttrs);
}
function format(type2, msg, rule) {
  return "[form-create ".concat(type2, "]: ").concat(msg) + (rule ? "\n\nrule: " + JSON.stringify(rule.getRule ? rule.getRule() : rule) : "");
}
function err(msg, rule) {
  console.error(format("err", msg, rule));
}
function logError(e) {
  err(e.toString());
  console.error(e);
}
function toCase(str) {
  var to = str.replace(/(-[a-z])/g, function(v) {
    return v.replace("-", "").toLocaleUpperCase();
  });
  return lower(to);
}
function lower(str) {
  return str.replace(str[0], str[0].toLowerCase());
}
var PREFIX = "[[FORM-CREATE-PREFIX-";
var SUFFIX = "-FORM-CREATE-SUFFIX]]";
function toJson(obj, space) {
  return JSON.stringify(deepExtend(Array.isArray(obj) ? [] : {}, obj, true), function(key, val) {
    if (val && val._isVue === true)
      return void 0;
    if (typeof val !== "function") {
      return val;
    }
    if (val.__json) {
      return val.__json;
    }
    if (val.__origin)
      val = val.__origin;
    if (val.__emit)
      return void 0;
    return PREFIX + val + SUFFIX;
  }, space);
}
function makeFn(fn) {
  return new Function("return " + fn)();
}
function parseFn(fn, mode) {
  if (fn && is.String(fn) && fn.length > 4) {
    var v = fn.trim();
    var flag = false;
    try {
      if (v.indexOf(SUFFIX) > 0 && v.indexOf(PREFIX) === 0) {
        v = v.replace(SUFFIX, "").replace(PREFIX, "");
        flag = true;
      } else if (v.indexOf("$FN:") === 0) {
        v = v.substring(4);
        flag = true;
      } else if (v.indexOf("$EXEC:") === 0) {
        v = v.substring(6);
        flag = true;
      } else if (v.indexOf("$GLOBAL:") === 0) {
        var name2 = v.substring(8);
        v = function v2() {
          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          var callback = args[0].api.getGlobalEvent(name2);
          if (callback) {
            return callback.call.apply(callback, [this].concat(args));
          }
          return void 0;
        };
        v.__json = fn;
        v.__inject = true;
        return v;
      } else if (v.indexOf("$FNX:") === 0) {
        v = makeFn("function($inject){" + v.substring(5) + "}");
        v.__json = fn;
        v.__inject = true;
        return v;
      } else if (!mode && v.indexOf("function ") === 0 && v !== "function ") {
        flag = true;
      } else if (!mode && v.indexOf("function(") === 0 && v !== "function(") {
        flag = true;
      }
      if (!flag)
        return fn;
      var val = makeFn(v);
      val.__json = fn;
      return val;
    } catch (e) {
      err("解析失败:".concat(v, "\n\nerr: ").concat(e));
      return void 0;
    }
  }
  return fn;
}
function parseJson(json, mode) {
  return JSON.parse(json, function(k, v) {
    if (is.Undef(v) || !v.indexOf)
      return v;
    return parseFn(v, mode);
  });
}
function enumerable(value, writable) {
  return {
    value,
    enumerable: false,
    configurable: false,
    writable: !!writable
  };
}
function copyRule(rule, mode) {
  return copyRules([rule], mode || false)[0];
}
function copyRules(rules, mode) {
  return deepExtend([], _toConsumableArray(rules), mode || false);
}
function mergeRule(rule, merge) {
  mergeProps2(Array.isArray(merge) ? merge : [merge], rule, {
    array: arrayAttrs,
    normal: normalAttrs
  });
  return rule;
}
function getRule(rule) {
  var r = is.Function(rule.getRule) ? rule.getRule() : rule;
  if (!r.type) {
    r.type = "input";
  }
  return r;
}
function mergeGlobal(target, merge) {
  if (!target)
    return merge;
  Object.keys(merge || {}).forEach(function(k) {
    if (merge[k]) {
      target[k] = mergeRule(target[k] || {}, merge[k]);
    }
  });
  return target;
}
function funcProxy(that, proxy) {
  Object.defineProperties(that, Object.keys(proxy).reduce(function(initial, k) {
    initial[k] = {
      get: function get() {
        return proxy[k]();
      }
    };
    return initial;
  }, {}));
}
function byCtx(rule) {
  return rule.__fc__ || (rule.__origin__ ? rule.__origin__.__fc__ : null);
}
function invoke(fn, def) {
  try {
    def = fn();
  } catch (e) {
    logError(e);
  }
  return def;
}
function makeSlotBag() {
  var slotBag = {};
  var slotName = function slotName2(n) {
    return n || "default";
  };
  return {
    setSlot: function setSlot(slot, vnFn) {
      slot = slotName(slot);
      if (!vnFn || Array.isArray(vnFn) && vnFn.length)
        return;
      if (!slotBag[slot])
        slotBag[slot] = [];
      slotBag[slot].push(vnFn);
    },
    getSlot: function getSlot3(slot, val) {
      slot = slotName(slot);
      var children = [];
      (slotBag[slot] || []).forEach(function(fn) {
        if (Array.isArray(fn)) {
          children.push.apply(children, _toConsumableArray(fn));
        } else if (is.Function(fn)) {
          var res = fn.apply(void 0, _toConsumableArray(val || []));
          if (Array.isArray(res)) {
            children.push.apply(children, _toConsumableArray(res));
          } else {
            children.push(res);
          }
        } else if (!is.Undef(fn)) {
          children.push(fn);
        }
      });
      return children;
    },
    getSlots: function getSlots() {
      var _this = this;
      var slots = {};
      Object.keys(slotBag).forEach(function(k) {
        slots[k] = function() {
          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          return _this.getSlot(k, args);
        };
      });
      return slots;
    },
    slotLen: function slotLen(slot) {
      slot = slotName(slot);
      return slotBag[slot] ? slotBag[slot].length : 0;
    },
    mergeBag: function mergeBag(bag) {
      var _this2 = this;
      if (!bag)
        return this;
      var slots = is.Function(bag.getSlots) ? bag.getSlots() : bag;
      if (Array.isArray(bag) || isVNode(bag)) {
        this.setSlot(void 0, function() {
          return bag;
        });
      } else {
        Object.keys(slots).forEach(function(k) {
          _this2.setSlot(k, slots[k]);
        });
      }
      return this;
    }
  };
}
function toProps(rule) {
  var prop = _objectSpread2({}, rule.props || {});
  Object.keys(rule.on || {}).forEach(function(k) {
    if (k.indexOf("-") > 0) {
      k = toCase(k);
    }
    var name2 = "on".concat(upper(k));
    if (Array.isArray(prop[name2])) {
      prop[name2] = [].concat(_toConsumableArray(prop[name2]), [rule.on[k]]);
    } else if (prop[name2]) {
      prop[name2] = [prop[name2], rule.on[k]];
    } else {
      prop[name2] = rule.on[k];
    }
  });
  prop.key = rule.key;
  prop.ref = rule.ref;
  prop["class"] = rule["class"];
  prop.id = rule.id;
  prop.style = rule.style;
  if (prop.slot)
    delete prop.slot;
  return prop;
}
function setPrototypeOf(o, proto) {
  Object.setPrototypeOf(o, proto);
  return o;
}
var changeType = function changeType2(a, b) {
  if (typeof a === "string") {
    return String(b);
  } else if (typeof a === "number") {
    return Number(b);
  }
  return b;
};
var condition = {
  "==": function _(a, b) {
    return JSON.stringify(a) === JSON.stringify(changeType(a, b));
  },
  "!=": function _2(a, b) {
    return !condition["=="](a, b);
  },
  ">": function _3(a, b) {
    return a > b;
  },
  ">=": function _4(a, b) {
    return a >= b;
  },
  "<": function _5(a, b) {
    return a < b;
  },
  "<=": function _6(a, b) {
    return a <= b;
  },
  on: function on(a, b) {
    return a && a.indexOf && a.indexOf(changeType(a[0], b)) > -1;
  },
  notOn: function notOn(a, b) {
    return !condition.on(a, b);
  },
  "in": function _in(a, b) {
    return b && b.indexOf && b.indexOf(a) > -1;
  },
  notIn: function notIn(a, b) {
    return !condition["in"](a, b);
  },
  between: function between(a, b) {
    return a > b[0] && a < b[1];
  },
  notBetween: function notBetween(a, b) {
    return a < b[0] || a > b[1];
  },
  empty: function empty2(a) {
    return is.empty(a);
  },
  notEmpty: function notEmpty(a) {
    return !is.empty(a);
  },
  pattern: function pattern(a, b) {
    return new RegExp(b, "g").test(a);
  }
};
function deepGet(val, split) {
  (Array.isArray(split) ? split : (split || "").split(".")).forEach(function(k) {
    if (val != null) {
      val = val[k];
    }
  });
  return val;
}
function extractVar(str) {
  var regex = /{{\s*(.*?)\s*}}/g;
  var match;
  var matches = {};
  while ((match = regex.exec(str)) !== null) {
    if (match[1]) {
      matches[match[1]] = true;
    }
  }
  return Object.keys(matches);
}
function baseRule() {
  return {
    props: {},
    on: {},
    options: [],
    children: [],
    hidden: false,
    display: true,
    value: void 0
  };
}
function creatorFactory(name2, init4) {
  return function(title, field, value) {
    var props = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};
    var maker2 = new Creator(name2, title, field, value, props);
    if (init4) {
      if (is.Function(init4))
        init4(maker2);
      else
        maker2.props(init4);
    }
    return maker2;
  };
}
function Creator(type2, title, field, value, props) {
  this._data = extend(baseRule(), {
    type: type2,
    title,
    field,
    value,
    props: props || {}
  });
  this.event = this.on;
}
extend(Creator.prototype, {
  getRule: function getRule2() {
    return this._data;
  },
  setProp: function setProp(key, value) {
    $set(this._data, key, value);
    return this;
  },
  modelField: function modelField(field) {
    this._data.modelField = field;
    return this;
  },
  _clone: function _clone() {
    var clone = new this.constructor();
    clone._data = copyRule(this._data);
    return clone;
  }
});
function appendProto(attrs2) {
  attrs2.forEach(function(name2) {
    Creator.prototype[name2] = function(key) {
      mergeRule(this._data, _defineProperty({}, name2, arguments.length < 2 ? key : _defineProperty({}, key, arguments[1])));
      return this;
    };
  });
}
appendProto(attrs());
var commonMaker = creatorFactory("");
function create(type2, field, title) {
  var make = commonMaker("", field);
  make._data.type = type2;
  make._data.title = title;
  return make;
}
function makerFactory() {
  return {
    create,
    factory: creatorFactory
  };
}
function getError(action, option, xhr) {
  var msg = "fail to ".concat(action, " ").concat(xhr.status, "'");
  var err2 = new Error(msg);
  err2.status = xhr.status;
  err2.url = action;
  return err2;
}
function getBody(xhr) {
  var text = xhr.responseText || xhr.response;
  if (!text) {
    return text;
  }
  try {
    return JSON.parse(text);
  } catch (e) {
    return text;
  }
}
function fetch$1(option) {
  if (typeof XMLHttpRequest === "undefined") {
    return;
  }
  var xhr = new XMLHttpRequest();
  var action = option.action || "";
  if (option.query) {
    var queryString = new URLSearchParams(option.query).toString();
    if (action.includes("?")) {
      action += "&".concat(queryString);
    } else {
      action += "?".concat(queryString);
    }
  }
  xhr.onerror = function error(e) {
    option.onError(e);
  };
  xhr.onload = function onload() {
    if (xhr.status < 200 || xhr.status >= 300) {
      return option.onError(getError(action, option, xhr), getBody(xhr));
    }
    option.onSuccess(getBody(xhr));
  };
  xhr.open(option.method || "get", action, true);
  var formData3;
  if (option.data) {
    if ((option.dataType || "").toLowerCase() !== "json") {
      formData3 = new FormData();
      Object.keys(option.data).map(function(key) {
        formData3.append(key, option.data[key]);
      });
    } else {
      formData3 = JSON.stringify(option.data);
      xhr.setRequestHeader("content-type", "application/json");
    }
  }
  if (option.withCredentials && "withCredentials" in xhr) {
    xhr.withCredentials = true;
  }
  var headers = option.headers || {};
  Object.keys(headers).forEach(function(item) {
    if (headers[item] != null) {
      xhr.setRequestHeader(item, headers[item]);
    }
  });
  xhr.send(formData3);
}
function asyncFetch(config, _fetch, api) {
  return new Promise(function(resolve, reject) {
    (_fetch || fetch$1)(_objectSpread2(_objectSpread2({}, config), {}, {
      onSuccess: function onSuccess(res) {
        var fn = function fn2(v) {
          return v;
        };
        var parse = parseFn(config.parse);
        if (is.Function(parse)) {
          fn = parse;
        } else if (parse && is.String(parse)) {
          fn = function fn2(v) {
            return deepGet(v, parse);
          };
        }
        resolve(fn(res, void 0, api));
      },
      onError: function onError(err2) {
        reject(err2);
      }
    }));
  });
}
function copy(value) {
  return deepCopy(value);
}
function Api(h2) {
  function tidyFields(fields) {
    if (is.Undef(fields))
      fields = h2.fields();
    else if (!Array.isArray(fields))
      fields = [fields];
    return fields;
  }
  function props(fields, key, val) {
    tidyFields(fields).forEach(function(field) {
      h2.getCtxs(field).forEach(function(ctx) {
        $set(ctx.rule, key, val);
        h2.$render.clearCache(ctx);
      });
    });
  }
  function allSubForm() {
    var subs = h2.subForm;
    return Object.keys(subs).reduce(function(initial, k) {
      var sub = subs[k];
      if (!sub)
        return initial;
      if (Array.isArray(sub))
        initial.push.apply(initial, _toConsumableArray(sub));
      else
        initial.push(sub);
      return initial;
    }, []);
  }
  var api = {
    get config() {
      return h2.options;
    },
    set config(val) {
      h2.fc.options.value = val;
    },
    get options() {
      return h2.options;
    },
    set options(val) {
      h2.fc.options.value = val;
    },
    get form() {
      return h2.form;
    },
    get rule() {
      return h2.rules;
    },
    get parent() {
      return h2.vm.setupState.parent && h2.vm.setupState.parent.setupState.fapi;
    },
    get top() {
      if (api.parent) {
        return api.parent.top;
      }
      return api;
    },
    get children() {
      return allSubForm();
    },
    get siblings() {
      var inject2 = h2.vm.setupState.getGroupInject();
      if (inject2) {
        var subForm = inject2.getSubForm();
        if (Array.isArray(subForm)) {
          return _toConsumableArray(subForm);
        }
      }
      return void 0;
    },
    get index() {
      var siblings = api.siblings;
      if (siblings) {
        var idx = siblings.indexOf(api);
        return idx > -1 ? idx : void 0;
      }
      return void 0;
    },
    formData: function formData3(fields) {
      if (fields == null) {
        var data5 = {};
        Object.keys(h2.form).forEach(function(k) {
          if (h2.ignoreFields.indexOf(k) === -1) {
            data5[k] = copy(h2.form[k]);
          }
        });
        return data5;
      } else {
        return tidyFields(fields).reduce(function(initial, id2) {
          initial[id2] = api.getValue(id2);
          return initial;
        }, {});
      }
    },
    getValue: function getValue(field) {
      var ctx = h2.getFieldCtx(field);
      if (!ctx) {
        if (h2.options.appendValue !== false && hasProperty(h2.appendData, field)) {
          return copy(h2.appendData[field]);
        }
        return void 0;
      }
      return copy(ctx.rule.value);
    },
    coverValue: function coverValue(formData3) {
      var data5 = _objectSpread2({}, formData3 || {});
      h2.deferSyncValue(function() {
        h2.appendData = {};
        api.fields().forEach(function(key) {
          var ctxs = h2.fieldCtx[key];
          if (ctxs) {
            var flag = hasProperty(formData3, key);
            ctxs.forEach(function(ctx) {
              ctx.rule.value = flag ? formData3[key] : void 0;
            });
            delete data5[key];
          }
        });
        extend(h2.appendData, data5);
      }, true);
    },
    setValue: function setValue4(field) {
      var formData3 = field;
      if (arguments.length >= 2)
        formData3 = _defineProperty({}, field, arguments[1]);
      h2.deferSyncValue(function() {
        Object.keys(formData3).forEach(function(key) {
          var ctxs = h2.fieldCtx[key];
          if (!ctxs)
            return h2.appendData[key] = formData3[key];
          ctxs.forEach(function(ctx) {
            ctx.rule.value = formData3[key];
          });
        });
      }, true);
    },
    removeField: function removeField(field) {
      var ctx = h2.getCtx(field);
      h2.deferSyncValue(function() {
        h2.getCtxs(field).forEach(function(ctx2) {
          ctx2.rm();
        });
      }, true);
      return ctx ? ctx.origin : void 0;
    },
    removeRule: function removeRule2(rule) {
      var ctx = rule && byCtx(rule);
      if (!ctx)
        return;
      ctx.rm();
      return ctx.origin;
    },
    fields: function fields() {
      return h2.fields();
    },
    append: function append(rule, after, child) {
      var index = h2.sort.length - 1, rules;
      var ctx = h2.getCtx(after);
      if (ctx) {
        if (child) {
          rules = ctx.getPending("children", ctx.rule.children);
          if (!Array.isArray(rules))
            return;
          index = ctx.rule.children.length - 1;
        } else {
          index = ctx.root.indexOf(ctx.origin);
          rules = ctx.root;
        }
      } else
        rules = h2.rules;
      rules.splice(index + 1, 0, rule);
    },
    prepend: function prepend(rule, after, child) {
      var index = 0, rules;
      var ctx = h2.getCtx(after);
      if (ctx) {
        if (child) {
          rules = ctx.getPending("children", ctx.rule.children);
          if (!Array.isArray(rules))
            return;
        } else {
          index = ctx.root.indexOf(ctx.origin);
          rules = ctx.root;
        }
      } else
        rules = h2.rules;
      rules.splice(index, 0, rule);
    },
    hidden: function hidden2(state, fields) {
      props(fields, "hidden", !!state);
      h2.refresh();
    },
    hiddenStatus: function hiddenStatus(id2) {
      var ctx = h2.getCtx(id2);
      if (!ctx)
        return;
      return !!ctx.rule.hidden;
    },
    display: function display(state, fields) {
      props(fields, "display", !!state);
      h2.refresh();
    },
    displayStatus: function displayStatus(id2) {
      var ctx = h2.getCtx(id2);
      if (!ctx)
        return;
      return !!ctx.rule.display;
    },
    disabled: function disabled(_disabled, fields) {
      tidyFields(fields).forEach(function(field) {
        h2.getCtxs(field).forEach(function(ctx) {
          $set(ctx.rule.props, "disabled", !!_disabled);
        });
      });
      h2.refresh();
    },
    all: function all(origin) {
      return Object.keys(h2.ctxs).map(function(k) {
        var ctx = h2.ctxs[k];
        return origin ? ctx.origin : ctx.rule;
      });
    },
    model: function model(origin) {
      return h2.fields().reduce(function(initial, key) {
        var ctx = h2.fieldCtx[key][0];
        initial[key] = origin ? ctx.origin : ctx.rule;
        return initial;
      }, {});
    },
    component: function component(origin) {
      return Object.keys(h2.nameCtx).reduce(function(initial, key) {
        var ctx = h2.nameCtx[key].map(function(ctx2) {
          return origin ? ctx2.origin : ctx2.rule;
        });
        initial[key] = ctx.length === 1 ? ctx[0] : ctx;
        return initial;
      }, {});
    },
    bind: function bind2() {
      return api.form;
    },
    reload: function reload(rules) {
      h2.reloadRule(rules);
    },
    updateOptions: function updateOptions2(options) {
      h2.fc.updateOptions(options);
      api.refresh();
    },
    onSubmit: function onSubmit(fn) {
      api.updateOptions({
        onSubmit: fn
      });
    },
    sync: function sync(field) {
      if (Array.isArray(field)) {
        field.forEach(function(v) {
          return api.sync(v);
        });
        return;
      }
      var ctxs = is.Object(field) ? byCtx(field) : h2.getCtxs(field);
      if (!ctxs) {
        return;
      }
      ctxs = Array.isArray(ctxs) ? ctxs : [ctxs];
      ctxs.forEach(function(ctx) {
        if (!ctx.deleted) {
          var subForm = h2.subForm[ctx.id];
          if (subForm) {
            if (Array.isArray(subForm)) {
              subForm.forEach(function(form2) {
                form2.refresh();
              });
            } else if (subForm) {
              subForm.refresh();
            }
          }
          h2.$render.clearCache(ctx);
        }
      });
      h2.refresh();
    },
    refresh: function refresh() {
      allSubForm().forEach(function(sub) {
        sub.refresh();
      });
      h2.$render.clearCacheAll();
      h2.refresh();
    },
    refreshOptions: function refreshOptions() {
      h2.$manager.updateOptions(h2.options);
      api.refresh();
    },
    hideForm: function hideForm(hide) {
      h2.vm.setupState.isShow = !hide;
    },
    changeStatus: function changeStatus() {
      return h2.changeStatus;
    },
    clearChangeStatus: function clearChangeStatus() {
      h2.changeStatus = false;
    },
    updateRule: function updateRule(id2, rule) {
      h2.getCtxs(id2).forEach(function(ctx) {
        extend(ctx.rule, rule);
      });
    },
    updateRules: function updateRules(rules) {
      Object.keys(rules).forEach(function(id2) {
        api.updateRule(id2, rules[id2]);
      });
    },
    mergeRule: function mergeRule$1(id2, rule) {
      h2.getCtxs(id2).forEach(function(ctx) {
        mergeRule(ctx.rule, rule);
      });
    },
    mergeRules: function mergeRules(rules) {
      Object.keys(rules).forEach(function(id2) {
        api.mergeRule(id2, rules[id2]);
      });
    },
    getRule: function getRule3(id2, origin) {
      var ctx = h2.getCtx(id2);
      if (ctx) {
        return origin ? ctx.origin : ctx.rule;
      }
    },
    getRenderRule: function getRenderRule(id2) {
      var ctx = h2.getCtx(id2);
      if (ctx) {
        return ctx.prop;
      }
    },
    getRefRule: function getRefRule(id2) {
      var ctxs = h2.getCtxs(id2);
      if (ctxs) {
        var rules = ctxs.map(function(ctx) {
          return ctx.rule;
        });
        return rules.length === 1 ? rules[0] : rules;
      }
    },
    setEffect: function setEffect(id2, attr, value) {
      var ctx = h2.getCtx(id2);
      if (ctx && attr) {
        if (attr[0] === "$") {
          attr = attr.substr(1);
        }
        if (hasProperty(ctx.rule, "$" + attr)) {
          $set(ctx.rule, "$" + attr, value);
        }
        if (!hasProperty(ctx.rule, "effect")) {
          ctx.rule.effect = {};
        }
        $set(ctx.rule.effect, attr, value);
      }
    },
    clearEffectData: function clearEffectData2(id2, attr) {
      var ctx = h2.getCtx(id2);
      if (ctx) {
        if (attr && attr[0] === "$") {
          attr = attr.substr(1);
        }
        ctx.clearEffectData(attr);
        api.sync(id2);
      }
    },
    updateValidate: function updateValidate(id2, validate2, merge) {
      if (merge) {
        api.mergeRule(id2, {
          validate: validate2
        });
      } else {
        props(id2, "validate", validate2);
      }
    },
    updateValidates: function updateValidates(validates, merge) {
      Object.keys(validates).forEach(function(id2) {
        api.updateValidate(id2, validates[id2], merge);
      });
    },
    refreshValidate: function refreshValidate() {
      api.refresh();
    },
    resetFields: function resetFields(fields) {
      tidyFields(fields).forEach(function(field) {
        h2.getCtxs(field).forEach(function(ctx) {
          h2.$render.clearCache(ctx);
          ctx.rule.value = copy(ctx.defaultValue);
        });
      });
      nextTick(function() {
        api.clearValidateState();
      });
      if (fields == null) {
        is.Function(h2.options.onReset) && invoke(function() {
          return h2.options.onReset(api);
        });
        h2.vm.emit("reset", api);
      }
    },
    method: function method(id2, name2) {
      var el = api.el(id2);
      if (!el || !el[name2])
        throw new Error(format("err", "".concat(name2, " 方法不存在")));
      return function() {
        return el[name2].apply(el, arguments);
      };
    },
    exec: function exec(id2, name2) {
      for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {
        args[_key - 2] = arguments[_key];
      }
      return invoke(function() {
        return api.method(id2, name2).apply(void 0, args);
      });
    },
    toJson: function toJson$1(space) {
      return toJson(api.rule, space);
    },
    trigger: function trigger(id2, event) {
      var el = api.el(id2);
      for (var _len2 = arguments.length, args = new Array(_len2 > 2 ? _len2 - 2 : 0), _key2 = 2; _key2 < _len2; _key2++) {
        args[_key2 - 2] = arguments[_key2];
      }
      el && el.$emit.apply(el, [event].concat(args));
    },
    el: function el(id2) {
      var ctx = h2.getCtx(id2);
      if (ctx)
        return ctx.el || h2.vm.refs[ctx.ref];
    },
    closeModal: function closeModal(id2) {
      h2.bus.$emit("fc:closeModal:" + id2);
    },
    getSubForm: function getSubForm(field) {
      var ctx = h2.getCtx(field);
      return ctx ? h2.subForm[ctx.id] : void 0;
    },
    getChildrenRuleList: function getChildrenRuleList(id2) {
      var flag = _typeof(id2) === "object";
      var ctx = flag ? byCtx(id2) : h2.getCtx(id2);
      var rule = ctx ? ctx.rule : flag ? id2 : api.getRule(id2);
      if (!rule) {
        return [];
      }
      var rules = [];
      var findRules = function findRules2(children) {
        children && children.forEach(function(item) {
          if (_typeof(item) !== "object") {
            return;
          }
          if (item.field) {
            rules.push(item);
          }
          rules.push.apply(rules, _toConsumableArray(api.getChildrenRuleList(item)));
        });
      };
      findRules(ctx ? ctx.loadChildrenPending() : rule.children);
      return rules;
    },
    getParentRule: function getParentRule(id2) {
      var flag = _typeof(id2) === "object";
      var ctx = flag ? byCtx(id2) : h2.getCtx(id2);
      return ctx.parent.rule;
    },
    getParentSubRule: function getParentSubRule(id2) {
      var flag = _typeof(id2) === "object";
      var ctx = flag ? byCtx(id2) : h2.getCtx(id2);
      if (ctx) {
        var group = ctx.getParentGroup();
        if (group) {
          return group.rule;
        }
      }
    },
    getChildrenFormData: function getChildrenFormData(id2) {
      var rules = api.getChildrenRuleList(id2);
      return rules.reduce(function(formData3, rule) {
        formData3[rule.field] = copy(rule.value);
        return formData3;
      }, {});
    },
    setChildrenFormData: function setChildrenFormData(id2, formData3, cover) {
      var rules = api.getChildrenRuleList(id2);
      h2.deferSyncValue(function() {
        rules.forEach(function(rule) {
          if (hasProperty(formData3, rule.field)) {
            rule.value = formData3[rule.field];
          } else if (cover) {
            rule.value = void 0;
          }
        });
      });
    },
    getGlobalEvent: function getGlobalEvent(name2) {
      var event = api.options.globalEvent[name2];
      if (event) {
        if (_typeof(event) === "object") {
          event = event.handle;
        }
        return parseFn(event);
      }
      return void 0;
    },
    getGlobalData: function getGlobalData(name2) {
      return new Promise(function(resolve, inject2) {
        var config = api.options.globalData[name2];
        if (!config) {
          resolve(h2.fc.loadData[name2]);
        }
        if (config.type === "fetch") {
          api.fetch(config).then(function(res) {
            resolve(res);
          })["catch"](inject2);
        } else {
          resolve(config.data);
        }
      });
    },
    nextTick: function nextTick2(fn) {
      h2.bus.$once("next-tick", fn);
      h2.refresh();
    },
    nextRefresh: function nextRefresh(fn) {
      h2.nextRefresh();
      fn && invoke(fn);
    },
    deferSyncValue: function deferSyncValue(fn, sync) {
      h2.deferSyncValue(fn, sync);
    },
    emit: function emit(name2) {
      var _h$vm;
      for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {
        args[_key3 - 1] = arguments[_key3];
      }
      (_h$vm = h2.vm).emit.apply(_h$vm, [name2].concat(args));
    },
    bus: h2.bus,
    fetch: function fetch3(opt) {
      return new Promise(function(resolve, reject) {
        opt = deepCopy(opt);
        opt = h2.loadFetchVar(opt);
        h2.beforeFetch(opt).then(function() {
          return asyncFetch(opt, h2.fc.create.fetch, api).then(function(res) {
            invoke(function() {
              return opt.onSuccess && opt.onSuccess(res);
            });
            resolve(res);
          })["catch"](function(e) {
            invoke(function() {
              return opt.onError && opt.onError(e);
            });
            reject(e);
          });
        });
      });
    },
    watchFetch: function watchFetch(opt, callback, error) {
      return h2.fc.watchLoadData(function(get, change) {
        var _opt = deepCopy(opt);
        _opt = h2.loadFetchVar(_opt, get);
        h2.beforeFetch(_opt).then(function() {
          return asyncFetch(_opt, h2.fc.create.fetch, api).then(function(res) {
            invoke(function() {
              return _opt.onSuccess && _opt.onSuccess(res);
            });
            callback && callback(res, change);
          })["catch"](function(e) {
            invoke(function() {
              return _opt.onError && _opt.onError(e);
            });
            error && error(e);
          });
        });
      });
    },
    getData: function getData(id2, def) {
      return h2.fc.getLoadData(id2, def);
    },
    setData: function setData(id2, data5, isGlobal) {
      return h2.fc.setData(id2, data5, isGlobal);
    },
    refreshData: function refreshData(id2) {
      return h2.fc.refreshData(id2);
    },
    t: function t3(id2, params) {
      return h2.fc.t(id2, params);
    },
    getLocale: function getLocale() {
      return h2.fc.getLocale();
    },
    helper: {
      tidyFields,
      props
    }
  };
  ["on", "once", "off"].forEach(function(n) {
    api[n] = function() {
      var _h$bus;
      (_h$bus = h2.bus)["$".concat(n)].apply(_h$bus, arguments);
    };
  });
  api.changeValue = api.changeField = api.setValue;
  return api;
}
function useCache(Render2) {
  extend(Render2.prototype, {
    initCache: function initCache() {
      this.clearCacheAll();
    },
    clearCache: function clearCache(ctx) {
      if (ctx.rule.cache) {
        return;
      }
      if (!this.cache[ctx.id]) {
        if (ctx.parent) {
          this.clearCache(ctx.parent);
        }
        return;
      }
      if (this.cache[ctx.id].use === true || this.cache[ctx.id].parent) {
        this.$handle.refresh();
      }
      if (this.cache[ctx.id].parent) {
        this.clearCache(this.cache[ctx.id].parent);
      }
      this.cache[ctx.id] = null;
    },
    clearCacheAll: function clearCacheAll() {
      this.cache = {};
    },
    setCache: function setCache(ctx, vnode, parent) {
      this.cache[ctx.id] = {
        vnode,
        use: false,
        parent,
        slot: ctx.rule.slot
      };
    },
    getCache: function getCache(ctx) {
      var cache2 = this.cache[ctx.id];
      if (cache2) {
        cache2.use = true;
        return cache2.vnode;
      }
      return void 0;
    }
  });
}
function toString(val) {
  return val == null ? "" : _typeof(val) === "object" ? JSON.stringify(val, null, 2) : String(val);
}
var id$2 = 0;
function uniqueId() {
  var num = 370 + ++id$2;
  return "F" + Math.random().toString(36).substr(3, 3) + Number("".concat(Date.now())).toString(36) + num.toString(36) + "c";
}
function deepSet(data5, idx, val) {
  var _data = data5, to;
  (idx || "").split(".").forEach(function(v) {
    if (to) {
      if (!_data[to] || _typeof(_data[to]) != "object") {
        _data[to] = {};
      }
      _data = _data[to];
    }
    to = v;
  });
  _data[to] = val;
  return _data;
}
function useRender$1(Render2) {
  extend(Render2.prototype, {
    initRender: function initRender() {
      this.cacheConfig = {};
    },
    getTypeSlot: function getTypeSlot(ctx) {
      var _fn = function _fn2(vm) {
        if (vm) {
          var slot = void 0;
          if (ctx.rule.field) {
            slot = vm.slots["field-" + toLine(ctx.rule.field)] || vm.slots["field-" + ctx.rule.field];
          }
          if (!slot) {
            slot = vm.slots["type-" + toLine(ctx.type)] || vm.slots["type-" + ctx.type];
          }
          if (slot) {
            return slot;
          }
          return _fn2(vm.setupState.parent);
        }
      };
      return _fn(this.vm);
    },
    render: function render17() {
      var _this = this;
      if (!this.vm.setupState.isShow) {
        return;
      }
      this.$manager.beforeRender();
      var slotBag = makeSlotBag();
      this.sort.forEach(function(k) {
        _this.renderSlot(slotBag, _this.$handle.ctxs[k]);
      });
      return this.$manager.render(slotBag);
    },
    renderSlot: function renderSlot(slotBag, ctx, parent) {
      if (this.isFragment(ctx)) {
        ctx.initProp();
        this.mergeGlobal(ctx);
        ctx.initNone();
        var slots = this.renderChildren(ctx.loadChildrenPending(), ctx);
        var def = slots["default"];
        def && slotBag.setSlot(ctx.rule.slot, function() {
          return def();
        });
        delete slots["default"];
        slotBag.mergeBag(slots);
      } else {
        slotBag.setSlot(ctx.rule.slot, this.renderCtx(ctx, parent));
      }
    },
    mergeGlobal: function mergeGlobal2(ctx) {
      var _this2 = this;
      var g = this.$handle.options.global;
      if (!g)
        return;
      if (!this.cacheConfig[ctx.trueType]) {
        this.cacheConfig[ctx.trueType] = computed(function() {
          var g2 = _this2.$handle.options.global;
          return mergeRule({}, [g2["*"], g2[ctx.originType] || g2[ctx.type] || g2[ctx.type] || {}]);
        });
      }
      ctx.prop = mergeRule({}, [this.cacheConfig[ctx.trueType].value, ctx.prop]);
    },
    setOptions: function setOptions(ctx) {
      var opt = ctx.loadPending({
        key: "options",
        origin: ctx.prop.options,
        def: []
      });
      ctx.prop.options = opt;
      if (ctx.prop.optionsTo && opt) {
        deepSet(ctx.prop, ctx.prop.optionsTo, opt);
      }
    },
    deepSet: function deepSet$1(ctx) {
      var deep = ctx.rule.deep;
      deep && Object.keys(deep).sort(function(a, b) {
        return a.length < b.length ? -1 : 1;
      }).forEach(function(str) {
        deepSet(ctx.prop, str, deep[str]);
      });
    },
    parseSide: function parseSide(side, ctx) {
      return is.Object(side) ? mergeRule({
        props: {
          formCreateInject: ctx.prop.props.formCreateInject
        }
      }, side) : side;
    },
    renderSides: function renderSides(vn, ctx, temp) {
      var prop = ctx[temp ? "rule" : "prop"];
      return [this.renderRule(this.parseSide(prop.prefix, ctx)), vn, this.renderRule(this.parseSide(prop.suffix, ctx))];
    },
    renderId: function renderId(name2, type2) {
      var _this3 = this;
      var ctxs = this.$handle[type2 === "field" ? "fieldCtx" : "nameCtx"][name2];
      return ctxs ? ctxs.map(function(ctx) {
        return _this3.renderCtx(ctx, ctx.parent);
      }) : void 0;
    },
    renderCtx: function renderCtx(ctx, parent) {
      var _this4 = this;
      try {
        if (ctx.type === "hidden")
          return;
        var rule = ctx.rule;
        if (!this.cache[ctx.id] || this.cache[ctx.id].slot !== rule.slot) {
          var vn;
          ctx.initProp();
          this.mergeGlobal(ctx);
          ctx.initNone();
          this.$manager.tidyRule(ctx);
          this.deepSet(ctx);
          this.setOptions(ctx);
          this.ctxProp(ctx);
          var prop = ctx.prop;
          prop.preview = !!(prop.preview != null ? prop.preview : this.$handle.preview);
          prop.props.formCreateInject = this.injectProp(ctx);
          var cacheFlag = prop.cache !== false;
          var preview2 = prop.preview;
          if (prop.hidden) {
            this.setCache(ctx, void 0, parent);
            return;
          }
          vn = function vn2() {
            for (var _len = arguments.length, slotValue = new Array(_len), _key = 0; _key < _len; _key++) {
              slotValue[_key] = arguments[_key];
            }
            var inject2 = {
              rule,
              prop,
              preview: preview2,
              api: _this4.$handle.api,
              model: prop.model || {},
              slotValue
            };
            if (slotValue.length && rule.slotUpdate) {
              invoke(function() {
                return rule.slotUpdate(inject2);
              });
            }
            var children = {};
            var _load = ctx.loadChildrenPending();
            if (ctx.parser.renderChildren) {
              children = ctx.parser.renderChildren(_load, ctx);
            } else if (ctx.parser.loadChildren !== false) {
              children = _this4.renderChildren(_load, ctx);
            }
            var slot = _this4.getTypeSlot(ctx);
            var _vn;
            if (slot) {
              inject2.children = children;
              _vn = slot(inject2);
            } else {
              _vn = preview2 ? ctx.parser.preview(copy$1(children), ctx) : ctx.parser.render(copy$1(children), ctx);
            }
            _vn = _this4.renderSides(_vn, ctx);
            if (!(!ctx.input && is.Undef(prop["native"])) && prop["native"] !== true) {
              _this4.fc.targetFormDriver("updateWrap", ctx);
              _vn = _this4.$manager.makeWrap(ctx, _vn);
            }
            if (ctx.none) {
              if (Array.isArray(_vn)) {
                _vn = _vn.map(function(v) {
                  if (!v || !v.__v_isVNode) {
                    return v;
                  }
                  return _this4.none(v);
                });
              } else {
                _vn = _this4.none(_vn);
              }
            }
            cacheFlag && _this4.setCache(ctx, function() {
              return _this4.stable(_vn);
            }, parent);
            return _vn;
          };
          this.setCache(ctx, vn, parent);
        }
        return function() {
          var cache2 = _this4.getCache(ctx);
          if (cache2) {
            return cache2.apply(void 0, arguments);
          } else if (_this4.cache[ctx.id]) {
            return;
          }
          var _vn = _this4.renderCtx(ctx, ctx.parent);
          if (_vn) {
            return _vn();
          }
        };
      } catch (e) {
        console.error(e);
        return;
      }
    },
    none: function none(vn) {
      if (vn) {
        vn.props["class"] = this.mergeClass(vn.props["class"], "fc-none");
        return vn;
      }
    },
    mergeClass: function mergeClass(target, value) {
      if (Array.isArray(target)) {
        target.push(value);
      } else {
        return target ? [target, value] : value;
      }
      return target;
    },
    stable: function stable(vn) {
      var _this5 = this;
      var list = Array.isArray(vn) ? vn : [vn];
      list.forEach(function(v) {
        if (v && v.__v_isVNode && v.children && _typeof(v.children) === "object") {
          v.children.$stable = true;
          _this5.stable(v.children);
        }
      });
      return vn;
    },
    getModelField: function getModelField(ctx) {
      return ctx.prop.modelField || ctx.parser.modelField || this.fc.modelFields[this.vNode.aliasMap[ctx.type]] || this.fc.modelFields[ctx.type] || this.fc.modelFields[ctx.originType] || "modelValue";
    },
    isFragment: function isFragment(ctx) {
      return ctx.type === "fragment" || ctx.type === "template";
    },
    injectProp: function injectProp(ctx) {
      var _this6 = this;
      var state = this.vm.setupState;
      if (!state.ctxInject[ctx.id]) {
        state.ctxInject[ctx.id] = {
          api: this.$handle.api,
          form: this.fc.create,
          subForm: function subForm(_subForm) {
            _this6.$handle.addSubForm(ctx, _subForm);
          },
          getSubForm: function getSubForm() {
            return _this6.$handle.subForm[ctx.id];
          },
          slots: function slots() {
            return _this6.vm.setupState.top.slots;
          },
          options: [],
          children: [],
          preview: false,
          id: ctx.id,
          field: ctx.field,
          rule: ctx.rule,
          input: ctx.input,
          updateValue: function updateValue2(data5) {
            _this6.$handle.onUpdateValue(ctx, data5);
          }
        };
      }
      var inject2 = state.ctxInject[ctx.id];
      extend(inject2, {
        preview: ctx.prop.preview,
        options: ctx.prop.options,
        children: ctx.loadChildrenPending()
      });
      return inject2;
    },
    ctxProp: function ctxProp(ctx) {
      var _this7 = this;
      var ref2 = ctx.ref, key = ctx.key, rule = ctx.rule;
      this.$manager.mergeProp(ctx);
      ctx.parser.mergeProp(ctx);
      var props = [{
        ref: ref2,
        key: rule.key || "".concat(key, "fc"),
        slot: void 0,
        on: {
          vnodeMounted: function vnodeMounted(vn) {
            vn.el.__rule__ = ctx.rule;
            _this7.onMounted(ctx, vn.el);
          },
          "fc.updateValue": function fcUpdateValue(data5) {
            _this7.$handle.onUpdateValue(ctx, data5);
          },
          "fc.el": function fcEl(el) {
            ctx.exportEl = el;
            if (el) {
              (el.$el || el).__rule__ = ctx.rule;
            }
          }
        }
      }];
      if (ctx.input) {
        if (this.vm.props.disabled === true) {
          ctx.prop.props.disabled = true;
        }
        var field = this.getModelField(ctx);
        var model = {
          callback: function callback(value) {
            _this7.onInput(ctx, value);
          },
          modelField: field,
          value: this.$handle.getFormData(ctx)
        };
        props.push({
          on: _objectSpread2(_defineProperty({}, "update:".concat(field), model.callback), ctx.prop.modelEmit ? _defineProperty({}, ctx.prop.modelEmit, function() {
            return _this7.onEmitInput(ctx);
          }) : {}),
          props: _defineProperty({}, field, model.value)
        });
        ctx.prop.model = model;
      }
      mergeProps2(props, ctx.prop);
      return ctx.prop;
    },
    onMounted: function onMounted2(ctx, el) {
      ctx.el = this.vm.refs[ctx.ref] || el;
      ctx.parser.mounted(ctx);
      this.$handle.effect(ctx, "mounted");
      this.$handle.targetHook(ctx, "mounted");
    },
    onInput: function onInput(ctx, value) {
      if (ctx.prop.modelEmit) {
        this.$handle.onBaseInput(ctx, value);
        return;
      }
      this.$handle.onInput(ctx, value);
    },
    onEmitInput: function onEmitInput(ctx) {
      this.$handle.setValue(ctx, ctx.parser.toValue(ctx.modelValue, ctx), ctx.modelValue);
    },
    renderChildren: function renderChildren2(children, ctx) {
      var _this8 = this;
      if (!is.trueArray(children))
        return {};
      var slotBag = makeSlotBag();
      children.map(function(child) {
        if (!child)
          return;
        if (is.String(child))
          return slotBag.setSlot(null, child);
        if (child.__fc__) {
          return _this8.renderSlot(slotBag, child.__fc__, ctx);
        }
        if (child.type) {
          nextTick(function() {
            _this8.$handle.loadChildren(children, ctx);
            _this8.$handle.refresh();
          });
        }
      });
      return slotBag.getSlots();
    },
    defaultRender: function defaultRender(ctx, children) {
      var prop = ctx.prop;
      if (prop.component) {
        if (typeof prop.component === "string") {
          return this.vNode.make(prop.component, prop, children);
        } else {
          return this.vNode.makeComponent(prop.component, prop, children);
        }
      }
      if (this.vNode[ctx.type])
        return this.vNode[ctx.type](prop, children);
      if (this.vNode[ctx.originType])
        return this.vNode[ctx.originType](prop, children);
      return this.vNode.make(lower(prop.type), prop, children);
    },
    renderRule: function renderRule(rule, children, origin) {
      var _this9 = this;
      if (!rule)
        return void 0;
      if (is.String(rule))
        return rule;
      var type2;
      if (origin) {
        type2 = rule.type;
      } else {
        type2 = rule.is;
        if (rule.type) {
          type2 = toCase(rule.type);
          var alias2 = this.vNode.aliasMap[type2];
          if (alias2)
            type2 = toCase(alias2);
        }
      }
      if (!type2)
        return void 0;
      var slotBag = makeSlotBag();
      if (is.trueArray(rule.children)) {
        rule.children.forEach(function(v) {
          v && slotBag.setSlot(v === null || v === void 0 ? void 0 : v.slot, function() {
            return _this9.renderRule(v);
          });
        });
      }
      var props = _objectSpread2({}, rule);
      delete props.type;
      delete props.is;
      return this.vNode.make(type2, props, slotBag.mergeBag(children).getSlots());
    }
  });
}
var id$1 = 1;
function Render(handle) {
  extend(this, {
    $handle: handle,
    fc: handle.fc,
    vm: handle.vm,
    $manager: handle.$manager,
    vNode: new handle.fc.CreateNode(handle.vm),
    id: id$1++
  });
  funcProxy(this, {
    options: function options() {
      return handle.options;
    },
    sort: function sort() {
      return handle.sort;
    }
  });
  this.initCache();
  this.initRender();
}
useCache(Render);
useRender$1(Render);
function useInject(Handler2) {
  extend(Handler2.prototype, {
    parseInjectEvent: function parseInjectEvent(rule, on2) {
      var inject2 = rule.inject || this.options.injectEvent;
      return this.parseEventLst(rule, on2, inject2);
    },
    parseEventLst: function parseEventLst(rule, data5, inject2, deep) {
      var _this = this;
      Object.keys(data5).forEach(function(k) {
        var fn = _this.parseEvent(rule, data5[k], inject2, deep);
        if (fn) {
          data5[k] = fn;
        }
      });
      return data5;
    },
    parseEvent: function parseEvent(rule, fn, inject2, deep) {
      if (is.Function(fn) && (inject2 !== false && !is.Undef(inject2) || fn.__inject)) {
        return this.inject(rule, fn, inject2);
      } else if (!deep && Array.isArray(fn) && fn[0] && (is.String(fn[0]) || is.Function(fn[0]))) {
        return this.parseEventLst(rule, fn, inject2, true);
      } else if (is.String(fn)) {
        var val = parseFn(fn);
        if (val && fn !== val) {
          return val.__inject ? this.parseEvent(rule, val, inject2, true) : val;
        }
      }
    },
    parseEmit: function parseEmit(ctx) {
      var _this2 = this;
      var event = {}, rule = ctx.rule, emitPrefix = rule.emitPrefix, field = rule.field, name2 = rule.name, inject2 = rule.inject;
      var emit = rule.emit || [];
      if (is.trueArray(emit)) {
        emit.forEach(function(eventName) {
          if (!eventName)
            return;
          var eventInject;
          var emitKey = emitPrefix || field || name2;
          if (is.Object(eventName)) {
            eventInject = eventName.inject;
            eventName = eventName.name;
            emitKey = eventName.prefix || emitKey;
          }
          if (emitKey) {
            var fieldKey = toLine("".concat(emitKey, "-").concat(eventName));
            var fn = function fn2() {
              var _this2$vm, _this2$vm2, _this2$bus;
              if (_this2.vm.emitsOptions) {
                _this2.vm.emitsOptions[fieldKey] = null;
              }
              for (var _len = arguments.length, arg = new Array(_len), _key = 0; _key < _len; _key++) {
                arg[_key] = arguments[_key];
              }
              (_this2$vm = _this2.vm).emit.apply(_this2$vm, [fieldKey].concat(arg));
              (_this2$vm2 = _this2.vm).emit.apply(_this2$vm2, ["emit-event", fieldKey].concat(arg));
              (_this2$bus = _this2.bus).$emit.apply(_this2$bus, [fieldKey].concat(arg));
            };
            fn.__emit = true;
            if (!eventInject && inject2 === false) {
              event[eventName] = fn;
            } else {
              var _inject = eventInject || inject2 || _this2.options.injectEvent;
              event[eventName] = is.Undef(_inject) ? fn : _this2.inject(rule, fn, _inject);
            }
          }
        });
      }
      ctx.computed.on = event;
      return event;
    },
    getInjectData: function getInjectData(self, inject2) {
      var $api = self.__fc__ && self.__fc__.$api;
      var vm = self.__fc__ && self.__fc__.$handle.vm || this.vm.props;
      var _vm$props = vm.props, option = _vm$props.option, rule = _vm$props.rule;
      return {
        $f: $api || this.api,
        api: $api || this.api,
        rule,
        self: self.__origin__,
        option,
        inject: inject2
      };
    },
    inject: function inject2(self, _fn, _inject2) {
      if (_fn.__origin) {
        if (this.watching && !this.loading)
          return _fn;
        _fn = _fn.__origin;
      }
      var h2 = this;
      var fn = function fn2() {
        var data5 = h2.getInjectData(self, _inject2);
        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
          args[_key2] = arguments[_key2];
        }
        data5.args = [].concat(args);
        args.unshift(data5);
        return _fn.apply(this, args);
      };
      fn.__origin = _fn;
      fn.__json = _fn.__json;
      return fn;
    },
    loadStrVar: function loadStrVar(str, get) {
      var _this3 = this;
      if (str && typeof str === "string" && str.indexOf("{{") > -1 && str.indexOf("}}") > -1) {
        var tmp = str;
        var vars = extractVar(str);
        var lastVal;
        vars.forEach(function(v) {
          var split = v.split("||");
          var field = split[0].trim();
          if (field) {
            var def = (split[1] || "").trim();
            var val = get ? get(field, def) : _this3.fc.getLoadData(field, def);
            lastVal = val;
            str = str.replaceAll("{{".concat(v, "}}"), val == null ? "" : val);
          }
        });
        if (vars.length === 1 && tmp === "{{".concat(vars[0], "}}")) {
          return lastVal;
        }
      }
      return str;
    },
    loadFetchVar: function loadFetchVar(options, get) {
      var _this4 = this;
      var loadVal = function loadVal2(str) {
        return _this4.loadStrVar(str, get);
      };
      options.action = loadVal(options.action);
      if (options.headers) {
        var _headers = {};
        Object.keys(options.headers).forEach(function(k) {
          _headers[loadVal(k)] = loadVal(options.headers[k]);
        });
        options.headers = _headers;
      }
      if (options.data) {
        var _data = {};
        Object.keys(options.data).forEach(function(k) {
          _data[loadVal(k)] = loadVal(options.data[k]);
        });
        options.data = _data;
      }
      return options;
    }
  });
}
var EVENT = ["hook:updated", "hook:mounted"];
function usePage(Handler2) {
  extend(Handler2.prototype, {
    usePage: function usePage2() {
      var _this = this;
      var page = this.options.page;
      if (!page)
        return;
      var first = 25;
      var limit = getLimit(this.rules);
      if (is.Object(page)) {
        if (page.first)
          first = parseInt(page.first, 10) || first;
        if (page.limit)
          limit = parseInt(page.limit, 10) || limit;
      }
      extend(this, {
        first,
        limit,
        pageEnd: this.rules.length <= first
      });
      this.bus.$on("page-end", function() {
        return _this.vm.emit("page-end", _this.api);
      });
      this.pageLoad();
    },
    pageLoad: function pageLoad() {
      var _this2 = this;
      var pageFn = function pageFn2() {
        if (_this2.pageEnd) {
          _this2.bus.$off(EVENT, pageFn2);
          _this2.bus.$emit("page-end");
        } else {
          _this2.first += _this2.limit;
          _this2.pageEnd = _this2.rules.length <= _this2.first;
          _this2.loadRule();
          _this2.refresh();
        }
      };
      this.bus.$on(EVENT, pageFn);
    }
  });
}
function getLimit(rules) {
  return rules.length < 31 ? 31 : Math.ceil(rules.length / 3);
}
function useRender(Handler2) {
  extend(Handler2.prototype, {
    clearNextTick: function clearNextTick() {
      this.nextTick && clearTimeout(this.nextTick);
      this.nextTick = null;
    },
    bindNextTick: function bindNextTick(fn) {
      var _this = this;
      this.clearNextTick();
      this.nextTick = setTimeout(function() {
        fn();
        _this.nextTick = null;
      }, 10);
    },
    render: function render17() {
      ++this.loadedId;
      if (this.vm.setupState.unique > 0)
        return this.$render.render();
      else {
        this.vm.setupState.unique = 1;
        return [];
      }
    }
  });
}
function bind(ctx) {
  Object.defineProperties(ctx.origin, {
    __fc__: enumerable(markRaw(ctx), true)
  });
}
function RuleContext(handle, rule, defaultValue) {
  var id2 = uniqueId();
  var isInput = !!rule.field;
  extend(this, {
    id: id2,
    ref: id2,
    wrapRef: id2 + "fi",
    rule,
    origin: rule.__origin__ || rule,
    name: rule.name,
    pending: {},
    none: false,
    watch: [],
    linkOn: [],
    root: [],
    ctrlRule: [],
    children: [],
    parent: null,
    group: rule.subRule ? this : null,
    cacheConfig: null,
    prop: _objectSpread2({}, rule),
    computed: {},
    payload: {},
    refRule: {},
    input: isInput,
    el: void 0,
    exportEl: void 0,
    defaultValue: isInput ? deepCopy(defaultValue) : void 0,
    field: rule.field || void 0
  });
  this.updateKey();
  bind(this);
  this.update(handle, true);
}
extend(RuleContext.prototype, {
  getParentGroup: function getParentGroup() {
    var ctx = this.parent;
    while (ctx) {
      if (ctx.group) {
        return ctx;
      }
      ctx = ctx.parent;
    }
  },
  loadChildrenPending: function loadChildrenPending() {
    var _this = this;
    var children = this.rule.children || [];
    if (Array.isArray(children))
      return children;
    return this.loadPending({
      key: "children",
      origin: children,
      def: [],
      onLoad: function onLoad(data5) {
        _this.$handle && _this.$handle.loadChildren(data5, _this);
      },
      onUpdate: function onUpdate(value, oldValue) {
        if (_this.$handle) {
          value === oldValue ? _this.$handle.loadChildren(value, _this) : _this.$handle.updateChildren(_this, value, oldValue);
        }
      },
      onReload: function onReload(value) {
        if (_this.$handle) {
          _this.$handle.updateChildren(_this, [], value);
        } else {
          delete _this.pending.children;
        }
      }
    });
  },
  loadPending: function loadPending(config) {
    var _this2 = this;
    var key = config.key, origin = config.origin, def = config.def, onLoad = config.onLoad, onReload = config.onReload, onUpdate = config.onUpdate;
    if (this.pending[key] && this.pending[key].origin === origin) {
      return this.getPending(key, def);
    }
    delete this.pending[key];
    var value = origin;
    if (is.Function(origin)) {
      var source = invoke(function() {
        return origin({
          rule: _this2.rule,
          api: _this2.$api,
          update: function update5(data5) {
            var value2 = data5 || def;
            var oldValue = _this2.getPending(key, def);
            _this2.setPending(key, origin, value2);
            onUpdate && onUpdate(value2, oldValue);
          },
          reload: function reload() {
            var oldValue = _this2.getPending(key, def);
            delete _this2.pending[key];
            onReload && onReload(oldValue);
            _this2.$api && _this2.$api.sync(_this2.rule);
          }
        });
      });
      if (source && is.Function(source.then)) {
        source.then(function(data5) {
          var value2 = data5 || def;
          _this2.setPending(key, origin, value2);
          onLoad && onLoad(value2);
          _this2.$api && _this2.$api.sync(_this2.rule);
        })["catch"](function(e) {
          console.error(e);
        });
        value = def;
        this.setPending(key, origin, value);
      } else {
        value = source || def;
        this.setPending(key, origin, value);
        onLoad && onLoad(value);
      }
    }
    return value;
  },
  getPending: function getPending(key, def) {
    return this.pending[key] && this.pending[key].value || def;
  },
  setPending: function setPending(key, origin, value) {
    this.pending[key] = {
      origin,
      value: reactive(value)
    };
  },
  effectData: function effectData(name2) {
    if (!this.payload[name2]) {
      this.payload[name2] = {};
    }
    return this.payload[name2];
  },
  clearEffectData: function clearEffectData(name2) {
    if (name2 === void 0) {
      this.payload = {};
    } else {
      delete this.payload[name2];
    }
  },
  updateKey: function updateKey(flag) {
    this.key = uniqueId();
    flag && this.parent && this.parent.updateKey(flag);
  },
  updateType: function updateType() {
    this.originType = this.rule.type;
    this.type = toCase(this.rule.type);
    this.trueType = this.$handle.getType(this.originType);
  },
  setParser: function setParser(parser) {
    this.parser = parser;
    parser.init(this);
  },
  initProp: function initProp() {
    var _this3 = this;
    var rule = _objectSpread2({}, this.rule);
    delete rule.children;
    delete rule.validate;
    this.prop = mergeRule({}, [rule].concat(_toConsumableArray(Object.keys(this.payload).map(function(k) {
      return _this3.payload[k];
    })), [this.computed]));
    this.prop.validate = [].concat(_toConsumableArray(this.refRule.__$validate.value || []), _toConsumableArray(this.prop.validate || []));
  },
  initNone: function initNone() {
    this.none = !(is.Undef(this.prop.display) || !!this.prop.display);
  },
  injectValidate: function injectValidate() {
    return this.prop.validate;
  },
  check: function check(handle) {
    return this.vm === handle.vm;
  },
  unwatch: function unwatch() {
    this.watch.forEach(function(un) {
      return un();
    });
    this.watch = [];
    this.refRule = {};
  },
  unlink: function unlink() {
    this.linkOn.forEach(function(un) {
      return un();
    });
    this.linkOn = [];
  },
  link: function link() {
    this.unlink();
    this.$handle.appendLink(this);
  },
  watchTo: function watchTo() {
    this.$handle.watchCtx(this);
  },
  "delete": function _delete() {
    this.unwatch();
    this.unlink();
    this.rmCtrl();
    if (this.parent) {
      this.parent.children.splice(this.parent.children.indexOf(this) >>> 0, 1);
    }
    extend(this, {
      deleted: true,
      computed: {},
      parent: null,
      children: [],
      cacheConfig: null,
      none: false
    });
  },
  rmCtrl: function rmCtrl() {
    this.ctrlRule.forEach(function(ctrl) {
      return ctrl.__fc__ && ctrl.__fc__.rm();
    });
    this.ctrlRule = [];
  },
  rm: function rm() {
    var _this4 = this;
    var _rm = function _rm2() {
      var index = _this4.root.indexOf(_this4.origin);
      if (index > -1) {
        _this4.root.splice(index, 1);
        _this4.$handle && _this4.$handle.refresh();
      }
    };
    if (this.deleted) {
      _rm();
      return;
    }
    this.$handle.noWatch(function() {
      _this4.$handle.deferSyncValue(function() {
        _this4.rmCtrl();
        _rm();
        _this4.$handle.rmCtx(_this4);
        extend(_this4, {
          root: []
        });
      }, _this4.input);
    });
  },
  update: function update2(handle, init4) {
    extend(this, {
      deleted: false,
      $handle: handle,
      $render: handle.$render,
      $api: handle.api,
      vm: handle.vm,
      vNode: handle.$render.vNode,
      updated: false,
      cacheValue: this.rule.value
    });
    !init4 && this.unwatch();
    this.watchTo();
    this.link();
    this.updateType();
  }
});
function useLoader(Handler2) {
  extend(Handler2.prototype, {
    nextRefresh: function nextRefresh(fn) {
      var _this = this;
      var id2 = this.loadedId;
      nextTick(function() {
        id2 === _this.loadedId && (fn ? fn() : _this.refresh());
      });
    },
    parseRule: function parseRule(_rule) {
      var _this2 = this;
      var rule = getRule(_rule);
      Object.defineProperties(rule, {
        __origin__: enumerable(_rule, true)
      });
      fullRule(rule);
      this.appendValue(rule);
      [rule, rule["prefix"], rule["suffix"]].forEach(function(item) {
        if (!item) {
          return;
        }
        _this2.loadFn(item, rule);
      });
      this.loadCtrl(rule);
      if (rule.update) {
        rule.update = parseFn(rule.update);
      }
      return rule;
    },
    loadFn: function loadFn(item, rule) {
      var _this3 = this;
      ["on", "props", "deep"].forEach(function(k) {
        item[k] && _this3.parseInjectEvent(rule, item[k]);
      });
    },
    loadCtrl: function loadCtrl(rule) {
      rule.control && rule.control.forEach(function(ctrl) {
        if (ctrl.handle) {
          ctrl.handle = parseFn(ctrl.handle);
        }
      });
    },
    syncProp: function syncProp(ctx) {
      var _this4 = this;
      var rule = ctx.rule;
      is.trueArray(rule.sync) && mergeProps2([{
        on: rule.sync.reduce(function(pre, prop) {
          pre["update:".concat(prop)] = function(val) {
            rule.props[prop] = val;
            _this4.vm.emit("sync", prop, val, rule, _this4.fapi);
          };
          return pre;
        }, {})
      }], ctx.computed);
    },
    loadRule: function loadRule() {
      var _this5 = this;
      this.cycleLoad = false;
      this.loading = true;
      if (this.pageEnd) {
        this.bus.$emit("load-start");
      }
      this.deferSyncValue(function() {
        _this5._loadRule(_this5.rules);
        _this5.loading = false;
        if (_this5.cycleLoad && _this5.pageEnd) {
          return _this5.loadRule();
        }
        _this5.syncForm();
        if (_this5.pageEnd) {
          _this5.bus.$emit("load-end");
        }
        _this5.vm.setupState.renderRule();
      });
    },
    loadChildren: function loadChildren(children, parent) {
      this.cycleLoad = false;
      this.loading = true;
      this.bus.$emit("load-start");
      this._loadRule(children, parent);
      this.loading = false;
      if (this.cycleLoad) {
        return this.loadRule();
      } else {
        this.syncForm();
        this.bus.$emit("load-end");
      }
      this.$render.clearCache(parent);
    },
    _loadRule: function _loadRule(rules, parent) {
      var _this6 = this;
      var preIndex = function preIndex2(i) {
        var pre = rules[i - 1];
        if (!pre || !pre.__fc__) {
          return i > 0 ? preIndex2(i - 1) : -1;
        }
        var index = _this6.sort.indexOf(pre.__fc__.id);
        return index > -1 ? index : preIndex2(i - 1);
      };
      var loadChildren = function loadChildren2(children, parent2) {
        if (is.trueArray(children)) {
          _this6._loadRule(children, parent2);
        }
      };
      var ctxs = rules.map(function(_rule, index) {
        if (parent && !is.Object(_rule))
          return;
        if (!_this6.pageEnd && !parent && index >= _this6.first)
          return;
        if (_rule.__fc__ && _rule.__fc__.root === rules && _this6.ctxs[_rule.__fc__.id]) {
          loadChildren(_rule.__fc__.loadChildrenPending(), _rule.__fc__);
          return _rule.__fc__;
        }
        var rule = getRule(_rule);
        var isRepeat = function isRepeat2() {
          return !!(rule.field && _this6.fieldCtx[rule.field] && _this6.fieldCtx[rule.field][0] !== _rule.__fc__);
        };
        _this6.fc.targetFormDriver("loadRule", {
          rule,
          api: _this6.api
        }, _this6.fc);
        _this6.ruleEffect(rule, "init", {
          repeat: isRepeat()
        });
        if (isRepeat()) {
          _this6.vm.emit("repeat-field", _rule, _this6.api);
        }
        var ctx;
        var isCopy = false;
        var isInit = !!_rule.__fc__;
        var defaultValue = rule.value;
        if (isInit) {
          ctx = _rule.__fc__;
          defaultValue = ctx.defaultValue;
          if (ctx.deleted) {
            if (isCtrl(ctx)) {
              return;
            }
            ctx.update(_this6);
          } else {
            if (!ctx.check(_this6)) {
              if (isCtrl(ctx)) {
                return;
              }
              rules[index] = _rule = _rule._clone ? _rule._clone() : copyRule(_rule);
              ctx = null;
              isCopy = true;
            }
          }
        }
        if (!ctx) {
          var _rule2 = _this6.parseRule(_rule);
          ctx = new RuleContext(_this6, _rule2, defaultValue);
          _this6.bindParser(ctx);
        } else {
          if (ctx.originType !== ctx.rule.type) {
            ctx.updateType();
          }
          _this6.bindParser(ctx);
          _this6.appendValue(ctx.rule);
          if (ctx.parent && ctx.parent !== parent) {
            _this6.rmSubRuleData(ctx);
          }
        }
        _this6.parseEmit(ctx);
        _this6.syncProp(ctx);
        ctx.parent = parent || null;
        ctx.root = rules;
        _this6.setCtx(ctx);
        if (!isCopy && !isInit) {
          _this6.effect(ctx, "load");
          _this6.targetHook(ctx, "load");
        }
        _this6.effect(ctx, "created");
        var _load = ctx.loadChildrenPending();
        ctx.parser.loadChildren === false || loadChildren(_load, ctx);
        if (!parent) {
          var _preIndex = preIndex(index);
          if (_preIndex > -1 || !index) {
            _this6.sort.splice(_preIndex + 1, 0, ctx.id);
          } else {
            _this6.sort.push(ctx.id);
          }
        }
        var r = ctx.rule;
        if (!ctx.updated) {
          ctx.updated = true;
          if (is.Function(r.update)) {
            _this6.bus.$once("load-end", function() {
              _this6.refreshUpdate(ctx, r.value, "init");
            });
          }
          _this6.effect(ctx, "loaded");
        }
        if (_this6.refreshControl(ctx))
          _this6.cycleLoad = true;
        return ctx;
      }).filter(function(v) {
        return !!v;
      });
      if (parent) {
        parent.children = ctxs;
      }
    },
    refreshControl: function refreshControl(ctx) {
      return ctx.input && ctx.rule.control && this.useCtrl(ctx);
    },
    useCtrl: function useCtrl(ctx) {
      var _this7 = this;
      var controls = getCtrl(ctx), validate2 = [], api = this.api;
      if (!controls.length)
        return false;
      var _loop = function _loop2(i2) {
        var control = controls[i2], handleFn = control.handle || function(val) {
          return (condition[control.condition || "=="] || condition["=="])(val, control.value);
        };
        if (!is.trueArray(control.rule))
          return "continue";
        var data5 = _objectSpread2(_objectSpread2({}, control), {}, {
          valid: invoke(function() {
            return handleFn(ctx.rule.value, api);
          }),
          ctrl: findCtrl(ctx, control.rule),
          isHidden: is.String(control.rule[0])
        });
        if (data5.valid && data5.ctrl || !data5.valid && !data5.ctrl && !data5.isHidden)
          return "continue";
        validate2.push(data5);
      };
      for (var i = 0; i < controls.length; i++) {
        var _ret = _loop(i);
        if (_ret === "continue")
          continue;
      }
      if (!validate2.length)
        return false;
      var hideLst = [];
      var flag = false;
      this.deferSyncValue(function() {
        validate2.reverse().forEach(function(_ref) {
          var isHidden = _ref.isHidden, valid2 = _ref.valid, rule = _ref.rule, prepend = _ref.prepend, append = _ref.append, child = _ref.child, ctrl = _ref.ctrl, method = _ref.method;
          if (isHidden) {
            valid2 ? ctx.ctrlRule.push({
              __ctrl: true,
              children: rule,
              valid: valid2
            }) : ctrl && ctx.ctrlRule.splice(ctx.ctrlRule.indexOf(ctrl) >>> 0, 1);
            hideLst[valid2 ? "push" : "unshift"](function() {
              if (method === "disabled" || method === "enabled") {
                _this7.api.disabled(!valid2, rule);
              } else if (method === "display") {
                _this7.api.display(valid2, rule);
              } else if (method === "required") {
                rule.forEach(function(item) {
                  _this7.api.setEffect(item, "required", valid2);
                });
                if (!valid2) {
                  _this7.api.clearValidateState(rule);
                }
              } else {
                _this7.api.hidden(!valid2, rule);
              }
            });
            return;
          }
          if (valid2) {
            flag = true;
            var ruleCon = {
              type: "fragment",
              "native": true,
              __ctrl: true,
              children: rule
            };
            ctx.ctrlRule.push(ruleCon);
            _this7.bus.$once("load-start", function() {
              if (prepend) {
                api.prepend(ruleCon, prepend, child);
              } else if (append || child) {
                api.append(ruleCon, append || ctx.id, child);
              } else {
                ctx.root.splice(ctx.root.indexOf(ctx.origin) + 1, 0, ruleCon);
              }
            });
          } else {
            ctx.ctrlRule.splice(ctx.ctrlRule.indexOf(ctrl), 1);
            var ctrlCtx = byCtx(ctrl);
            ctrlCtx && ctrlCtx.rm();
          }
        });
      });
      if (hideLst.length) {
        if (this.loading) {
          hideLst.length && this.bus.$once("load-end", function() {
            hideLst.forEach(function(v) {
              return v();
            });
          });
        } else {
          hideLst.length && nextTick(function() {
            hideLst.forEach(function(v) {
              return v();
            });
          });
        }
      }
      this.vm.emit("control", ctx.origin, this.api);
      this.effect(ctx, "control");
      return flag;
    },
    reloadRule: function reloadRule(rules) {
      return this._reloadRule(rules);
    },
    _reloadRule: function _reloadRule(rules) {
      var _this8 = this;
      if (!rules)
        rules = this.rules;
      var ctxs = _objectSpread2({}, this.ctxs);
      this.clearNextTick();
      this.initData(rules);
      this.fc.rules = rules;
      this.deferSyncValue(function() {
        _this8.bus.$once("load-end", function() {
          Object.keys(ctxs).filter(function(id2) {
            return _this8.ctxs[id2] === void 0;
          }).forEach(function(id2) {
            return _this8.rmCtx(ctxs[id2]);
          });
          _this8.$render.clearCacheAll();
        });
        _this8.reloading = true;
        _this8.loadRule();
        _this8.reloading = false;
        _this8.refresh();
        _this8.bus.$emit("reloading", _this8.api);
      });
      this.bus.$off("next-tick", this.nextReload);
      this.bus.$once("next-tick", this.nextReload);
      this.bus.$emit("update", this.api);
    },
    //todo 组件生成全部通过 alias
    refresh: function refresh() {
      this.vm.setupState.refresh();
    }
  });
}
function fullRule(rule) {
  var def = baseRule();
  Object.keys(def).forEach(function(k) {
    if (!hasProperty(rule, k))
      rule[k] = def[k];
  });
  return rule;
}
function getCtrl(ctx) {
  var control = ctx.rule.control || [];
  if (is.Object(control))
    return [control];
  else
    return control;
}
function findCtrl(ctx, rule) {
  for (var i = 0; i < ctx.ctrlRule.length; i++) {
    var ctrl = ctx.ctrlRule[i];
    if (ctrl.children === rule)
      return ctrl;
  }
}
function isCtrl(ctx) {
  return !!ctx.rule.__ctrl;
}
function useInput(Handler2) {
  extend(Handler2.prototype, {
    setValue: function setValue4(ctx, value, formValue, setFlag) {
      if (ctx.deleted)
        return;
      ctx.rule.value = value;
      this.changeStatus = true;
      this.nextRefresh();
      this.$render.clearCache(ctx);
      this.setFormData(ctx, formValue);
      this.syncValue();
      this.valueChange(ctx, value);
      this.vm.emit("change", ctx.field, value, ctx.origin, this.api, setFlag || false);
      this.effect(ctx, "value");
      this.targetHook(ctx, "value", {
        value
      });
      this.emitEvent("change", ctx.field, value, {
        rule: ctx.origin,
        api: this.api,
        setFlag: setFlag || false
      });
    },
    onInput: function onInput(ctx, value) {
      var val;
      if (ctx.input && (this.isQuote(ctx, val = ctx.parser.toValue(value, ctx)) || this.isChange(ctx, value))) {
        this.setValue(ctx, val, value);
      }
    },
    onUpdateValue: function onUpdateValue(ctx, data5) {
      var _this = this;
      this.deferSyncValue(function() {
        var group = ctx.getParentGroup();
        var subForm = group ? _this.subRuleData[group.id] : null;
        var subData = {};
        Object.keys(data5 || {}).forEach(function(k) {
          if (subForm && hasProperty(subForm, k)) {
            subData[k] = data5[k];
          } else if (hasProperty(_this.api.form, k)) {
            _this.api.form[k] = data5[k];
          } else if (_this.api.top !== _this.api && hasProperty(_this.api.top.form, k)) {
            _this.api.top.form[k] = data5[k];
          }
        });
        if (Object.keys(subData).length) {
          _this.api.setChildrenFormData(group.rule, subData);
        }
      });
    },
    onBaseInput: function onBaseInput(ctx, value) {
      this.setFormData(ctx, value);
      ctx.modelValue = value;
      this.nextRefresh();
      this.$render.clearCache(ctx);
    },
    setFormData: function setFormData(ctx, value) {
      ctx.modelValue = value;
      var group = ctx.getParentGroup();
      if (group) {
        if (!this.subRuleData[group.id]) {
          this.subRuleData[group.id] = {};
        }
        this.subRuleData[group.id][ctx.field] = ctx.rule.value;
      }
      $set(this.formData, ctx.id, value);
    },
    rmSubRuleData: function rmSubRuleData(ctx) {
      var group = ctx.getParentGroup();
      if (group && this.subRuleData[group.id]) {
        delete this.subRuleData[group.id][ctx.field];
      }
    },
    getFormData: function getFormData(ctx) {
      return this.formData[ctx.id];
    },
    syncForm: function syncForm() {
      var _this2 = this;
      var data5 = reactive({});
      var fields = this.fields();
      var ignoreFields = [];
      if (this.options.appendValue !== false) {
        Object.keys(this.appendData).reduce(function(initial, field) {
          if (fields.indexOf(field) === -1) {
            initial[field] = toRef(_this2.appendData, field);
          }
          return initial;
        }, data5);
      }
      fields.reduce(function(initial, field) {
        var ctx = (_this2.fieldCtx[field] || []).filter(function(ctx2) {
          return !_this2.isIgnore(ctx2.rule);
        })[0] || _this2.fieldCtx[field][0];
        if (_this2.isIgnore(ctx.rule)) {
          ignoreFields.push(field);
        }
        initial[field] = toRef(ctx.rule, "value");
        return initial;
      }, data5);
      this.form = data5;
      this.ignoreFields = ignoreFields;
      this.syncValue();
    },
    isIgnore: function isIgnore(rule) {
      return rule.ignore === true || rule.ignore === "hidden" && rule.hidden || this.options.ignoreHiddenFields && rule.hidden;
    },
    appendValue: function appendValue(rule) {
      if ((!rule.field || !hasProperty(this.appendData, rule.field)) && !this.options.forceCoverValue) {
        return;
      }
      rule.value = this.appendData[rule.field];
      delete this.appendData[rule.field];
    },
    addSubForm: function addSubForm(ctx, subForm) {
      this.subForm[ctx.id] = subForm;
    },
    deferSyncValue: function deferSyncValue(fn, sync) {
      if (!this.deferSyncFn) {
        this.deferSyncFn = fn;
      }
      if (!this.deferSyncFn.sync) {
        this.deferSyncFn.sync = sync;
      }
      invoke(fn);
      if (this.deferSyncFn === fn) {
        this.deferSyncFn = null;
        if (fn.sync) {
          this.syncForm();
        }
      }
    },
    syncValue: function syncValue() {
      var _this3 = this;
      if (this.deferSyncFn) {
        return this.deferSyncFn.sync = true;
      }
      var data5 = {};
      Object.keys(this.form).forEach(function(k) {
        if (_this3.ignoreFields.indexOf(k) === -1) {
          data5[k] = _this3.form[k];
        }
      });
      this.vm.setupState.updateValue(data5);
    },
    isChange: function isChange(ctx, value) {
      return JSON.stringify(this.getFormData(ctx), strFn) !== JSON.stringify(value, strFn);
    },
    isQuote: function isQuote(ctx, value) {
      return (is.Object(value) || Array.isArray(value)) && value === ctx.rule.value;
    },
    refreshUpdate: function refreshUpdate(ctx, val, origin, field) {
      var _this4 = this;
      if (is.Function(ctx.rule.update)) {
        var state = invoke(function() {
          return ctx.rule.update(val, ctx.origin, _this4.api, {
            origin: origin || "change",
            linkField: field
          });
        });
        if (state === void 0)
          return;
        ctx.rule.hidden = state === true;
      }
    },
    valueChange: function valueChange(ctx, val) {
      this.refreshRule(ctx, val);
      this.bus.$emit("change-" + ctx.field, val);
    },
    refreshRule: function refreshRule(ctx, val, origin, field) {
      if (this.refreshControl(ctx)) {
        this.$render.clearCacheAll();
        this.loadRule();
        this.bus.$emit("update", this.api);
        this.refresh();
      }
      this.refreshUpdate(ctx, val, origin, field);
    },
    appendLink: function appendLink(ctx) {
      var _this5 = this;
      var link2 = ctx.rule.link;
      is.trueArray(link2) && link2.forEach(function(field) {
        var fn = function fn2() {
          return _this5.refreshRule(ctx, ctx.rule.value, "link", field);
        };
        _this5.bus.$on("change-" + field, fn);
        ctx.linkOn.push(function() {
          return _this5.bus.$off("change-" + field, fn);
        });
      });
    },
    fields: function fields() {
      return Object.keys(this.fieldCtx);
    }
  });
}
function strFn(key, val) {
  return typeof val === "function" ? "" + val : val;
}
var BaseParser = {
  init: function init(ctx) {
  },
  toFormValue: function toFormValue2(value, ctx) {
    return value;
  },
  toValue: function toValue2(formValue, ctx) {
    return formValue;
  },
  mounted: function mounted6(ctx) {
  },
  render: function render10(children, ctx) {
    if (ctx.$handle.fc.renderDriver && ctx.$handle.fc.renderDriver.defaultRender) {
      return ctx.$handle.fc.renderDriver.defaultRender(ctx, children);
    }
    return ctx.$render.defaultRender(ctx, children);
  },
  preview: function preview(children, ctx) {
    if (ctx.$handle.fc.renderDriver && ctx.$handle.fc.renderDriver.defaultPreview) {
      return ctx.$handle.fc.renderDriver.defaultPreview(ctx, children);
    }
    return this.render(children, ctx);
  },
  mergeProp: function mergeProp2(ctx) {
  }
};
var noneKey = ["field", "value", "vm", "template", "name", "config", "control", "inject", "sync", "payload", "optionsTo", "update", "slotUpdate", "computed", "component", "cache"];
function useContext(Handler2) {
  extend(Handler2.prototype, {
    getCtx: function getCtx(id2) {
      return this.getFieldCtx(id2) || this.getNameCtx(id2)[0] || this.ctxs[id2];
    },
    getCtxs: function getCtxs(id2) {
      return this.fieldCtx[id2] || this.nameCtx[id2] || (this.ctxs[id2] ? [this.ctxs[id2]] : []);
    },
    setIdCtx: function setIdCtx(ctx, key, type2) {
      var field = "".concat(type2, "Ctx");
      if (!this[field][key]) {
        this[field][key] = [ctx];
      } else {
        this[field][key].push(ctx);
      }
    },
    rmIdCtx: function rmIdCtx(ctx, key, type2) {
      var field = "".concat(type2, "Ctx");
      var lst = this[field][key];
      if (!lst)
        return false;
      var flag = lst.splice(lst.indexOf(ctx) >>> 0, 1).length > 0;
      if (!lst.length) {
        delete this[field][key];
      }
      return flag;
    },
    getFieldCtx: function getFieldCtx(field) {
      return (this.fieldCtx[field] || [])[0];
    },
    getNameCtx: function getNameCtx(name2) {
      return this.nameCtx[name2] || [];
    },
    setCtx: function setCtx(ctx) {
      var id2 = ctx.id, field = ctx.field, name2 = ctx.name, rule = ctx.rule;
      this.ctxs[id2] = ctx;
      name2 && this.setIdCtx(ctx, name2, "name");
      if (!ctx.input)
        return;
      this.setIdCtx(ctx, field, "field");
      this.setFormData(ctx, ctx.parser.toFormValue(rule.value, ctx));
      if (this.isMounted && !this.reloading) {
        this.vm.emit("change", ctx.field, rule.value, ctx.origin, this.api);
      }
    },
    getParser: function getParser(ctx) {
      var list = this.fc.parsers;
      var renderDriver = this.fc.renderDriver;
      if (renderDriver) {
        var _list = renderDriver.parsers || {};
        var parser = _list[ctx.originType] || _list[toCase(ctx.type)] || _list[ctx.trueType];
        if (parser) {
          return parser;
        }
      }
      return list[ctx.originType] || list[toCase(ctx.type)] || list[ctx.trueType] || BaseParser;
    },
    bindParser: function bindParser(ctx) {
      ctx.setParser(this.getParser(ctx));
    },
    getType: function getType2(alias2) {
      var map = this.fc.CreateNode.aliasMap;
      var type2 = map[alias2] || map[toCase(alias2)] || alias2;
      return toCase(type2);
    },
    noWatch: function noWatch(fn) {
      if (!this.noWatchFn) {
        this.noWatchFn = fn;
      }
      invoke(fn);
      if (this.noWatchFn === fn) {
        this.noWatchFn = null;
      }
    },
    watchCtx: function watchCtx(ctx) {
      var _this = this;
      var all = attrs();
      all.filter(function(k) {
        return k[0] !== "_" && k[0] !== "$" && noneKey.indexOf(k) === -1;
      }).forEach(function(key) {
        var ref2 = toRef(ctx.rule, key);
        var flag = key === "children";
        ctx.refRule[key] = ref2;
        ctx.watch.push(watch(flag ? function() {
          return is.Function(ref2.value) ? ref2.value : _toConsumableArray(ref2.value || []);
        } : function() {
          return ref2.value;
        }, function(_7, o) {
          var n = ref2.value;
          if (_this.isBreakWatch())
            return;
          if (flag && ctx.parser.loadChildren === false) {
            _this.$render.clearCache(ctx);
            _this.nextRefresh();
            return;
          }
          _this.watching = true;
          nextTick(function() {
            _this.targetHook(ctx, "watch", {
              key,
              oldValue: o,
              newValue: n
            });
          });
          if (key === "hidden" && Boolean(n) !== Boolean(o)) {
            _this.$render.clearCacheAll();
            nextTick(function() {
              _this.targetHook(ctx, "hidden", {
                value: n
              });
            });
          }
          if (key === "ignore" && ctx.input || key === "hidden" && ctx.input && (ctx.rule.ignore === "hidden" || _this.options.ignoreHiddenFields)) {
            _this.syncForm();
          } else if (key === "link") {
            ctx.link();
            return;
          } else if (["props", "on", "deep"].indexOf(key) > -1) {
            _this.parseInjectEvent(ctx.rule, n || {});
            if (key === "props" && ctx.input) {
              _this.setFormData(ctx, ctx.parser.toFormValue(ctx.rule.value, ctx));
            }
          } else if (key === "emit") {
            _this.parseEmit(ctx);
          } else if (["prefix", "suffix"].indexOf(key) > -1)
            n && _this.loadFn(n, ctx.rule);
          else if (key === "type") {
            ctx.updateType();
            _this.bindParser(ctx);
          } else if (flag) {
            if (is.Function(o)) {
              o = ctx.getPending("children", []);
            }
            if (is.Function(n)) {
              n = ctx.loadChildrenPending();
            }
            _this.updateChildren(ctx, n, o);
          }
          _this.$render.clearCache(ctx);
          _this.refresh();
          _this.watching = false;
        }, {
          deep: !flag,
          sync: flag
        }));
      });
      ctx.refRule["__$title"] = computed(function() {
        var title = (_typeof(ctx.rule.title) === "object" ? ctx.rule.title.title : ctx.rule.title) || "";
        if (title) {
          var match = title.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);
          if (match) {
            title = _this.api.t(match[1]);
          }
        }
        return title;
      });
      ctx.refRule["__$info"] = computed(function() {
        var info = (_typeof(ctx.rule.info) === "object" ? ctx.rule.info.info : ctx.rule.info) || "";
        if (info) {
          var match = info.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);
          if (match) {
            info = _this.api.t(match[1]);
          }
        }
        return info;
      });
      ctx.refRule["__$validate"] = computed(function() {
        return toArray(ctx.rule.validate).map(function(item) {
          var temp = _objectSpread2({}, item);
          if (temp.message) {
            var match = temp.message.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);
            if (match) {
              temp.message = _this.api.t(match[1], {
                title: ctx.refRule.__$title.value
              });
            }
          }
          if (is.Function(temp.validator)) {
            var that = ctx;
            temp.validator = function() {
              var _item$validator;
              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
                args[_key] = arguments[_key];
              }
              return (_item$validator = item.validator).call.apply(_item$validator, [{
                that: this,
                id: that.id,
                field: that.field,
                rule: that.rule,
                api: that.$handle.api
              }].concat(args));
            };
            return temp;
          }
          return temp;
        });
      });
      if (ctx.input) {
        var val = toRef(ctx.rule, "value");
        ctx.watch.push(watch(function() {
          return val.value;
        }, function() {
          var formValue = ctx.parser.toFormValue(val.value, ctx);
          if (_this.isChange(ctx, formValue)) {
            _this.setValue(ctx, val.value, formValue, true);
          }
        }));
      }
      this.bus.$once("load-end", function() {
        var computedRule = ctx.rule.computed;
        if (!computedRule) {
          return;
        }
        if (_typeof(computedRule) !== "object") {
          computedRule = {
            value: computedRule
          };
        }
        Object.keys(computedRule).forEach(function(k) {
          var oldValue = void 0;
          var computedValue = computed(function() {
            var item = computedRule[k];
            if (!item)
              return void 0;
            var value = _this.compute(ctx, item);
            if (item.linkage && value === void 0) {
              return oldValue;
            }
            return value;
          });
          var callback = function callback2(n) {
            if (k === "value") {
              _this.onInput(ctx, n);
            } else if (k[0] === "$") {
              _this.api.setEffect(ctx.id, k, n);
            } else {
              deepSet(ctx.rule, k, n);
            }
          };
          if (k === "value" ? [void 0, null, ""].indexOf(ctx.rule.value) > -1 : computedValue.value !== deepGet(ctx.rule, k)) {
            callback(computedValue.value);
          }
          ctx.watch.push(watch(computedValue, function(n) {
            oldValue = n;
            setTimeout(function() {
              callback(n);
            });
          }));
        });
      });
      this.watchEffect(ctx);
    },
    compute: function compute(ctx, item) {
      var _this2 = this;
      var fn;
      if (_typeof(item) === "object") {
        var group = ctx.getParentGroup();
        var checkCondition = function checkCondition2(item2) {
          item2 = Array.isArray(item2) ? {
            mode: "AND",
            group: item2
          } : item2;
          if (!is.trueArray(item2.group)) {
            return true;
          }
          var or = item2.mode === "OR";
          var valid2 = true;
          var _loop = function _loop2(i2) {
            var one = item2.group[i2];
            var flag = void 0;
            var field = one.field;
            if (one.variable) {
              field = JSON.stringify(_this2.fc.getLoadData(one.variable) || "");
            }
            if (one.mode) {
              flag = checkCondition2(one);
            } else if (!condition[one.condition]) {
              flag = false;
            } else if (is.Function(one.handler)) {
              flag = invoke(function() {
                return one.handler(_this2.api, ctx.rule);
              });
            } else {
              flag = new Function("$condition", "$val", "$form", "$group", "$rule", "with($form){with(this){with($group){ return $condition['".concat(one.condition, "'](").concat(field, ", ").concat(one.compare ? one.compare : "$val", "); }}}")).call(_this2.api.form, condition, one.value, _this2.api.top.form, group ? _this2.subRuleData[group.id] || {} : {}, ctx.rule);
            }
            if (or && flag) {
              return {
                v: true
              };
            }
            if (!or) {
              valid2 = valid2 && flag;
            }
          };
          for (var i = 0; i < item2.group.length; i++) {
            var _ret = _loop(i);
            if (_typeof(_ret) === "object")
              return _ret.v;
          }
          return or ? false : valid2;
        };
        var val = checkCondition(item);
        val = item.invert === true ? !val : val;
        if (item.linkage) {
          return val ? invoke(function() {
            return _this2.computeValue(item.linkage, ctx, group);
          }, void 0) : void 0;
        }
        return val;
      } else if (is.Function(item)) {
        fn = function fn2() {
          return item(_this2.api.form, _this2.api);
        };
      } else {
        var _group = ctx.getParentGroup();
        fn = function fn2() {
          return _this2.computeValue(item, ctx, _group);
        };
      }
      return invoke(fn, void 0);
    },
    computeValue: function computeValue(str, ctx, group) {
      var that = this;
      var formulas = Object.keys(this.fc.formulas).reduce(function(obj, k) {
        obj[k] = function() {
          var _that$fc$formulas$k;
          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          return (_that$fc$formulas$k = that.fc.formulas[k]).call.apply(_that$fc$formulas$k, [{
            that: this,
            rule: ctx.rule,
            api: that.api,
            fc: that.fc
          }].concat(args));
        };
        return obj;
      }, {});
      return new Function("$formulas", "$form", "$group", "$rule", "$api", "with($form){with(this){with($group){with($formulas){ return ".concat(str, " }}}}")).call(this.api.form, formulas, this.api.top.form, group ? this.subRuleData[group.id] || {} : {}, ctx.rule, this.api);
    },
    updateChildren: function updateChildren(ctx, n, o) {
      var _this3 = this;
      this.deferSyncValue(function() {
        o && o.forEach(function(child) {
          if ((n || []).indexOf(child) === -1 && child && !is.String(child) && child.__fc__ && child.__fc__.parent === ctx) {
            _this3.rmCtx(child.__fc__);
          }
        });
        if (is.trueArray(n)) {
          _this3.loadChildren(n, ctx);
          _this3.bus.$emit("update", _this3.api);
        }
      });
    },
    rmSub: function rmSub(sub) {
      var _this4 = this;
      is.trueArray(sub) && sub.forEach(function(r) {
        r && r.__fc__ && _this4.rmCtx(r.__fc__);
      });
    },
    rmCtx: function rmCtx(ctx) {
      var _this5 = this;
      if (ctx.deleted)
        return;
      var id2 = ctx.id, field = ctx.field, input4 = ctx.input, name2 = ctx.name;
      $del(this.ctxs, id2);
      $del(this.formData, id2);
      $del(this.subForm, id2);
      $del(this.vm.setupState.ctxInject, id2);
      var group = ctx.getParentGroup();
      if (group && this.subRuleData[group.id]) {
        $del(this.subRuleData[group.id], field);
      }
      if (ctx.group) {
        $del(this.subRuleData, id2);
      }
      input4 && this.rmIdCtx(ctx, field, "field");
      name2 && this.rmIdCtx(ctx, name2, "name");
      if (input4 && !hasProperty(this.fieldCtx, field)) {
        $del(this.form, field);
      }
      this.deferSyncValue(function() {
        if (!_this5.reloading) {
          if (ctx.parser.loadChildren !== false) {
            var children = ctx.getPending("children", ctx.rule.children);
            if (is.trueArray(children)) {
              children.forEach(function(h2) {
                return h2.__fc__ && _this5.rmCtx(h2.__fc__);
              });
            }
          }
          if (ctx.root === _this5.rules) {
            _this5.vm.setupState.renderRule();
          }
        }
      }, input4);
      var index = this.sort.indexOf(id2);
      if (index > -1) {
        this.sort.splice(index, 1);
      }
      this.$render.clearCache(ctx);
      ctx["delete"]();
      this.effect(ctx, "deleted");
      this.targetHook(ctx, "deleted");
      input4 && !this.fieldCtx[field] && this.vm.emit("remove-field", field, ctx.rule, this.api);
      ctx.rule.__ctrl || this.vm.emit("remove-rule", ctx.rule, this.api);
      return ctx;
    }
  });
}
function useLifecycle(Handler2) {
  extend(Handler2.prototype, {
    mounted: function mounted7() {
      var _this = this;
      var _mounted = function _mounted2() {
        _this.isMounted = true;
        _this.lifecycle("mounted");
      };
      if (this.pageEnd) {
        _mounted();
      } else {
        this.bus.$once("page-end", _mounted);
      }
    },
    lifecycle: function lifecycle(name2) {
      this.fc.targetFormDriver(name2, this.api, this.fc);
      this.vm.emit(name2, this.api);
      this.emitEvent(name2, this.api);
    },
    emitEvent: function emitEvent2(name2) {
      var _this$bus;
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      var _fn = this.options[name2] || this.options[toCase("on-" + name2)];
      if (_fn) {
        var fn = parseFn(_fn);
        is.Function(fn) && invoke(function() {
          return fn.apply(void 0, args);
        });
      }
      (_this$bus = this.bus).$emit.apply(_this$bus, [name2].concat(args));
    },
    targetHook: function targetHook(ctx, name2, args) {
      var _ctx$prop, _ctx$prop$hook, _this2 = this;
      var hook = (_ctx$prop = ctx.prop) === null || _ctx$prop === void 0 ? void 0 : (_ctx$prop$hook = _ctx$prop.hook) === null || _ctx$prop$hook === void 0 ? void 0 : _ctx$prop$hook[name2];
      if (hook) {
        hook = Array.isArray(hook) ? hook : [hook];
        hook.forEach(function(fn) {
          invoke(function() {
            return fn(_objectSpread2(_objectSpread2({}, args || {}), {}, {
              rule: ctx.rule,
              api: _this2.api
            }));
          });
        });
      }
    }
  });
}
function useEffect(Handler2) {
  extend(Handler2.prototype, {
    useProvider: function useProvider() {
      var _this = this;
      var ps = this.fc.providers;
      Object.keys(ps).forEach(function(k) {
        var prop = ps[k];
        if (is.Function(prop)) {
          prop = prop(_this.fc);
        }
        prop._c = getComponent(prop);
        _this.onEffect(prop);
        _this.providers[k] = prop;
      });
    },
    onEffect: function onEffect(provider) {
      var _this2 = this;
      var used = [];
      (provider._c || ["*"]).forEach(function(name2) {
        var type2 = name2 === "*" ? "*" : _this2.getType(name2);
        if (used.indexOf(type2) > -1)
          return;
        used.push(type2);
        _this2.bus.$on("p:".concat(provider.name, ":").concat(type2, ":").concat(provider.input ? 1 : 0), function(event, args) {
          provider[event] && provider[event].apply(provider, _toConsumableArray(args));
        });
      });
      provider._used = used;
    },
    watchEffect: function watchEffect2(ctx) {
      var _this3 = this;
      var effect = {
        required: function required2() {
          var _ctx$rule, _ctx$rule$effect;
          return (hasProperty(ctx.rule, "$required") ? ctx.rule["$required"] : (_ctx$rule = ctx.rule) === null || _ctx$rule === void 0 ? void 0 : (_ctx$rule$effect = _ctx$rule.effect) === null || _ctx$rule$effect === void 0 ? void 0 : _ctx$rule$effect.required) || false;
        }
      };
      Object.keys(ctx.rule.effect || {}).forEach(function(k) {
        effect[k] = function() {
          return ctx.rule.effect[k];
        };
      });
      Object.keys(ctx.rule).forEach(function(k) {
        if (k[0] === "$") {
          effect[k.substr(1)] = function() {
            return ctx.rule[k];
          };
        }
      });
      Object.keys(effect).forEach(function(k) {
        ctx.watch.push(watch(effect[k], function(n) {
          _this3.effect(ctx, "watch", _defineProperty({}, k, n));
        }, {
          deep: true
        }));
      });
    },
    ruleEffect: function ruleEffect(rule, event, append) {
      this.emitEffect({
        rule,
        input: !!rule.field,
        type: this.getType(rule.type)
      }, event, append);
    },
    effect: function effect(ctx, event, custom) {
      this.emitEffect({
        rule: ctx.rule,
        input: ctx.input,
        type: ctx.trueType,
        ctx,
        custom
      }, event);
    },
    getEffect: function getEffect(rule, name2) {
      if (hasProperty(rule, "$" + name2)) {
        return rule["$" + name2];
      }
      if (hasProperty(rule, "effect") && hasProperty(rule.effect, name2))
        return rule.effect[name2];
      return void 0;
    },
    emitEffect: function emitEffect(_ref, event, append) {
      var _this4 = this;
      var ctx = _ref.ctx, rule = _ref.rule, input4 = _ref.input, type2 = _ref.type, custom = _ref.custom;
      if (!type2 || ["fcFragment", "fragment"].indexOf(type2) > -1)
        return;
      var effect = custom ? custom : Object.keys(rule).reduce(function(i, k) {
        if (k[0] === "$") {
          i[k.substr(1)] = rule[k];
        }
        return i;
      }, _objectSpread2({}, rule.effect || {}));
      Object.keys(effect).forEach(function(attr) {
        var p = _this4.providers[attr];
        if (!p || p.input && !input4)
          return;
        var _type;
        if (!p._c) {
          _type = "*";
        } else if (p._used.indexOf(type2) > -1) {
          _type = type2;
        } else {
          return;
        }
        var data5 = _objectSpread2({
          value: effect[attr],
          getValue: function getValue() {
            return _this4.getEffect(rule, attr);
          }
        }, append || {});
        if (ctx) {
          data5.getProp = function() {
            return ctx.effectData(attr);
          };
          data5.clearProp = function() {
            return ctx.clearEffectData(attr);
          };
          data5.mergeProp = function(prop) {
            return mergeRule(data5.getProp(), [prop]);
          };
          data5.id = ctx.id;
        }
        _this4.bus.$emit("p:".concat(attr, ":").concat(_type, ":").concat(p.input ? 1 : 0), event, [data5, rule, _this4.api]);
      });
    }
  });
}
function unique(arr) {
  return arr.filter(function(item, index, arr2) {
    return arr2.indexOf(item, 0) === index;
  });
}
function getComponent(p) {
  var c = p.components;
  if (Array.isArray(c)) {
    var arr = unique(c.filter(function(v) {
      return v !== "*";
    }));
    return arr.length ? arr : false;
  } else if (is.String(c))
    return [c];
  else
    return false;
}
function Handler(fc) {
  var _this = this;
  funcProxy(this, {
    options: function options() {
      return fc.options.value || {};
    },
    bus: function bus() {
      return fc.bus;
    },
    preview: function preview2() {
      return fc.vm.props.preview != null ? fc.vm.props.preview : fc.options.value.preview || false;
    }
  });
  extend(this, {
    fc,
    vm: fc.vm,
    watching: false,
    loading: false,
    reloading: false,
    noWatchFn: null,
    deferSyncFn: null,
    isMounted: false,
    formData: reactive({}),
    subRuleData: reactive({}),
    subForm: {},
    form: reactive({}),
    appendData: {},
    ignoreFields: [],
    providers: {},
    cycleLoad: null,
    loadedId: 1,
    nextTick: null,
    changeStatus: false,
    pageEnd: true,
    nextReload: function nextReload() {
      _this.lifecycle("reload");
    }
  });
  this.initData(fc.rules);
  this.$manager = new fc.manager(this);
  this.$render = new Render(this);
  this.api = fc.extendApiFn.reduce(function(api, fn) {
    extend(api, invoke(function() {
      return fn(api, _this);
    }, {}));
    return api;
  }, Api(this));
}
extend(Handler.prototype, {
  initData: function initData(rules) {
    extend(this, {
      ctxs: {},
      fieldCtx: {},
      nameCtx: {},
      sort: [],
      rules
    });
  },
  init: function init2() {
    this.updateAppendData();
    this.useProvider();
    this.usePage();
    this.loadRule();
    this.$manager.__init();
    this.lifecycle("created");
  },
  updateAppendData: function updateAppendData() {
    this.appendData = _objectSpread2(_objectSpread2(_objectSpread2({}, this.options.formData || {}), this.fc.vm.props.modelValue || {}), this.appendData);
  },
  isBreakWatch: function isBreakWatch() {
    return this.loading || this.noWatchFn || this.reloading;
  },
  beforeFetch: function beforeFetch(opt) {
    var _this2 = this;
    return new Promise(function(resolve) {
      var source = _this2.options.beforeFetch && invoke(function() {
        return _this2.options.beforeFetch(opt, {
          api: _this2.api
        });
      });
      if (source && is.Function(source.then)) {
        source.then(resolve);
      } else {
        resolve();
      }
    });
  }
});
useInject(Handler);
usePage(Handler);
useRender(Handler);
useLoader(Handler);
useInput(Handler);
useContext(Handler);
useLifecycle(Handler);
useEffect(Handler);
var NAME = "fcFragment";
var fragment = defineComponent({
  name: NAME,
  inheritAttrs: false,
  props: ["vnode"],
  render: function render11() {
    return this.vnode;
  }
});
function tidyDirectives(directives) {
  return Object.keys(directives).map(function(n) {
    var data5 = directives[n];
    var directive = resolveDirective(n);
    if (!directive)
      return;
    return [directive, data5.value, data5.arg, data5.modifiers];
  }).filter(function(v) {
    return !!v;
  });
}
function makeDirective(data5, vn) {
  var directives = data5.directives;
  if (!directives)
    return vn;
  if (!Array.isArray(directives)) {
    directives = [directives];
  }
  return withDirectives(vn, directives.reduce(function(lst, v) {
    return lst.concat(tidyDirectives(v));
  }, []));
}
function CreateNodeFactory() {
  var aliasMap = {};
  function CreateNode() {
  }
  extend(CreateNode.prototype, {
    make: function make(tag, data5, children) {
      return makeDirective(data5, this.h(tag, toProps(data5), children));
    },
    makeComponent: function makeComponent(type2, data5, children) {
      try {
        return makeDirective(data5, createVNode(type2, toProps(data5), children));
      } catch (e) {
        console.error(e);
        return createVNode("");
      }
    },
    h: function h2(tag, data5, children) {
      var isNativeTag = getCurrentInstance().appContext.config.isNativeTag(tag);
      if (isNativeTag) {
        delete data5.formCreateInject;
      }
      try {
        return createVNode(isNativeTag ? tag : resolveComponent(tag), data5, children);
      } catch (e) {
        console.error(e);
        return createVNode("");
      }
    },
    aliasMap
  });
  extend(CreateNode, {
    aliasMap,
    alias: function alias2(_alias, name2) {
      aliasMap[_alias] = name2;
    },
    use: function use(nodes) {
      Object.keys(nodes).forEach(function(k) {
        var line = toLine(k);
        var lower2 = toString(k).toLocaleLowerCase();
        var v = nodes[k];
        [k, line, lower2].forEach(function(n) {
          CreateNode.alias(k, v);
          CreateNode.prototype[n] = function(data5, children) {
            return this.make(v, data5, children);
          };
        });
      });
    }
  });
  return CreateNode;
}
function createManager(proto) {
  var CustomManager = function(_Manager) {
    _inherits(CustomManager2, _Manager);
    var _super = _createSuper(CustomManager2);
    function CustomManager2() {
      _classCallCheck(this, CustomManager2);
      return _super.apply(this, arguments);
    }
    return CustomManager2;
  }(Manager);
  Object.assign(CustomManager.prototype, proto);
  return CustomManager;
}
function Manager(handler3) {
  extend(this, {
    $handle: handler3,
    vm: handler3.vm,
    options: {},
    ref: "fcForm",
    mergeOptionsRule: {
      normal: ["form", "row", "info", "submitBtn", "resetBtn"]
    }
  });
  this.updateKey();
  this.init();
}
extend(Manager.prototype, {
  __init: function __init() {
    var _this = this;
    this.$render = this.$handle.$render;
    this.$r = function() {
      var _this$$render;
      return (_this$$render = _this.$render).renderRule.apply(_this$$render, arguments);
    };
  },
  updateKey: function updateKey2() {
    this.key = uniqueId();
  },
  //TODO interface
  init: function init3() {
  },
  update: function update3() {
  },
  beforeRender: function beforeRender() {
  },
  form: function form() {
    return this.vm.refs[this.ref];
  },
  getSlot: function getSlot2(name2) {
    var _fn = function _fn2(vm) {
      if (vm) {
        var slot = vm.slots[name2];
        if (slot) {
          return slot;
        }
        return _fn2(vm.setupState.parent);
      }
      return void 0;
    };
    return _fn(this.vm);
  },
  mergeOptions: function mergeOptions(args, opt) {
    var _this2 = this;
    return mergeProps2(args.map(function(v) {
      return _this2.tidyOptions(v);
    }), opt, this.mergeOptionsRule);
  },
  updateOptions: function updateOptions(options) {
    this.$handle.fc.targetFormDriver("updateOptions", options, {
      handle: this.$handle,
      api: this.$handle.api
    });
    this.options = this.mergeOptions([options], this.getDefaultOptions());
    this.update();
  },
  tidyOptions: function tidyOptions(options) {
    return options;
  },
  tidyRule: function tidyRule(ctx) {
  },
  mergeProp: function mergeProp3(ctx) {
  },
  getDefaultOptions: function getDefaultOptions() {
    return {};
  },
  render: function render12(children) {
  }
});
var loadData = function loadData2(fc) {
  var loadData3 = {
    name: "loadData",
    _fn: [],
    mounted: function mounted7(inject2, rule, api) {
      this.deleted(inject2);
      var attrs2 = toArray(inject2.getValue());
      var unwatchs = [];
      attrs2.forEach(function(attr) {
        if (attr && (attr.attr || attr.template)) {
          var fn = function fn2(get) {
            var value;
            if (attr.template) {
              value = fc.$handle.loadStrVar(attr.template, get);
            } else {
              value = get(attr.attr, attr["default"]);
            }
            if (attr.copy !== false) {
              value = deepCopy(value);
            }
            var _rule = attr.modify ? rule : inject2.getProp();
            if (attr.to === "child") {
              if (_rule.children) {
                _rule.children[0] = value;
              } else {
                _rule.children = [value];
              }
            } else {
              deepSet(_rule, attr.to || "options", value);
            }
            api.sync(rule);
          };
          var callback = function callback2(get) {
            return fn(get);
          };
          var unwatch2 = fc.watchLoadData(callback);
          fn = debounce(fn, attr.wait || 300);
          if (attr.watch !== false) {
            unwatchs.push(unwatch2);
          } else {
            unwatch2();
          }
        }
      });
      this._fn[inject2.id] = unwatchs;
    },
    deleted: function deleted(inject2) {
      if (this._fn[inject2.id]) {
        this._fn[inject2.id].forEach(function(un) {
          un();
        });
      }
      inject2.clearProp();
    }
  };
  loadData3.watch = loadData3.mounted;
  return loadData3;
};
var t = function t2(fc) {
  var t3 = {
    name: "t",
    _fn: [],
    loaded: function loaded(inject2, rule, api) {
      this.deleted(inject2);
      var attrs2 = inject2.getValue() || {};
      var unwatchs = [];
      Object.keys(attrs2).forEach(function(key) {
        var attr = attrs2[key];
        if (attr) {
          var isObj = _typeof(attr) === "object";
          var fn = function fn2(get) {
            var value = fc.t(isObj ? attr.attr : attr, isObj ? attr.params : null, get);
            var _rule = isObj && attr.modify ? rule : inject2.getProp();
            if (key === "child") {
              if (_rule.children) {
                _rule.children[0] = value;
              } else {
                _rule.children = [value];
              }
            } else {
              deepSet(_rule, key, value);
            }
            api.sync(rule);
          };
          var callback = function callback2(get) {
            return fn(get);
          };
          var unwatch2 = fc.watchLoadData(callback);
          fn = debounce(fn, attr.wait || 300);
          if (attr.watch !== false) {
            unwatchs.push(unwatch2);
          } else {
            unwatch2();
          }
        }
      });
      this._fn[inject2.id] = unwatchs;
    },
    deleted: function deleted(inject2) {
      if (this._fn[inject2.id]) {
        this._fn[inject2.id].forEach(function(un) {
          un();
        });
      }
      inject2.clearProp();
    }
  };
  t3.watch = t3.loaded;
  return t3;
};
var componentValidate = {
  name: "componentValidate",
  load: function load(attr, rule, api) {
    var options = attr.getValue();
    if (!options || options.method === false) {
      attr.clearProp();
      api.clearValidateState([rule.field]);
    } else {
      if (!is.Object(options)) {
        options = {
          method: options
        };
      }
      var method = options.method;
      delete options.method;
      attr.getProp().validate = [_objectSpread2(_objectSpread2({}, options), {}, {
        validator: function validator() {
          var ctx = byCtx(rule);
          if (ctx) {
            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
              args[_key] = arguments[_key];
            }
            return api.exec.apply(api, [ctx.id, is.String(method) ? method : "formCreateValidate"].concat(args, [{
              attr,
              rule,
              api
            }]));
          }
        }
      })];
    }
  },
  watch: function watch2() {
    componentValidate.load.apply(componentValidate, arguments);
  }
};
var fetch = function fetch2(fc) {
  function parseOpt(option) {
    if (is.String(option)) {
      option = {
        action: option,
        to: "options"
      };
    }
    return option;
  }
  function run(inject2, rule, api) {
    var option = inject2.value;
    fetchAttr.deleted(inject2);
    if (is.Function(option)) {
      option = option(rule, api);
    }
    option = parseOpt(option);
    var set = function set2(val) {
      if (val === void 0) {
        inject2.clearProp();
      } else {
        deepSet(inject2.getProp(), option.to || "options", val);
      }
      api.sync(rule);
    };
    if (!option || !option.action && !option.key) {
      set(void 0);
      return;
    }
    option = deepCopy(option);
    if (!option.to) {
      option.to = "options";
    }
    if (option.key) {
      var item = fc.$handle.options.globalData[option.key];
      if (!item) {
        set(void 0);
        return;
      }
      if (item.type === "static") {
        set(item.data);
        return;
      } else {
        option = _objectSpread2(_objectSpread2({}, option), item);
      }
    }
    var _onError = option.onError;
    var check2 = function check3() {
      if (!inject2.getValue()) {
        inject2.clearProp();
        api.sync(rule);
        return true;
      }
    };
    fetchAttr._fn[inject2.id] = fc.watchLoadData(debounce(function(get, change) {
      if (change && option.watch === false) {
        return fetchAttr._fn[inject2.id]();
      }
      var _option = fc.$handle.loadFetchVar(deepCopy(option), get);
      var config = _objectSpread2(_objectSpread2({
        headers: {}
      }, _option), {}, {
        onSuccess: function onSuccess(body, flag) {
          if (check2())
            return;
          var fn = function fn2(v) {
            return flag ? v : hasProperty(v, "data") ? v.data : v;
          };
          var parse = parseFn(_option.parse);
          if (is.Function(parse)) {
            fn = parse;
          } else if (parse && is.String(parse)) {
            fn = function fn2(v) {
              return deepGet(v, parse);
            };
          }
          set(fn(body, rule, api));
          api.sync(rule);
        },
        onError: function onError(e) {
          set(void 0);
          if (check2())
            return;
          (_onError || function(e2) {
            return err(e2.message || "fetch fail " + _option.action);
          })(e, rule, api);
        }
      });
      fc.$handle.beforeFetch(config, {
        rule,
        api
      }).then(function() {
        if (is.Function(_option.action)) {
          _option.action(rule, api).then(function(val) {
            config.onSuccess(val, true);
          })["catch"](function(e) {
            config.onError(e);
          });
          return;
        }
        invoke(function() {
          return fc.create.fetch(config, {
            inject: inject2,
            rule,
            api
          });
        });
      });
    }, option.wait || 600));
  }
  var fetchAttr = {
    name: "fetch",
    _fn: [],
    mounted: function mounted7() {
      run.apply(void 0, arguments);
    },
    watch: function watch4() {
      run.apply(void 0, arguments);
    },
    deleted: function deleted(inject2) {
      if (this._fn[inject2.id]) {
        this._fn[inject2.id]();
      }
      inject2.clearProp();
    }
  };
  return fetchAttr;
};
var $provider = {
  fetch,
  loadData,
  t,
  componentValidate
};
var name$6 = "html";
var html = {
  name: name$6,
  loadChildren: false,
  render: function render13(children, ctx) {
    ctx.prop.props.innerHTML = children["default"]();
    return ctx.vNode.make(ctx.prop.props.tag || "div", ctx.prop);
  },
  renderChildren: function renderChildren(children) {
    return {
      "default": function _default15() {
        return children.filter(function(v) {
          return is.String(v);
        }).join("");
      }
    };
  }
};
function getCookie(name2) {
  name2 = name2 + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var cookieArray = decodedCookie.split(";");
  for (var i = 0; i < cookieArray.length; i++) {
    var cookie = cookieArray[i];
    while (cookie.charAt(0) === " ") {
      cookie = cookie.substring(1);
    }
    if (cookie.indexOf(name2) === 0) {
      cookie = cookie.substring(name2.length, cookie.length);
      try {
        return JSON.parse(cookie);
      } catch (e) {
        return cookie;
      }
    }
  }
  return null;
}
function getLocalStorage(name2) {
  var value = localStorage.getItem(name2);
  if (value) {
    try {
      return JSON.parse(value);
    } catch (e) {
      return value;
    }
  }
  return null;
}
function baseDriver(driver, name2) {
  if (!name2) {
    return null;
  }
  var split = name2.split(".");
  var value = driver(split.shift());
  if (!split.length) {
    return value;
  }
  if (value == null) {
    return null;
  }
  return deepGet(value, split);
}
function cookieDriver(name2) {
  return baseDriver(getCookie, name2);
}
function localStorageDriver(name2) {
  return baseDriver(getLocalStorage, name2);
}
function parseProp(name2, id2) {
  var prop;
  if (arguments.length === 2) {
    prop = arguments[1];
    id2 = prop[name2];
  } else {
    prop = arguments[2];
  }
  return {
    id: id2,
    prop
  };
}
function nameProp() {
  return parseProp.apply(void 0, ["name"].concat(Array.prototype.slice.call(arguments)));
}
function exportAttrs(attrs2) {
  var key = attrs2.key || [];
  var array = attrs2.array || [];
  var normal = attrs2.normal || [];
  keyAttrs.push.apply(keyAttrs, _toConsumableArray(key));
  arrayAttrs.push.apply(arrayAttrs, _toConsumableArray(array));
  normalAttrs.push.apply(normalAttrs, _toConsumableArray(normal));
  appendProto([].concat(_toConsumableArray(key), _toConsumableArray(array), _toConsumableArray(normal)));
}
var id = 1;
var instance = {};
function FormCreateFactory(config) {
  var components2 = _defineProperty({}, fragment.name, fragment);
  var parsers2 = {};
  var directives = {};
  var modelFields = {};
  var drivers = {};
  var useApps = [];
  var listener = [];
  var extendApiFn = [config.extendApi];
  var providers = _objectSpread2({}, $provider);
  var maker2 = makerFactory();
  var globalConfig = {
    global: {}
  };
  var loadData3 = reactive({});
  var CreateNode = CreateNodeFactory();
  var formulas = {};
  var isMobile = config.isMobile === true;
  var prototype = {};
  exportAttrs(config.attrs || {});
  function getApi(name2) {
    var val = instance[name2];
    if (Array.isArray(val)) {
      return val.map(function(v) {
        return v.api();
      });
    } else if (val) {
      return val.api();
    }
  }
  function useApp(fn) {
    useApps.push(fn);
  }
  function directive() {
    var data5 = nameProp.apply(void 0, arguments);
    if (data5.id && data5.prop)
      directives[data5.id] = data5.prop;
  }
  function register() {
    var data5 = nameProp.apply(void 0, arguments);
    if (data5.id && data5.prop)
      providers[data5.id] = is.Function(data5.prop) ? data5.prop : _objectSpread2(_objectSpread2({}, data5.prop), {}, {
        name: data5.id
      });
  }
  function componentAlias(alias2) {
    CreateNode.use(alias2);
  }
  function parser() {
    var data5 = nameProp.apply(void 0, arguments);
    if (!data5.id || !data5.prop)
      return BaseParser;
    var name2 = toCase(data5.id);
    var parser2 = data5.prop;
    var base = parser2.merge === true ? parsers2[name2] : void 0;
    parsers2[name2] = setPrototypeOf(parser2, base || BaseParser);
    maker2[name2] = creatorFactory(name2);
    parser2.maker && extend(maker2, parser2.maker);
  }
  function component(id2, component2) {
    var name2;
    if (is.String(id2)) {
      name2 = id2;
      if (component2 === void 0) {
        return components2[name2];
      }
    } else {
      name2 = id2.displayName || id2.name;
      component2 = id2;
    }
    if (!name2 || !component2)
      return;
    var nameAlias = toCase(name2);
    components2[name2] = component2;
    components2[nameAlias] = component2;
    delete CreateNode.aliasMap[name2];
    delete CreateNode.aliasMap[nameAlias];
    delete parsers2[name2];
    delete parsers2[nameAlias];
    if (component2.formCreateParser)
      parser(name2, component2.formCreateParser);
  }
  function $form() {
    return $FormCreate(FormCreate2, components2, directives);
  }
  function createFormApp(rule, option) {
    var Type = $form();
    return createApp({
      data: function data5() {
        return reactive({
          rule,
          option
        });
      },
      render: function render17() {
        return h(Type, _objectSpread2({
          ref: "fc"
        }, this.$data));
      }
    });
  }
  function $vnode() {
    return fragment;
  }
  function use(fn, opt) {
    if (is.Function(fn.install))
      fn.install(create2, opt);
    else if (is.Function(fn))
      fn(create2, opt);
    return this;
  }
  function create2(rules, option) {
    var app = createFormApp(rules, option || {});
    useApps.forEach(function(v) {
      invoke(function() {
        return v(create2, app);
      });
    });
    var div = document.createElement("div");
    ((option === null || option === void 0 ? void 0 : option.el) || document.body).appendChild(div);
    var vm = app.mount(div);
    return vm.$refs.fc.fapi;
  }
  setPrototypeOf(create2, prototype);
  function factory(inherit2) {
    var _config = _objectSpread2({}, config);
    if (inherit2) {
      _config.inherit = {
        components: components2,
        parsers: parsers2,
        directives,
        modelFields,
        providers,
        useApps,
        maker: maker2,
        formulas,
        loadData: loadData3
      };
    } else {
      delete _config.inherit;
    }
    return FormCreateFactory(_config);
  }
  function setModelField(name2, field) {
    modelFields[name2] = field;
  }
  function setFormula(name2, fn) {
    formulas[name2] = fn;
  }
  function setDriver(name2, driver) {
    var parent = drivers[name2] || {};
    var parsers3 = parent.parsers || {};
    if (driver.parsers) {
      Object.keys(driver.parsers).forEach(function(k) {
        parsers3[k] = setPrototypeOf(driver.parsers[k], BaseParser);
      });
    }
    driver.name = name2;
    drivers[name2] = _objectSpread2(_objectSpread2(_objectSpread2({}, parent), driver), {}, {
      parsers: parsers3
    });
  }
  function refreshData(id2) {
    if (id2) {
      Object.keys(instance).forEach(function(v) {
        var apis = Array.isArray(instance[v]) ? instance[v] : [instance[v]];
        apis.forEach(function(that) {
          that.bus.$emit("$loadData." + id2);
        });
      });
    }
  }
  function _setData(id2, data5) {
    deepSet(loadData3, id2, data5);
    refreshData(id2);
  }
  function setDataDriver(id2, data5) {
    var callback = function callback2() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      return invoke(function() {
        return data5.apply(void 0, args);
      });
    };
    callback._driver = true;
    _setData(id2, callback);
  }
  function getData(id2, def) {
    var split = (id2 || "").split(".");
    id2 = split.shift();
    var field = split.join(".");
    if (hasProperty(loadData3, id2)) {
      var val = loadData3[id2];
      if (val && val._driver) {
        val = val(field);
      } else if (split.length) {
        val = deepGet(val, split);
      }
      return val == null || val === "" ? def : val;
    } else {
      return def;
    }
  }
  function extendApi2(fn) {
    extendApiFn.push(fn);
  }
  function removeData(id2) {
    delete loadData3[id2];
    refreshData(id2);
  }
  function on2(name2, callback) {
    listener.push({
      name: name2,
      callback
    });
  }
  function FormCreate2(vm) {
    var _this = this;
    extend(this, {
      id: id++,
      create: create2,
      vm,
      manager: createManager(config.manager),
      parsers: parsers2,
      providers,
      modelFields,
      formulas,
      isMobile,
      rules: vm.props.rule,
      name: vm.props.name || uniqueId(),
      inFor: vm.props.inFor,
      prop: {
        components: components2,
        directives
      },
      drivers,
      renderDriver: null,
      refreshData,
      loadData: loadData3,
      CreateNode,
      bus: new Mitt(),
      unwatch: [],
      options: ref({}),
      extendApiFn,
      fetchCache: /* @__PURE__ */ new WeakMap(),
      tmpData: reactive({})
    });
    listener.forEach(function(item) {
      _this.bus.$on(item.name, item.callback);
    });
    nextTick(function() {
      watch(_this.options, function() {
        _this.$handle.$manager.updateOptions(_this.options.value);
        _this.api().refresh();
      }, {
        deep: true
      });
    });
    extend(vm.appContext.components, components2);
    extend(vm.appContext.directives, directives);
    this.$handle = new Handler(this);
    if (this.name) {
      if (this.inFor) {
        if (!instance[this.name])
          instance[this.name] = [];
        instance[this.name].push(this);
      } else {
        instance[this.name] = this;
      }
    }
  }
  FormCreate2.isMobile = isMobile;
  extend(FormCreate2.prototype, {
    init: function init4() {
      var _this2 = this;
      if (this.isSub()) {
        this.unwatch.push(watch(function() {
          return _this2.vm.setupState.parent.setupState.fc.options.value;
        }, function() {
          _this2.initOptions();
          _this2.$handle.api.refresh();
        }, {
          deep: true
        }));
      }
      if (this.vm.props.driver) {
        this.renderDriver = _typeof(this.vm.props.driver) === "object" ? this.vm.props.driver : this.drivers[this.vm.props.driver];
      }
      if (!this.renderDriver && this.vm.setupState.parent) {
        this.renderDriver = this.vm.setupState.parent.setupState.fc.renderDriver;
      }
      if (!this.renderDriver) {
        this.renderDriver = this.drivers["default"];
      }
      this.initOptions();
      this.$handle.init();
    },
    targetFormDriver: function targetFormDriver(method) {
      var _this3 = this;
      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
        args[_key2 - 1] = arguments[_key2];
      }
      if (this.renderDriver && this.renderDriver[method]) {
        return invoke(function() {
          var _this3$renderDriver;
          return (_this3$renderDriver = _this3.renderDriver)[method].apply(_this3$renderDriver, args);
        });
      }
    },
    t: function t3(id2, params, get) {
      var value = get ? get("$t." + id2) : this.globalLanguageDriver(id2);
      if (value == null && this.vm.appContext.config.globalProperties.$i18n) {
        return this.vm.appContext.config.globalProperties.$i18n.t(id2, params);
      }
      if (value == null) {
        value = "";
      }
      if (value && params) {
        Object.keys(params).forEach(function(param) {
          var regex = new RegExp("{".concat(param, "}"), "g");
          value = value.replace(regex, params[param]);
        });
      }
      return value;
    },
    globalDataDriver: function globalDataDriver(id2) {
      var _this4 = this;
      var split = id2.split(".");
      var key = split.shift();
      var option = this.options.value.globalData && this.options.value.globalData[key];
      if (option) {
        if (option.type === "static") {
          return deepGet(option.data, split);
        } else {
          var val;
          var res = this.fetchCache.get(option);
          if (res) {
            if (res.status) {
              val = deepGet(res.data, split);
            }
            if (!res.loading) {
              return val;
            }
            res.loading = false;
            this.fetchCache.set(option, res);
          } else {
            this.fetchCache.set(option, {
              status: false
            });
          }
          var reload = debounce(function() {
            unwatch2();
            var res2 = _this4.fetchCache.get(option);
            if (_this4.options.value.globalData && Object.values(_this4.options.value.globalData).indexOf(option) !== -1) {
              if (res2) {
                res2.loading = true;
                _this4.fetchCache.set(option, res2);
              }
              _this4.bus.$emit("$loadData.$globalData." + key);
            } else {
              _this4.fetchCache["delete"](option);
            }
          }, option.wait || 600);
          var _emit = function _emit2(data5) {
            _this4.fetchCache.set(option, {
              status: true,
              data: data5
            });
            _this4.bus.$emit("$loadData.$globalData." + key);
          };
          var callback = function callback2(get, change) {
            if (change && option.watch === false) {
              return unwatch2();
            }
            if (change) {
              reload();
              return;
            }
            var options = _this4.$handle.loadFetchVar(copy$1(option), get);
            _this4.$handle.api.fetch(options).then(function(res2) {
              _emit(res2);
            })["catch"](function(e) {
              _emit(null);
            });
          };
          var unwatch2 = this.watchLoadData(callback);
          this.unwatch.push(unwatch2);
          return val;
        }
      }
    },
    getLocale: function getLocale() {
      var locale = this.vm.setupState.top.props.locale;
      if (locale && _typeof(locale) === "object") {
        return locale.name;
      }
      if (typeof locale === "string") {
        return locale;
      }
      if (this.vm.appContext.config.globalProperties.$i18n && this.vm.appContext.config.globalProperties.$i18n.locale) {
        return this.vm.appContext.config.globalProperties.$i18n.locale;
      }
      return "zh-cn";
    },
    globalLanguageDriver: function globalLanguageDriver(id2) {
      var locale = this.vm.setupState.top.props.locale;
      var value = void 0;
      if (locale && _typeof(locale) === "object") {
        value = deepGet(locale, id2);
      }
      if (value == null) {
        var language = this.options.value.language || {};
        var _locale = this.getLocale();
        value = deepGet(language[_locale], id2);
      }
      return value;
    },
    globalVarDriver: function globalVarDriver(id2) {
      var _this5 = this;
      var split = id2.split(".");
      var key = split.shift();
      var option = this.options.value.globalVariable && this.options.value.globalVariable[key];
      if (option) {
        var handle = is.Function(option) ? option : option.handle;
        if (handle) {
          var val;
          var unwatch2 = this.watchLoadData(function(get, change) {
            if (change) {
              unwatch2();
              _this5.bus.$emit("$loadData.$var." + key);
              return val;
            }
            val = invoke(function() {
              return handle(get, _this5.$handle.api);
            });
          });
          this.unwatch.push(unwatch2);
          return val;
        }
      }
    },
    setData: function setData(id2, data5, isGlobal) {
      if (!isGlobal) {
        deepSet(this.vm.setupState.top.setupState.fc.tmpData, id2, data5);
        this.bus.$emit("$loadData." + id2);
      } else {
        _setData(id2, data5);
      }
    },
    getLoadData: function getLoadData(id2, def) {
      var val = null;
      if (id2 != null) {
        var split = id2.split(".");
        var key = split.shift();
        if (key === "$topForm") {
          val = this.$handle.api.top.formData();
        } else if (key === "$form") {
          val = this.$handle.api.formData();
        } else if (key === "$options") {
          val = this.options.value;
        } else if (key === "$globalData") {
          val = this.globalDataDriver(split.join("."));
          split = [];
        } else if (key === "$var") {
          val = this.globalVarDriver(split.join("."));
          split = [];
        } else if (key === "$locale") {
          val = this.getLocale();
          split = [];
        } else if (key === "$t") {
          val = this.globalLanguageDriver(split.join("."));
          split = [];
        } else {
          var tmpData = this.vm.setupState.top.setupState.fc.tmpData;
          val = hasProperty(tmpData, key) ? deepGet(tmpData, id2) : getData(id2);
          split = [];
        }
        if (val && split.length) {
          val = deepGet(val, split);
        }
      }
      return val == null || val === "" ? def : val;
    },
    watchLoadData: function watchLoadData(fn) {
      var _this6 = this;
      var unwatch2 = {};
      var run = function run2(flag) {
        invoke(function() {
          fn(get, flag);
        });
      };
      var get = function get2(id2, def) {
        if (unwatch2[id2]) {
          return unwatch2[id2].val;
        }
        var data5 = computed(function() {
          return _this6.getLoadData(id2, def);
        });
        var split = id2.split(".");
        var key = split.shift();
        var key2 = split.shift() || "";
        var callback = debounce(function() {
          var temp = _this6.getLoadData(id2, def);
          if (!unwatch2[id2]) {
            return;
          } else if (JSON.stringify(temp) !== JSON.stringify(unwatch2[id2].val)) {
            unwatch2[id2].val = temp;
            run(true);
          }
        }, 0);
        var un2 = watch(data5, function(n) {
          callback();
        });
        _this6.bus.$on("$loadData." + key, callback);
        if (key2) {
          _this6.bus.$on("$loadData." + key + "." + key2, callback);
        }
        unwatch2[id2] = {
          fn: function fn2() {
            _this6.bus.$off("$loadData." + key, callback);
            if (key2) {
              _this6.bus.$off("$loadData." + key + "." + key2, callback);
            }
            un2();
          },
          val: data5.value
        };
        return data5.value;
      };
      run(false);
      var un = function un2() {
        Object.keys(unwatch2).forEach(function(k) {
          return unwatch2[k].fn();
        });
        unwatch2 = {};
      };
      this.unwatch.push(un);
      return un;
    },
    isSub: function isSub() {
      return this.vm.setupState.parent && this.vm.props.extendOption;
    },
    initOptions: function initOptions() {
      this.options.value = {};
      var options = _objectSpread2({
        formData: {},
        submitBtn: {},
        resetBtn: {},
        globalEvent: {},
        globalData: {}
      }, deepCopy(globalConfig));
      if (this.isSub()) {
        options = this.mergeOptions(options, this.vm.setupState.parent.setupState.fc.options.value || {}, true);
      }
      options = this.mergeOptions(options, this.vm.props.option);
      this.updateOptions(options);
    },
    mergeOptions: function mergeOptions2(target, opt, parent) {
      opt = deepCopy(opt);
      parent && ["page", "onSubmit", "mounted", "reload", "formData", "el", "globalClass", "style"].forEach(function(n) {
        delete opt[n];
      });
      if (opt.global) {
        target.global = mergeGlobal(target.global, opt.global);
        delete opt.global;
      }
      this.$handle.$manager.mergeOptions([opt], target);
      return target;
    },
    updateOptions: function updateOptions2(options) {
      this.options.value = this.mergeOptions(this.options.value, options);
      this.$handle.$manager.updateOptions(this.options.value);
      this.bus.$emit("$loadData.$options");
    },
    api: function api() {
      return this.$handle.api;
    },
    render: function render17() {
      return this.$handle.render();
    },
    mounted: function mounted7() {
      this.$handle.mounted();
    },
    unmount: function unmount() {
      var _this7 = this;
      if (this.name) {
        if (this.inFor) {
          var idx = instance[this.name].indexOf(this);
          instance[this.name].splice(idx, 1);
        } else {
          delete instance[this.name];
        }
      }
      listener.forEach(function(item) {
        _this7.bus.$off(item.name, item.callback);
      });
      this.tmpData = {};
      this.unwatch.forEach(function(fn) {
        return fn();
      });
      this.unwatch = [];
      this.$handle.reloadRule([]);
    },
    updated: function updated() {
      var _this8 = this;
      this.$handle.bindNextTick(function() {
        return _this8.bus.$emit("next-tick", _this8.$handle.api);
      });
    }
  });
  function useAttr(formCreate) {
    extend(formCreate, {
      version: config.version,
      ui: config.ui,
      isMobile,
      extendApi: extendApi2,
      getData,
      setDataDriver,
      setData: _setData,
      removeData,
      refreshData,
      maker: maker2,
      component,
      directive,
      setModelField,
      setFormula,
      setDriver,
      register,
      $vnode,
      parser,
      use,
      factory,
      componentAlias,
      copyRule,
      copyRules,
      mergeRule,
      fetch: fetch$1,
      $form,
      parseFn,
      parseJson,
      toJson,
      useApp,
      getApi,
      on: on2
    });
  }
  function useStatic(formCreate) {
    extend(formCreate, {
      create: create2,
      install: function install2(app, options) {
        globalConfig = _objectSpread2(_objectSpread2({}, globalConfig), options || {});
        var key = "_installedFormCreate_" + config.ui;
        if (app[key] === true)
          return;
        app[key] = true;
        var $formCreate = function $formCreate2(rules) {
          var opt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          return create2(rules, opt);
        };
        useAttr($formCreate);
        app.config.globalProperties.$formCreate = $formCreate;
        var $component = $form();
        app.component($component.name, $component);
        useApps.forEach(function(v) {
          invoke(function() {
            return v(formCreate, app);
          });
        });
      }
    });
  }
  useAttr(prototype);
  useStatic(prototype);
  setDataDriver("$cookie", cookieDriver);
  setDataDriver("$localStorage", localStorageDriver);
  CreateNode.use({
    fragment: "fcFragment"
  });
  config.install && create2.use(config);
  useApp(function(_7, app) {
    app.mixin({
      props: ["formCreateInject"]
    });
  });
  parser(html);
  if (config.inherit) {
    var inherit = config.inherit;
    inherit.components && extend(components2, inherit.components);
    inherit.parsers && extend(parsers2, inherit.parsers);
    inherit.directives && extend(directives, inherit.directives);
    inherit.modelFields && extend(modelFields, inherit.modelFields);
    inherit.providers && extend(providers, inherit.providers);
    inherit.useApps && extend(useApps, inherit.useApps);
    inherit.maker && extend(maker2, inherit.maker);
    inherit.loadData && extend(loadData3, inherit.loadData);
    inherit.formulas && extend(formulas, inherit.formulas);
  }
  var FcComponent = $form();
  setPrototypeOf(FcComponent, prototype);
  Object.defineProperties(FcComponent, {
    fetch: {
      get: function get() {
        return prototype.fetch;
      },
      set: function set(val) {
        prototype.fetch = val;
      }
    }
  });
  FcComponent.util = prototype;
  return FcComponent;
}
var DEFAULT_FORMATS = {
  date: "YYYY-MM-DD",
  month: "YYYY-MM",
  week: "YYYY-wo",
  datetime: "YYYY-MM-DD HH:mm:ss",
  timerange: "HH:mm:ss",
  daterange: "YYYY-MM-DD",
  monthrange: "YYYY-MM",
  datetimerange: "YYYY-MM-DD HH:mm:ss",
  year: "YYYY"
};
var name$5 = "datePicker";
var datePicker = {
  name: name$5,
  maker: function() {
    return ["year", "month", "date", "dates", "week", "datetime", "datetimeRange", "dateRange", "monthRange"].reduce(function(initial, type2) {
      initial[type2] = creatorFactory(name$5, {
        type: type2.toLowerCase()
      });
      return initial;
    }, {});
  }(),
  mergeProp: function mergeProp4(ctx) {
    var props = ctx.prop.props;
    if (!props.valueFormat) {
      props.valueFormat = DEFAULT_FORMATS[props.type] || DEFAULT_FORMATS["date"];
    }
  }
};
var name$4 = "hidden";
var hidden = {
  name: name$4,
  maker: _defineProperty({}, name$4, function(field, value) {
    return creatorFactory(name$4)("", field, value);
  }),
  render: function render14() {
    return [];
  }
};
var name$3 = "input";
var input3 = {
  name: name$3,
  maker: function() {
    var maker2 = ["password", "url", "email", "text", "textarea"].reduce(function(maker3, type2) {
      maker3[type2] = creatorFactory(name$3, {
        type: type2
      });
      return maker3;
    }, {});
    maker2.idate = creatorFactory(name$3, {
      type: "date"
    });
    return maker2;
  }(),
  mergeProp: function mergeProp5(ctx) {
    var props = ctx.prop.props;
    if (props && props.autosize && props.autosize.minRows) {
      props.rows = props.autosize.minRows || 2;
    }
  }
};
var name$2 = "slider";
var slider = {
  name: name$2,
  maker: {
    sliderRange: creatorFactory(name$2, {
      range: true
    })
  },
  toFormValue: function toFormValue3(value, ctx) {
    var isArr = Array.isArray(value), props = ctx.prop.props, min = props.min || 0, parseValue;
    if (props.range === true) {
      parseValue = isArr ? value : [min, parseFloat(value) || min];
    } else {
      parseValue = isArr ? parseFloat(value[0]) || min : parseFloat(value);
    }
    return parseValue;
  }
};
var name$1 = "timePicker";
var timePicker = {
  name: name$1,
  maker: {
    time: creatorFactory(name$1, function(m) {
      return m.props.isRange = false;
    }),
    timeRange: creatorFactory(name$1, function(m) {
      return m.props.isRange = true;
    })
  },
  mergeProp: function mergeProp6(ctx) {
    var props = ctx.prop.props;
    if (!props.valueFormat) {
      props.valueFormat = "HH:mm:ss";
    }
  }
};
var row = {
  name: "FcRow",
  render: function render15(_7, ctx) {
    return ctx.vNode.col({
      props: {
        span: 24
      }
    }, {
      "default": function _default15() {
        return [ctx.vNode.row(ctx.prop, _7)];
      }
    });
  }
};
var name = "select";
var select = {
  name,
  toFormValue: function toFormValue4(value, ctx) {
    if (ctx.prop.props.multiple && !Array.isArray(value)) {
      return toArray(value);
    } else {
      return value;
    }
  }
};
var parsers = [datePicker, hidden, input3, slider, timePicker, row, select];
var PRE = "el";
var alias = {
  button: PRE + "-button",
  icon: PRE + "-icon",
  slider: PRE + "-slider",
  rate: PRE + "-rate",
  upload: "fc-upload",
  cascader: PRE + "-cascader",
  popover: PRE + "-popover",
  tooltip: PRE + "-tooltip",
  colorPicker: PRE + "-colorPicker",
  timePicker: PRE + "-time-picker",
  timeSelect: PRE + "-time-select",
  datePicker: PRE + "-date-picker",
  "switch": PRE + "-switch",
  select: "fc-select",
  checkbox: "fc-checkbox",
  radio: "fc-radio",
  inputNumber: PRE + "-input-number",
  number: PRE + "-input-number",
  input: PRE + "-input",
  formItem: PRE + "-form-item",
  form: PRE + "-form",
  frame: "fc-frame",
  col: PRE + "-col",
  row: PRE + "-row",
  tree: "fc-tree",
  autoComplete: PRE + "-autocomplete",
  auto: PRE + "-autocomplete",
  group: "fc-group",
  array: "fc-group",
  object: "fc-sub-form",
  subForm: "fc-sub-form"
};
function getConfig() {
  return {
    form: {
      inline: false,
      labelPosition: "right",
      labelWidth: "125px",
      disabled: false,
      size: void 0
    },
    row: {
      show: true,
      gutter: 0
    },
    submitBtn: {
      type: "primary",
      loading: false,
      disabled: false,
      innerText: "提交",
      show: true,
      col: void 0,
      click: void 0
    },
    resetBtn: {
      type: "default",
      loading: false,
      disabled: false,
      innerText: "重置",
      show: false,
      col: void 0,
      click: void 0
    }
  };
}
function isTooltip(info) {
  return info.type === "tooltip";
}
function tidy(props, name2) {
  if (!hasProperty(props, name2))
    return;
  if (is.String(props[name2])) {
    var _props$name;
    props[name2] = (_props$name = {}, _defineProperty(_props$name, name2, props[name2]), _defineProperty(_props$name, "show", true), _props$name);
  }
}
function isFalse(val) {
  return val === false;
}
function tidyBool(opt, name2) {
  if (hasProperty(opt, name2) && !is.Object(opt[name2])) {
    opt[name2] = {
      show: !!opt[name2]
    };
  }
}
function tidyRule2(rule) {
  var _rule = _objectSpread2({}, rule);
  delete _rule.children;
  return _rule;
}
var manager = {
  validate: function validate() {
    var form2 = this.form();
    if (form2) {
      return form2.validate();
    } else {
      return new Promise(function(v) {
        return v();
      });
    }
  },
  validateField: function validateField(field) {
    var _this = this;
    return new Promise(function(resolve, reject) {
      var form2 = _this.form();
      if (form2) {
        form2.validateField(field, function(res, err2) {
          err2 ? reject(err2) : resolve(res);
        });
      } else {
        resolve();
      }
    });
  },
  clearValidateState: function clearValidateState(ctx) {
    var fItem = this.vm.refs[ctx.wrapRef];
    if (fItem) {
      fItem.clearValidate();
    }
  },
  tidyOptions: function tidyOptions2(options) {
    ["submitBtn", "resetBtn", "row", "info", "wrap", "col", "title"].forEach(function(name2) {
      tidyBool(options, name2);
    });
    return options;
  },
  tidyRule: function tidyRule3(_ref) {
    var prop = _ref.prop;
    tidy(prop, "title");
    tidy(prop, "info");
    return prop;
  },
  mergeProp: function mergeProp7(ctx) {
    var _this2 = this;
    var def = {
      info: {
        trigger: "hover",
        placement: "top-start",
        icon: true
      },
      title: {},
      col: {
        span: 24
      },
      wrap: {}
    };
    ["info", "wrap", "col", "title"].forEach(function(name2) {
      ctx.prop[name2] = mergeProps2([_this2.options[name2] || {}, ctx.prop[name2] || {}], def[name2]);
    });
  },
  getDefaultOptions: function getDefaultOptions2() {
    return getConfig();
  },
  update: function update4() {
    var form2 = this.options.form;
    this.rule = {
      props: _objectSpread2({}, form2),
      on: {
        submit: function submit(e) {
          e.preventDefault();
        }
      },
      "class": [form2.className, form2["class"], "form-create", this.$handle.preview ? "is-preview" : ""],
      style: form2.style,
      type: "form"
    };
  },
  beforeRender: function beforeRender2() {
    var key = this.key, ref2 = this.ref, $handle = this.$handle;
    extend(this.rule, {
      key,
      ref: ref2
    });
    extend(this.rule.props, {
      model: $handle.formData
    });
  },
  render: function render16(children) {
    var _this3 = this;
    if (children.slotLen() && !this.$handle.preview) {
      children.setSlot(void 0, function() {
        return _this3.makeFormBtn();
      });
    }
    return this.$r(this.rule, isFalse(this.options.row.show) ? children.getSlots() : [this.makeRow(children)]);
  },
  makeWrap: function makeWrap(ctx, children) {
    var _this4 = this;
    var rule = ctx.prop;
    var uni = "".concat(this.key).concat(ctx.key);
    var col = rule.col;
    var isTitle2 = this.isTitle(rule) && rule.wrap.title !== false;
    var labelWidth = !col.labelWidth && !isTitle2 ? 0 : col.labelWidth;
    var _this$rule$props = this.rule.props, inline = _this$rule$props.inline, _col = _this$rule$props.col;
    delete rule.wrap.title;
    var item = isFalse(rule.wrap.show) ? children : this.$r(mergeProps2([rule.wrap, {
      props: _objectSpread2(_objectSpread2({
        labelWidth: labelWidth === void 0 ? labelWidth : toString(labelWidth),
        label: isTitle2 ? rule.title.title : void 0
      }, tidyRule2(rule.wrap || {})), {}, {
        prop: ctx.id,
        rules: ctx.injectValidate()
      }),
      "class": this.$render.mergeClass(rule.className, "fc-form-item"),
      key: "".concat(uni, "fi"),
      ref: ctx.wrapRef,
      type: "formItem"
    }]), _objectSpread2({
      "default": function _default15() {
        return children;
      }
    }, isTitle2 ? {
      label: function label() {
        return _this4.makeInfo(rule, uni, ctx);
      }
    } : {}));
    return inline === true || isFalse(_col) || isFalse(col.show) ? item : this.makeCol(rule, uni, [item]);
  },
  isTitle: function isTitle(rule) {
    if (this.options.form.title === false)
      return false;
    var title = rule.title;
    return !(!title.title && !title["native"] || isFalse(title.show));
  },
  makeInfo: function makeInfo(rule, uni, ctx) {
    var _this5 = this;
    var titleProp = _objectSpread2({}, rule.title);
    var infoProp = _objectSpread2({}, rule.info);
    var isTip = isTooltip(infoProp);
    var form2 = this.options.form;
    var titleSlot = this.getSlot("title");
    var children = [titleSlot ? titleSlot({
      title: ctx.refRule.__$title.value,
      rule: ctx.rule,
      options: this.options
    }) : ctx.refRule.__$title.value + (form2.labelSuffix || form2["label-suffix"] || "")];
    if (!isFalse(infoProp.show) && (infoProp.info || infoProp["native"]) && !isFalse(infoProp.icon)) {
      var prop = {
        type: infoProp.type || "popover",
        props: tidyRule2(infoProp),
        key: "".concat(uni, "pop")
      };
      delete prop.props.icon;
      delete prop.props.show;
      delete prop.props.info;
      delete prop.props.align;
      delete prop.props["native"];
      var field = "content";
      if (infoProp.info && !hasProperty(prop.props, field)) {
        prop.props[field] = ctx.refRule.__$info.value;
      }
      children[infoProp.align !== "left" ? "unshift" : "push"](this.$r(mergeProps2([infoProp, prop]), _defineProperty({}, titleProp.slot || (isTip ? "default" : "reference"), function() {
        return _this5.$r({
          type: "ElIcon",
          style: "top:2px",
          key: "".concat(uni, "i")
        }, {
          "default": function _default15() {
            return _this5.$r({
              type: infoProp.icon === true ? "icon-warning" : infoProp.icon
            });
          }
        }, true);
      })));
    }
    var _prop = mergeProps2([titleProp, {
      props: tidyRule2(titleProp),
      key: "".concat(uni, "tit"),
      "class": "fc-form-title",
      type: titleProp.type || "span"
    }]);
    delete _prop.props.show;
    delete _prop.props.title;
    delete _prop.props["native"];
    return this.$r(_prop, children);
  },
  makeCol: function makeCol(rule, uni, children) {
    var col = rule.col;
    return this.$r({
      "class": this.$render.mergeClass(col["class"], "fc-form-col"),
      type: "col",
      props: col || {
        span: 24
      },
      key: "".concat(uni, "col")
    }, children);
  },
  makeRow: function makeRow(children) {
    var row2 = this.options.row || {};
    return this.$r({
      type: "row",
      props: row2,
      "class": this.$render.mergeClass(row2["class"], "fc-form-row"),
      key: "".concat(this.key, "row")
    }, children);
  },
  makeFormBtn: function makeFormBtn() {
    var vn = [];
    if (!isFalse(this.options.submitBtn.show)) {
      vn.push(this.makeSubmitBtn());
    }
    if (!isFalse(this.options.resetBtn.show)) {
      vn.push(this.makeResetBtn());
    }
    if (!vn.length) {
      return;
    }
    var item = this.$r({
      type: "formItem",
      "class": "fc-form-item",
      key: "".concat(this.key, "fb")
    }, vn);
    return this.rule.props.inline === true ? item : this.$r({
      type: "col",
      "class": "fc-form-col fc-form-footer",
      props: {
        span: 24
      },
      key: "".concat(this.key, "fc")
    }, [item]);
  },
  makeResetBtn: function makeResetBtn() {
    var _this6 = this;
    var resetBtn = _objectSpread2({}, this.options.resetBtn);
    var innerText = resetBtn.innerText;
    delete resetBtn.innerText;
    delete resetBtn.click;
    delete resetBtn.col;
    delete resetBtn.show;
    return this.$r({
      type: "button",
      props: resetBtn,
      "class": "fc-reset-btn",
      style: {
        width: resetBtn.width
      },
      on: {
        click: function click() {
          var fApi = _this6.$handle.api;
          _this6.options.resetBtn.click ? _this6.options.resetBtn.click(fApi) : fApi.resetFields();
        }
      },
      key: "".concat(this.key, "b2")
    }, [innerText]);
  },
  makeSubmitBtn: function makeSubmitBtn() {
    var _this7 = this;
    var submitBtn = _objectSpread2({}, this.options.submitBtn);
    var innerText = submitBtn.innerText;
    delete submitBtn.innerText;
    delete submitBtn.click;
    delete submitBtn.col;
    delete submitBtn.show;
    return this.$r({
      type: "button",
      props: submitBtn,
      "class": "fc-submit-btn",
      style: {
        width: submitBtn.width
      },
      on: {
        click: function click() {
          var fApi = _this7.$handle.api;
          _this7.options.submitBtn.click ? _this7.options.submitBtn.click(fApi) : fApi.submit()["catch"](function() {
          });
        }
      },
      key: "".concat(this.key, "b1")
    }, [innerText]);
  }
};
var maker$1 = {};
useAlias(maker$1);
useSelect(maker$1);
useTree(maker$1);
useUpload(maker$1);
useFrame(maker$1);
function useAlias(maker2) {
  ["group", "tree", "switch", "upload", "autoComplete", "checkbox", "cascader", "colorPicker", "datePicker", "frame", "inputNumber", "radio", "rate"].forEach(function(name2) {
    maker2[name2] = creatorFactory(name2);
  });
  maker2.auto = maker2.autoComplete;
  maker2.number = maker2.inputNumber;
  maker2.color = maker2.colorPicker;
}
function useSelect(maker2) {
  var select2 = "select";
  var multiple = "multiple";
  maker2["selectMultiple"] = creatorFactory(select2, _defineProperty({}, multiple, true));
  maker2["selectOne"] = creatorFactory(select2, _defineProperty({}, multiple, false));
}
function useTree(maker2) {
  var name2 = "tree";
  var types = {
    "treeSelected": "selected",
    "treeChecked": "checked"
  };
  Object.keys(types).reduce(function(m, key) {
    m[key] = creatorFactory(name2, {
      type: types[key]
    });
    return m;
  }, maker2);
}
function useUpload(maker2) {
  var name2 = "upload";
  var types = {
    image: ["image", 0],
    file: ["file", 0],
    uploadFileOne: ["file", 1],
    uploadImageOne: ["image", 1]
  };
  Object.keys(types).reduce(function(m, key) {
    m[key] = creatorFactory(name2, function(m2) {
      return m2.props({
        uploadType: types[key][0],
        maxLength: types[key][1]
      });
    });
    return m;
  }, maker2);
  maker2.uploadImage = maker2.image;
  maker2.uploadFile = maker2.file;
}
function useFrame(maker2) {
  var types = {
    frameInputs: ["input", 0],
    frameFiles: ["file", 0],
    frameImages: ["image", 0],
    frameInputOne: ["input", 1],
    frameFileOne: ["file", 1],
    frameImageOne: ["image", 1]
  };
  Object.keys(types).reduce(function(maker3, key) {
    maker3[key] = creatorFactory("frame", function(m) {
      return m.props({
        type: types[key][0],
        maxLength: types[key][1]
      });
    });
    return maker3;
  }, maker2);
  maker2.frameInput = maker2.frameInputs;
  maker2.frameFile = maker2.frameFiles;
  maker2.frameImage = maker2.frameImages;
  return maker2;
}
var css_248z = ".form-create .form-create .el-form-item{margin-bottom:22px}.form-create{width:100%}.form-create .fc-none,.form-create.is-preview .el-form-item.is-required>.el-form-item__label-wrap>.el-form-item__label:before,.form-create.is-preview .el-form-item.is-required>.el-form-item__label:before,.form-create.is-preview .fc-clock{display:none!important}.fc-wrap-right .el-form-item__label{justify-content:flex-end}.fc-wrap-left .el-form-item__label{justify-content:flex-start}.fc-wrap-top.el-form-item{display:block}.fc-wrap-top.el-form-item .el-form-item__label{display:block;height:auto;line-height:22px;margin-bottom:8px;text-align:left}.el-form--large .fc-wrap-top.el-form-item .el-form-item__label{line-height:22px;margin-bottom:12px}.el-form--default .fc-wrap-top.el-form-item .el-form-item__label{line-height:22px;margin-bottom:8px}.el-form--small .fc-wrap-top.el-form-item .el-form-item__label{line-height:20px;margin-bottom:4px}.fc-form-footer{margin-top:12px}";
styleInject(css_248z);
function tidyBtnProp(btn, def) {
  if (is.Boolean(btn))
    btn = {
      show: btn
    };
  else if (!is.Undef(btn) && !is.Object(btn))
    btn = {
      show: def
    };
  return btn;
}
function extendApi(api, h2) {
  return {
    formEl: function formEl() {
      return h2.$manager.form();
    },
    wrapEl: function wrapEl(id2) {
      var ctx = h2.getFieldCtx(id2);
      if (!ctx)
        return;
      return h2.vm.refs[ctx.wrapRef];
    },
    validate: function validate2(callback) {
      return new Promise(function(resolve, reject) {
        var forms = api.children;
        var all = [h2.$manager.validate()];
        forms.forEach(function(v) {
          all.push(v.validate());
        });
        Promise.all(all).then(function() {
          resolve(true);
          callback && callback(true);
        })["catch"](function(e) {
          reject(e);
          callback && callback(e);
          h2.vm.emit("validate-fail", e, {
            api
          });
        });
      });
    },
    validateField: function validateField2(field, callback) {
      return new Promise(function(resolve, reject) {
        var ctx = h2.getFieldCtx(field);
        if (!ctx)
          return;
        var sub = h2.subForm[ctx.id];
        var all = [h2.$manager.validateField(ctx.id)];
        toArray(sub).forEach(function(v) {
          all.push(v.validate());
        });
        Promise.all(all).then(function() {
          resolve(null);
          callback && callback(null);
        })["catch"](function(e) {
          reject(e);
          callback && callback(e);
          h2.vm.emit("validate-field-fail", e, {
            field,
            api
          });
        });
      });
    },
    clearValidateState: function clearValidateState2(fields) {
      var _this = this;
      var clearSub = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
      api.helper.tidyFields(fields).forEach(function(field) {
        if (clearSub)
          _this.clearSubValidateState(field);
        h2.getCtxs(field).forEach(function(ctx) {
          h2.$manager.clearValidateState(ctx);
        });
      });
    },
    clearSubValidateState: function clearSubValidateState(fields) {
      api.helper.tidyFields(fields).forEach(function(field) {
        h2.getCtxs(field).forEach(function(ctx) {
          var subForm = h2.subForm[ctx.id];
          if (!subForm)
            return;
          if (Array.isArray(subForm)) {
            subForm.forEach(function(form2) {
              form2.clearValidateState();
            });
          } else if (subForm) {
            subForm.clearValidateState();
          }
        });
      });
    },
    btn: {
      loading: function loading() {
        var _loading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
        api.submitBtnProps({
          loading: !!_loading
        });
      },
      disabled: function disabled() {
        var _disabled = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
        api.submitBtnProps({
          disabled: !!_disabled
        });
      },
      show: function show() {
        var isShow = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
        api.submitBtnProps({
          show: !!isShow
        });
      }
    },
    resetBtn: {
      loading: function loading() {
        var _loading2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
        api.resetBtnProps({
          loading: !!_loading2
        });
      },
      disabled: function disabled() {
        var _disabled2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
        api.resetBtnProps({
          disabled: !!_disabled2
        });
      },
      show: function show() {
        var isShow = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
        api.resetBtnProps({
          show: !!isShow
        });
      }
    },
    submitBtnProps: function submitBtnProps() {
      var props = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
      var btn = tidyBtnProp(h2.options.submitBtn, true);
      extend(btn, props);
      h2.options.submitBtn = btn;
      api.refreshOptions();
    },
    resetBtnProps: function resetBtnProps() {
      var props = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
      var btn = tidyBtnProp(h2.options.resetBtn, false);
      extend(btn, props);
      h2.options.resetBtn = btn;
      api.refreshOptions();
    },
    submit: function submit(successFn, failFn) {
      return new Promise(function(resolve, reject) {
        api.validate().then(function() {
          var formData3 = api.formData();
          is.Function(successFn) && invoke(function() {
            return successFn(formData3, api);
          });
          is.Function(h2.options.onSubmit) && invoke(function() {
            return h2.options.onSubmit(formData3, api);
          });
          h2.vm.emit("submit", formData3, api);
          resolve(formData3);
        })["catch"](function() {
          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          is.Function(failFn) && invoke(function() {
            return failFn.apply(void 0, [api].concat(args));
          });
          reject.apply(void 0, args);
        });
      });
    }
  };
}
var required = {
  name: "required",
  load: function load2(inject2, rule, api) {
    var val = parseVal(inject2.getValue());
    if (val.required === false) {
      inject2.clearProp();
      api.clearValidateState([rule.field]);
    } else {
      var validate2 = _objectSpread2({
        required: true,
        validator: function validator(_7, v, call) {
          is.empty(v) ? call(validate2.message) : call();
        }
      }, val);
      if (!validate2.message) {
        validate2.message = rule.__fc__.refRule.__$title.value + (api.getLocale() === "en" ? " is required" : "不能为空");
      } else {
        var match = validate2.message.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);
        if (match) {
          validate2.message = api.t(match[1], {
            title: rule.__fc__.refRule.__$title.value
          });
        }
      }
      inject2.getProp().validate = [validate2];
    }
    api.sync(rule);
  },
  watch: function watch3() {
    required.load.apply(required, arguments);
  }
};
function parseVal(val) {
  if (is.Boolean(val)) {
    return {
      required: val
    };
  } else if (is.String(val)) {
    return {
      message: val
    };
  } else if (is.Undef(val)) {
    return {
      required: false
    };
  } else if (is.Function(val)) {
    return {
      validator: val
    };
  } else if (!is.Object(val)) {
    return {};
  } else {
    return val;
  }
}
function install(FormCreate2) {
  FormCreate2.componentAlias(alias);
  components.forEach(function(component) {
    FormCreate2.component(component.name, component);
  });
  FormCreate2.register(required);
  parsers.forEach(function(parser) {
    FormCreate2.parser(parser);
  });
  Object.keys(maker$1).forEach(function(name2) {
    FormCreate2.maker[name2] = maker$1[name2];
  });
  if (typeof window !== "undefined" && window.ElementPlus) {
    FormCreate2.useApp(function(_7, app) {
      app.use(window.ElementPlus);
    });
  }
}
function elmFormCreate() {
  return FormCreateFactory({
    ui: "element-ui",
    version: "3.2.14",
    manager,
    extendApi,
    install,
    attrs: {
      normal: ["col", "wrap"],
      array: ["className"],
      key: ["title", "info"]
    }
  });
}
var FormCreate = elmFormCreate();
if (typeof window !== "undefined") {
  window.formCreate = FormCreate;
}
var maker = FormCreate.maker;

export {
  FormCreate,
  maker
};
/*! Bundled license information:

@form-create/element-ui/dist/form-create.esm.js:
  (*!
   * @form-create/element-ui v3.2.14
   * (c) 2018-2024 xaboy
   * Github https://github.com/xaboy/form-create
   * Released under the MIT License.
   *)
*/
//# sourceMappingURL=chunk-FKOWI4VU.js.map
