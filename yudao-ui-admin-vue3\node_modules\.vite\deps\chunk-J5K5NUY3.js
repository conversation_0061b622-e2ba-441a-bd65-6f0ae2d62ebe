import {
  fromPairs_default
} from "./chunk-CIETM3MP.js";
import {
  hasOwn,
  init_shared_esm_bundler,
  isObject,
  isString,
  warn
} from "./chunk-GTWINWNV.js";

// node_modules/.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/node_modules/element-plus/es/utils/vue/props/runtime.mjs
init_shared_esm_bundler();
var epPropKey = "__epPropKey";
var definePropType = (val) => val;
var isEpProp = (val) => isObject(val) && !!val[epPropKey];
var buildProp = (prop, key) => {
  if (!isObject(prop) || isEpProp(prop))
    return prop;
  const { values, required, default: defaultValue, type, validator } = prop;
  const _validator = values || validator ? (val) => {
    let valid = false;
    let allowedValues = [];
    if (values) {
      allowedValues = Array.from(values);
      if (hasOwn(prop, "default")) {
        allowedValues.push(defaultValue);
      }
      valid || (valid = allowedValues.includes(val));
    }
    if (validator)
      valid || (valid = validator(val));
    if (!valid && allowedValues.length > 0) {
      const allowValuesText = [...new Set(allowedValues)].map((value) => JSON.stringify(value)).join(", ");
      warn(`Invalid prop: validation failed${key ? ` for prop "${key}"` : ""}. Expected one of [${allowValuesText}], got value ${JSON.stringify(val)}.`);
    }
    return valid;
  } : void 0;
  const epProp = {
    type,
    required: !!required,
    validator: _validator,
    [epPropKey]: true
  };
  if (hasOwn(prop, "default"))
    epProp.default = defaultValue;
  return epProp;
};
var buildProps = (props) => fromPairs_default(Object.entries(props).map(([key, option]) => [
  key,
  buildProp(option, key)
]));

// node_modules/.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/node_modules/element-plus/es/utils/error.mjs
init_shared_esm_bundler();
var ElementPlusError = class extends Error {
  constructor(m) {
    super(m);
    this.name = "ElementPlusError";
  }
};
function throwError(scope, m) {
  throw new ElementPlusError(`[${scope}] ${m}`);
}
function debugWarn(scope, message) {
  if (true) {
    const error = isString(scope) ? new ElementPlusError(`[${scope}] ${message}`) : scope;
    console.warn(error);
  }
}

export {
  throwError,
  debugWarn,
  definePropType,
  buildProp,
  buildProps
};
//# sourceMappingURL=chunk-J5K5NUY3.js.map
