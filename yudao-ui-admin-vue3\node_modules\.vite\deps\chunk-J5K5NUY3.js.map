{"version": 3, "sources": ["../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/utils/vue/props/runtime.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/utils/error.ts"], "sourcesContent": ["import { warn } from 'vue'\nimport { fromPairs } from 'lodash-unified'\nimport { isObject } from '../../types'\nimport { hasOwn } from '../../objects'\n\nimport type { PropType } from 'vue'\nimport type {\n  EpProp,\n  EpPropConvert,\n  EpPropFinalized,\n  EpPropInput,\n  EpPropMergeType,\n  IfEpProp,\n  IfNativePropType,\n  NativePropType,\n} from './types'\n\nexport const epPropKey = '__epPropKey'\n\nexport const definePropType = <T>(val: any): PropType<T> => val\n\nexport const isEpProp = (val: unknown): val is EpProp<any, any, any> =>\n  isObject(val) && !!(val as any)[epPropKey]\n\n/**\n * @description Build prop. It can better optimize prop types\n * @description 生成 prop，能更好地优化类型\n * @example\n  // limited options\n  // the type will be PropType<'light' | 'dark'>\n  buildProp({\n    type: String,\n    values: ['light', 'dark'],\n  } as const)\n  * @example\n  // limited options and other types\n  // the type will be PropType<'small' | 'large' | number>\n  buildProp({\n    type: [String, Number],\n    values: ['small', 'large'],\n    validator: (val: unknown): val is number => typeof val === 'number',\n  } as const)\n  @link see more: https://github.com/element-plus/element-plus/pull/3341\n */\nexport const buildProp = <\n  Type = never,\n  Value = never,\n  Validator = never,\n  Default extends EpPropMergeType<Type, Value, Validator> = never,\n  Required extends boolean = false\n>(\n  prop: EpPropInput<Type, Value, Validator, Default, Required>,\n  key?: string\n): EpPropFinalized<Type, Value, Validator, Default, Required> => {\n  // filter native prop type and nested prop, e.g `null`, `undefined` (from `buildProps`)\n  if (!isObject(prop) || isEpProp(prop)) return prop as any\n\n  const { values, required, default: defaultValue, type, validator } = prop\n\n  const _validator =\n    values || validator\n      ? (val: unknown) => {\n          let valid = false\n          let allowedValues: unknown[] = []\n\n          if (values) {\n            allowedValues = Array.from(values)\n            if (hasOwn(prop, 'default')) {\n              allowedValues.push(defaultValue)\n            }\n            valid ||= allowedValues.includes(val)\n          }\n          if (validator) valid ||= validator(val)\n\n          if (!valid && allowedValues.length > 0) {\n            const allowValuesText = [...new Set(allowedValues)]\n              .map((value) => JSON.stringify(value))\n              .join(', ')\n            warn(\n              `Invalid prop: validation failed${\n                key ? ` for prop \"${key}\"` : ''\n              }. Expected one of [${allowValuesText}], got value ${JSON.stringify(\n                val\n              )}.`\n            )\n          }\n          return valid\n        }\n      : undefined\n\n  const epProp: any = {\n    type,\n    required: !!required,\n    validator: _validator,\n    [epPropKey]: true,\n  }\n  if (hasOwn(prop, 'default')) epProp.default = defaultValue\n  return epProp\n}\n\nexport const buildProps = <\n  Props extends Record<\n    string,\n    | { [epPropKey]: true }\n    | NativePropType\n    | EpPropInput<any, any, any, any, any>\n  >\n>(\n  props: Props\n): {\n  [K in keyof Props]: IfEpProp<\n    Props[K],\n    Props[K],\n    IfNativePropType<Props[K], Props[K], EpPropConvert<Props[K]>>\n  >\n} =>\n  fromPairs(\n    Object.entries(props).map(([key, option]) => [\n      key,\n      buildProp(option as any, key),\n    ])\n  ) as any\n", "import { isString } from './types'\n\nclass ElementPlusError extends Error {\n  constructor(m: string) {\n    super(m)\n    this.name = 'ElementPlusError'\n  }\n}\n\nexport function throwError(scope: string, m: string): never {\n  throw new ElementPlusError(`[${scope}] ${m}`)\n}\n\nexport function debugWarn(err: Error): void\nexport function debugWarn(scope: string, message: string): void\nexport function debugWarn(scope: string | Error, message?: string): void {\n  if (process.env.NODE_ENV !== 'production') {\n    const error: Error = isString(scope)\n      ? new ElementPlusError(`[${scope}] ${message}`)\n      : scope\n    // eslint-disable-next-line no-console\n    console.warn(error)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAIY,IAAC,YAAY;AACb,IAAC,iBAAiB,CAAC,QAAQ;AAC3B,IAAC,WAAW,CAAC,QAAQ,SAAS,GAAG,KAAK,CAAC,CAAC,IAAI,SAAS;AACrD,IAAC,YAAY,CAAC,MAAM,QAAQ;AACtC,MAAI,CAAC,SAAS,IAAI,KAAK,SAAS,IAAI;AAClC,WAAO;AACT,QAAM,EAAE,QAAQ,UAAU,SAAS,cAAc,MAAM,UAAS,IAAK;AACrE,QAAM,aAAa,UAAU,YAAY,CAAC,QAAQ;AAChD,QAAI,QAAQ;AACZ,QAAI,gBAAgB,CAAA;AACpB,QAAI,QAAQ;AACV,sBAAgB,MAAM,KAAK,MAAM;AACjC,UAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,sBAAc,KAAK,YAAY;MACvC;AACM,gBAAU,QAAQ,cAAc,SAAS,GAAG;IAClD;AACI,QAAI;AACF,gBAAU,QAAQ,UAAU,GAAG;AACjC,QAAI,CAAC,SAAS,cAAc,SAAS,GAAG;AACtC,YAAM,kBAAkB,CAAC,GAAG,IAAI,IAAI,aAAa,CAAC,EAAE,IAAI,CAAC,UAAU,KAAK,UAAU,KAAK,CAAC,EAAE,KAAK,IAAI;AACnG,WAAK,kCAAkC,MAAM,cAAc,GAAG,MAAM,EAAE,sBAAsB,eAAe,gBAAgB,KAAK,UAAU,GAAG,CAAC,GAAG;IACvJ;AACI,WAAO;EACX,IAAM;AACJ,QAAM,SAAS;IACb;IACA,UAAU,CAAC,CAAC;IACZ,WAAW;IACX,CAAC,SAAS,GAAG;EACjB;AACE,MAAI,OAAO,MAAM,SAAS;AACxB,WAAO,UAAU;AACnB,SAAO;AACT;AACY,IAAC,aAAa,CAAC,UAAU,kBAAU,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,MAAM,MAAM;EAC1F;EACA,UAAU,QAAQ,GAAG;AACvB,CAAC,CAAC;;;;ACzCF,IAAM,mBAAN,cAA+B,MAAM;EACnC,YAAY,GAAG;AACb,UAAM,CAAC;AACP,SAAK,OAAO;EAChB;AACA;AACO,SAAS,WAAW,OAAO,GAAG;AACnC,QAAM,IAAI,iBAAiB,IAAI,KAAK,KAAK,CAAC,EAAE;AAC9C;AACO,SAAS,UAAU,OAAO,SAAS;AACxC,MAAI,MAAuC;AACzC,UAAM,QAAQ,SAAS,KAAK,IAAI,IAAI,iBAAiB,IAAI,KAAK,KAAK,OAAO,EAAE,IAAI;AAChF,YAAQ,KAAK,KAAK;EACtB;AACA;", "names": []}