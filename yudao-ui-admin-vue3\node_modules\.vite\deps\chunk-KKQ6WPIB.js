import {
  isHorizontal
} from "./chunk-7C6J56BH.js";
import {
  getBusinessObject,
  is,
  isAny
} from "./chunk-FNF472WR.js";
import {
  isString
} from "./chunk-YTJ5ESGD.js";

// node_modules/.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/features/modeling/util/ModelingUtil.js
function getParent(element, anyType) {
  if (isString(anyType)) {
    anyType = [anyType];
  }
  while (element = element.parent) {
    if (isAny(element, anyType)) {
      return element;
    }
  }
  return null;
}
function isDirectionHorizontal(element, elementRegistry) {
  var parent = getParent(element, "bpmn:Process");
  if (parent) {
    return true;
  }
  var types = ["bpmn:Participant", "bpmn:Lane"];
  parent = getParent(element, types);
  if (parent) {
    return isHorizontal(parent);
  } else if (isAny(element, types)) {
    return isHorizontal(element);
  }
  var process;
  for (process = getBusinessObject(element); process; process = process.$parent) {
    if (is(process, "bpmn:Process")) {
      break;
    }
  }
  if (!elementRegistry) {
    return true;
  }
  var pool = elementRegistry.find(function(shape) {
    var businessObject = getBusinessObject(shape);
    return businessObject && businessObject.get("processRef") === process;
  });
  if (!pool) {
    return true;
  }
  return isHorizontal(pool);
}

export {
  getParent,
  isDirectionHorizontal
};
//# sourceMappingURL=chunk-KKQ6WPIB.js.map
