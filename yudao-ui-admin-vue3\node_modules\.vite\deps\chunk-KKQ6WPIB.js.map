{"version": 3, "sources": ["../../.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/features/modeling/util/ModelingUtil.js"], "sourcesContent": ["import { isString } from 'min-dash';\n\nexport { is, isAny } from '../../../util/ModelUtil';\n\nimport {\n  is,\n  isAny,\n  getBusinessObject\n} from '../../../util/ModelUtil';\n\nimport { isHorizontal } from '../../../util/DiUtil';\n\n/**\n * @typedef {import('diagram-js/lib/core/ElementRegistry').default} ElementRegistry\n * @typedef {import('../../../model/Types').Element} Element\n */\n\n/**\n * Return the parent of the element with any of the given types.\n *\n * @param {Element} element\n * @param {string|string[]} anyType\n *\n * @return {Element|null}\n */\nexport function getParent(element, anyType) {\n\n  if (isString(anyType)) {\n    anyType = [ anyType ];\n  }\n\n  while ((element = element.parent)) {\n    if (isAny(element, anyType)) {\n      return element;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Determines if the local modeling direction is vertical or horizontal.\n *\n * @param {Element} element\n * @param {ElementRegistry} [elementRegistry] - provide to consider parent diagram direction\n *\n * @return {boolean} false for vertical pools, lanes and their children. true otherwise\n */\nexport function isDirectionHorizontal(element, elementRegistry) {\n\n  var parent = getParent(element, 'bpmn:Process');\n  if (parent) {\n    return true;\n  }\n\n  var types = [ 'bpmn:Participant', 'bpmn:Lane' ];\n\n  parent = getParent(element, types);\n  if (parent) {\n    return isHorizontal(parent);\n  } else if (isAny(element, types)) {\n    return isHorizontal(element);\n  }\n\n  var process;\n  for (process = getBusinessObject(element); process; process = process.$parent) {\n    if (is(process, 'bpmn:Process')) {\n      break;\n    }\n  }\n\n  if (!elementRegistry) {\n    return true;\n  }\n\n  // The direction may be specified in another diagram. We ignore that there\n  // could be multiple diagrams with contradicting properties based on the\n  // assumption that such BPMN files are unusual.\n  var pool = elementRegistry.find(function(shape) {\n    var businessObject = getBusinessObject(shape);\n    return businessObject && businessObject.get('processRef') === process;\n  });\n\n  if (!pool) {\n    return true;\n  }\n\n  return isHorizontal(pool);\n}"], "mappings": ";;;;;;;;;;;;;AAyBO,SAAS,UAAU,SAAS,SAAS;AAE1C,MAAI,SAAS,OAAO,GAAG;AACrB,cAAU,CAAE,OAAQ;AAAA,EACtB;AAEA,SAAQ,UAAU,QAAQ,QAAS;AACjC,QAAI,MAAM,SAAS,OAAO,GAAG;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAUO,SAAS,sBAAsB,SAAS,iBAAiB;AAE9D,MAAI,SAAS,UAAU,SAAS,cAAc;AAC9C,MAAI,QAAQ;AACV,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,CAAE,oBAAoB,WAAY;AAE9C,WAAS,UAAU,SAAS,KAAK;AACjC,MAAI,QAAQ;AACV,WAAO,aAAa,MAAM;AAAA,EAC5B,WAAW,MAAM,SAAS,KAAK,GAAG;AAChC,WAAO,aAAa,OAAO;AAAA,EAC7B;AAEA,MAAI;AACJ,OAAK,UAAU,kBAAkB,OAAO,GAAG,SAAS,UAAU,QAAQ,SAAS;AAC7E,QAAI,GAAG,SAAS,cAAc,GAAG;AAC/B;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AAKA,MAAI,OAAO,gBAAgB,KAAK,SAAS,OAAO;AAC9C,QAAI,iBAAiB,kBAAkB,KAAK;AAC5C,WAAO,kBAAkB,eAAe,IAAI,YAAY,MAAM;AAAA,EAChE,CAAC;AAED,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,SAAO,aAAa,IAAI;AAC1B;", "names": []}