import {
  getDi,
  is
} from "./chunk-FNF472WR.js";

// node_modules/.pnpm/inherits-browser@0.1.0/node_modules/inherits-browser/dist/index.es.js
function e(e2, t) {
  t && (e2.super_ = t, e2.prototype = Object.create(t.prototype, { constructor: { value: e2, enumerable: false, writable: true, configurable: true } }));
}

// node_modules/.pnpm/ids@1.0.5/node_modules/ids/dist/index.esm.js
function createCommonjsModule(fn, module) {
  return module = { exports: {} }, fn(module, module.exports), module.exports;
}
var hat_1 = createCommonjsModule(function(module) {
  var hat = module.exports = function(bits, base) {
    if (!base)
      base = 16;
    if (bits === void 0)
      bits = 128;
    if (bits <= 0)
      return "0";
    var digits = Math.log(Math.pow(2, bits)) / Math.log(base);
    for (var i = 2; digits === Infinity; i *= 2) {
      digits = Math.log(Math.pow(2, bits / i)) / Math.log(base) * i;
    }
    var rem = digits - Math.floor(digits);
    var res = "";
    for (var i = 0; i < Math.floor(digits); i++) {
      var x = Math.floor(Math.random() * base).toString(base);
      res = x + res;
    }
    if (rem) {
      var b = Math.pow(base, rem);
      var x = Math.floor(Math.random() * b).toString(base);
      res = x + res;
    }
    var parsed = parseInt(res, base);
    if (parsed !== Infinity && parsed >= Math.pow(2, bits)) {
      return hat(bits, base);
    } else
      return res;
  };
  hat.rack = function(bits, base, expandBy) {
    var fn = function(data) {
      var iters = 0;
      do {
        if (iters++ > 10) {
          if (expandBy)
            bits += expandBy;
          else
            throw new Error("too many ID collisions, use more bits");
        }
        var id = hat(bits, base);
      } while (Object.hasOwnProperty.call(hats, id));
      hats[id] = data;
      return id;
    };
    var hats = fn.hats = {};
    fn.get = function(id) {
      return fn.hats[id];
    };
    fn.set = function(id, value) {
      fn.hats[id] = value;
      return fn;
    };
    fn.bits = bits || 128;
    fn.base = base || 16;
    return fn;
  };
});
function Ids(seed) {
  if (!(this instanceof Ids)) {
    return new Ids(seed);
  }
  seed = seed || [128, 36, 1];
  this._seed = seed.length ? hat_1.rack(seed[0], seed[1], seed[2]) : seed;
}
Ids.prototype.next = function(element) {
  return this._seed(element || true);
};
Ids.prototype.nextPrefixed = function(prefix, element) {
  var id;
  do {
    id = prefix + this.next(true);
  } while (this.assigned(id));
  this.claim(id, element);
  return id;
};
Ids.prototype.claim = function(id, element) {
  this._seed.set(id, element || true);
};
Ids.prototype.assigned = function(id) {
  return this._seed.get(id) || false;
};
Ids.prototype.unclaim = function(id) {
  delete this._seed.hats[id];
};
Ids.prototype.clear = function() {
  var hats = this._seed.hats, id;
  for (id in hats) {
    this.unclaim(id);
  }
};
var index_esm_default = Ids;

// node_modules/.pnpm/tiny-svg@3.1.3/node_modules/tiny-svg/dist/index.esm.js
function ensureImported(element, target) {
  if (element.ownerDocument !== target.ownerDocument) {
    try {
      return target.ownerDocument.importNode(element, true);
    } catch (e2) {
    }
  }
  return element;
}
function appendTo(element, target) {
  return target.appendChild(ensureImported(element, target));
}
function append(target, node2) {
  appendTo(node2, target);
  return target;
}
var LENGTH_ATTR = 2;
var CSS_PROPERTIES = {
  "alignment-baseline": 1,
  "baseline-shift": 1,
  "clip": 1,
  "clip-path": 1,
  "clip-rule": 1,
  "color": 1,
  "color-interpolation": 1,
  "color-interpolation-filters": 1,
  "color-profile": 1,
  "color-rendering": 1,
  "cursor": 1,
  "direction": 1,
  "display": 1,
  "dominant-baseline": 1,
  "enable-background": 1,
  "fill": 1,
  "fill-opacity": 1,
  "fill-rule": 1,
  "filter": 1,
  "flood-color": 1,
  "flood-opacity": 1,
  "font": 1,
  "font-family": 1,
  "font-size": LENGTH_ATTR,
  "font-size-adjust": 1,
  "font-stretch": 1,
  "font-style": 1,
  "font-variant": 1,
  "font-weight": 1,
  "glyph-orientation-horizontal": 1,
  "glyph-orientation-vertical": 1,
  "image-rendering": 1,
  "kerning": 1,
  "letter-spacing": 1,
  "lighting-color": 1,
  "marker": 1,
  "marker-end": 1,
  "marker-mid": 1,
  "marker-start": 1,
  "mask": 1,
  "opacity": 1,
  "overflow": 1,
  "pointer-events": 1,
  "shape-rendering": 1,
  "stop-color": 1,
  "stop-opacity": 1,
  "stroke": 1,
  "stroke-dasharray": 1,
  "stroke-dashoffset": 1,
  "stroke-linecap": 1,
  "stroke-linejoin": 1,
  "stroke-miterlimit": 1,
  "stroke-opacity": 1,
  "stroke-width": LENGTH_ATTR,
  "text-anchor": 1,
  "text-decoration": 1,
  "text-rendering": 1,
  "unicode-bidi": 1,
  "visibility": 1,
  "word-spacing": 1,
  "writing-mode": 1
};
function getAttribute(node2, name) {
  if (CSS_PROPERTIES[name]) {
    return node2.style[name];
  } else {
    return node2.getAttributeNS(null, name);
  }
}
function setAttribute(node2, name, value) {
  var hyphenated = name.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase();
  var type = CSS_PROPERTIES[hyphenated];
  if (type) {
    if (type === LENGTH_ATTR && typeof value === "number") {
      value = String(value) + "px";
    }
    node2.style[hyphenated] = value;
  } else {
    node2.setAttributeNS(null, name, value);
  }
}
function setAttributes(node2, attrs) {
  var names = Object.keys(attrs), i, name;
  for (i = 0, name; name = names[i]; i++) {
    setAttribute(node2, name, attrs[name]);
  }
}
function attr(node2, name, value) {
  if (typeof name === "string") {
    if (value !== void 0) {
      setAttribute(node2, name, value);
    } else {
      return getAttribute(node2, name);
    }
  } else {
    setAttributes(node2, name);
  }
  return node2;
}
var toString = Object.prototype.toString;
function classes(el) {
  return new ClassList(el);
}
function ClassList(el) {
  if (!el || !el.nodeType) {
    throw new Error("A DOM element reference is required");
  }
  this.el = el;
  this.list = el.classList;
}
ClassList.prototype.add = function(name) {
  this.list.add(name);
  return this;
};
ClassList.prototype.remove = function(name) {
  if ("[object RegExp]" == toString.call(name)) {
    return this.removeMatching(name);
  }
  this.list.remove(name);
  return this;
};
ClassList.prototype.removeMatching = function(re) {
  const arr = this.array();
  for (let i = 0; i < arr.length; i++) {
    if (re.test(arr[i])) {
      this.remove(arr[i]);
    }
  }
  return this;
};
ClassList.prototype.toggle = function(name, force) {
  if ("undefined" !== typeof force) {
    if (force !== this.list.toggle(name, force)) {
      this.list.toggle(name);
    }
  } else {
    this.list.toggle(name);
  }
  return this;
};
ClassList.prototype.array = function() {
  return Array.from(this.list);
};
ClassList.prototype.has = ClassList.prototype.contains = function(name) {
  return this.list.contains(name);
};
function clear(element) {
  var child;
  while (child = element.firstChild) {
    element.removeChild(child);
  }
  return element;
}
function clone(element) {
  return element.cloneNode(true);
}
var ns = {
  svg: "http://www.w3.org/2000/svg"
};
var SVG_START = '<svg xmlns="' + ns.svg + '"';
function parse(svg) {
  var unwrap = false;
  if (svg.substring(0, 4) === "<svg") {
    if (svg.indexOf(ns.svg) === -1) {
      svg = SVG_START + svg.substring(4);
    }
  } else {
    svg = SVG_START + ">" + svg + "</svg>";
    unwrap = true;
  }
  var parsed = parseDocument(svg);
  if (!unwrap) {
    return parsed;
  }
  var fragment = document.createDocumentFragment();
  var parent = parsed.firstChild;
  while (parent.firstChild) {
    fragment.appendChild(parent.firstChild);
  }
  return fragment;
}
function parseDocument(svg) {
  var parser;
  parser = new DOMParser();
  parser.async = false;
  return parser.parseFromString(svg, "text/xml");
}
function create(name, attrs) {
  var element;
  name = name.trim();
  if (name.charAt(0) === "<") {
    element = parse(name).firstChild;
    element = document.importNode(element, true);
  } else {
    element = document.createElementNS(ns.svg, name);
  }
  if (attrs) {
    attr(element, attrs);
  }
  return element;
}
var node = null;
function getNode() {
  if (node === null) {
    node = create("svg");
  }
  return node;
}
function extend(object, props) {
  var i, k, keys = Object.keys(props);
  for (i = 0; k = keys[i]; i++) {
    object[k] = props[k];
  }
  return object;
}
function createMatrix(a, b, c, d, e2, f) {
  var matrix = getNode().createSVGMatrix();
  switch (arguments.length) {
    case 0:
      return matrix;
    case 1:
      return extend(matrix, a);
    case 6:
      return extend(matrix, {
        a,
        b,
        c,
        d,
        e: e2,
        f
      });
  }
}
function createTransform(matrix) {
  if (matrix) {
    return getNode().createSVGTransformFromMatrix(matrix);
  } else {
    return getNode().createSVGTransform();
  }
}
var TEXT_ENTITIES = /([&<>]{1})/g;
var ATTR_ENTITIES = /([&<>\n\r"]{1})/g;
var ENTITY_REPLACEMENT = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "'"
};
function escape(str, pattern) {
  function replaceFn(match, entity) {
    return ENTITY_REPLACEMENT[entity] || entity;
  }
  return str.replace(pattern, replaceFn);
}
function serialize(node2, output) {
  var i, len, attrMap, attrNode, childNodes;
  switch (node2.nodeType) {
    case 3:
      output.push(escape(node2.textContent, TEXT_ENTITIES));
      break;
    case 1:
      output.push("<", node2.tagName);
      if (node2.hasAttributes()) {
        attrMap = node2.attributes;
        for (i = 0, len = attrMap.length; i < len; ++i) {
          attrNode = attrMap.item(i);
          output.push(" ", attrNode.name, '="', escape(attrNode.value, ATTR_ENTITIES), '"');
        }
      }
      if (node2.hasChildNodes()) {
        output.push(">");
        childNodes = node2.childNodes;
        for (i = 0, len = childNodes.length; i < len; ++i) {
          serialize(childNodes.item(i), output);
        }
        output.push("</", node2.tagName, ">");
      } else {
        output.push("/>");
      }
      break;
    case 8:
      output.push("<!--", escape(node2.nodeValue, TEXT_ENTITIES), "-->");
      break;
    case 4:
      output.push("<![CDATA[", node2.nodeValue, "]]>");
      break;
    default:
      throw new Error("unable to handle node " + node2.nodeType);
  }
  return output;
}
function set(element, svg) {
  var parsed = parse(svg);
  clear(element);
  if (!svg) {
    return;
  }
  if (!isFragment(parsed)) {
    parsed = parsed.documentElement;
  }
  var nodes = slice(parsed.childNodes);
  for (var i = 0; i < nodes.length; i++) {
    appendTo(nodes[i], element);
  }
}
function get(element) {
  var child = element.firstChild, output = [];
  while (child) {
    serialize(child, output);
    child = child.nextSibling;
  }
  return output.join("");
}
function isFragment(node2) {
  return node2.nodeName === "#document-fragment";
}
function innerSVG(element, svg) {
  if (svg !== void 0) {
    try {
      set(element, svg);
    } catch (e2) {
      throw new Error("error parsing SVG: " + e2.message);
    }
    return element;
  } else {
    return get(element);
  }
}
function slice(arr) {
  return Array.prototype.slice.call(arr);
}
function remove(element) {
  var parent = element.parentNode;
  if (parent) {
    parent.removeChild(element);
  }
  return element;
}
function wrapMatrix(transformList, transform2) {
  if (transform2 instanceof SVGMatrix) {
    return transformList.createSVGTransformFromMatrix(transform2);
  }
  return transform2;
}
function setTransforms(transformList, transforms) {
  var i, t;
  transformList.clear();
  for (i = 0; t = transforms[i]; i++) {
    transformList.appendItem(wrapMatrix(transformList, t));
  }
}
function transform(node2, transforms) {
  var transformList = node2.transform.baseVal;
  if (transforms) {
    if (!Array.isArray(transforms)) {
      transforms = [transforms];
    }
    setTransforms(transformList, transforms);
  }
  return transformList.consolidate();
}

// node_modules/.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/util/DrilldownUtil.js
var planeSuffix = "_plane";
function getShapeIdFromPlane(element) {
  var id = element.id;
  return removePlaneSuffix(id);
}
function getPlaneIdFromShape(element) {
  var id = element.id;
  if (is(element, "bpmn:SubProcess")) {
    return addPlaneSuffix(id);
  }
  return id;
}
function toPlaneId(id) {
  return addPlaneSuffix(id);
}
function isPlane(element) {
  var di = getDi(element);
  return is(di, "bpmndi:BPMNPlane");
}
function addPlaneSuffix(id) {
  return id + planeSuffix;
}
function removePlaneSuffix(id) {
  return id.replace(new RegExp(planeSuffix + "$"), "");
}

export {
  index_esm_default,
  getShapeIdFromPlane,
  getPlaneIdFromShape,
  toPlaneId,
  isPlane,
  e,
  appendTo,
  append,
  attr,
  classes,
  clear,
  clone,
  create,
  createMatrix,
  createTransform,
  innerSVG,
  remove,
  transform
};
//# sourceMappingURL=chunk-KLPCQ2XZ.js.map
