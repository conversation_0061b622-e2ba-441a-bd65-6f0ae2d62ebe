{"version": 3, "sources": ["../../.pnpm/inherits-browser@0.1.0/node_modules/inherits-browser/index.js", "../../.pnpm/ids@1.0.5/node_modules/ids/node_modules/hat/index.js", "../../.pnpm/ids@1.0.5/node_modules/ids/index.js", "../../.pnpm/tiny-svg@3.1.3/node_modules/tiny-svg/dist/index.esm.js", "../../.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/util/DrilldownUtil.js"], "sourcesContent": ["export default function inherits(ctor, superCtor) {\n  if (superCtor) {\n    ctor.super_ = superCtor\n    ctor.prototype = Object.create(superCtor.prototype, {\n      constructor: {\n        value: ctor,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    })\n  }\n};", "var hat = module.exports = function (bits, base) {\n    if (!base) base = 16;\n    if (bits === undefined) bits = 128;\n    if (bits <= 0) return '0';\n    \n    var digits = Math.log(Math.pow(2, bits)) / Math.log(base);\n    for (var i = 2; digits === Infinity; i *= 2) {\n        digits = Math.log(Math.pow(2, bits / i)) / Math.log(base) * i;\n    }\n    \n    var rem = digits - Math.floor(digits);\n    \n    var res = '';\n    \n    for (var i = 0; i < Math.floor(digits); i++) {\n        var x = Math.floor(Math.random() * base).toString(base);\n        res = x + res;\n    }\n    \n    if (rem) {\n        var b = Math.pow(base, rem);\n        var x = Math.floor(Math.random() * b).toString(base);\n        res = x + res;\n    }\n    \n    var parsed = parseInt(res, base);\n    if (parsed !== Infinity && parsed >= Math.pow(2, bits)) {\n        return hat(bits, base)\n    }\n    else return res;\n};\n\nhat.rack = function (bits, base, expandBy) {\n    var fn = function (data) {\n        var iters = 0;\n        do {\n            if (iters ++ > 10) {\n                if (expandBy) bits += expandBy;\n                else throw new Error('too many ID collisions, use more bits')\n            }\n            \n            var id = hat(bits, base);\n        } while (Object.hasOwnProperty.call(hats, id));\n        \n        hats[id] = data;\n        return id;\n    };\n    var hats = fn.hats = {};\n    \n    fn.get = function (id) {\n        return fn.hats[id];\n    };\n    \n    fn.set = function (id, value) {\n        fn.hats[id] = value;\n        return fn;\n    };\n    \n    fn.bits = bits || 128;\n    fn.base = base || 16;\n    return fn;\n};\n", "import hat from 'hat';\n\n\n/**\n * Create a new id generator / cache instance.\n *\n * You may optionally provide a seed that is used internally.\n *\n * @param {Seed} seed\n */\nexport default function Ids(seed) {\n\n  if (!(this instanceof Ids)) {\n    return new Ids(seed);\n  }\n\n  seed = seed || [ 128, 36, 1 ];\n  this._seed = seed.length ? hat.rack(seed[0], seed[1], seed[2]) : seed;\n}\n\n/**\n * Generate a next id.\n *\n * @param {Object} [element] element to bind the id to\n *\n * @return {String} id\n */\nIds.prototype.next = function(element) {\n  return this._seed(element || true);\n};\n\n/**\n * Generate a next id with a given prefix.\n *\n * @param {Object} [element] element to bind the id to\n *\n * @return {String} id\n */\nIds.prototype.nextPrefixed = function(prefix, element) {\n  var id;\n\n  do {\n    id = prefix + this.next(true);\n  } while (this.assigned(id));\n\n  // claim {prefix}{random}\n  this.claim(id, element);\n\n  // return\n  return id;\n};\n\n/**\n * Manually claim an existing id.\n *\n * @param {String} id\n * @param {String} [element] element the id is claimed by\n */\nIds.prototype.claim = function(id, element) {\n  this._seed.set(id, element || true);\n};\n\n/**\n * Returns true if the given id has already been assigned.\n *\n * @param  {String} id\n * @return {Boolean}\n */\nIds.prototype.assigned = function(id) {\n  return this._seed.get(id) || false;\n};\n\n/**\n * Unclaim an id.\n *\n * @param  {String} id the id to unclaim\n */\nIds.prototype.unclaim = function(id) {\n  delete this._seed.hats[id];\n};\n\n\n/**\n * Clear all claimed ids.\n */\nIds.prototype.clear = function() {\n\n  var hats = this._seed.hats,\n      id;\n\n  for (id in hats) {\n    this.unclaim(id);\n  }\n};", "function ensureImported(element, target) {\n\n  if (element.ownerDocument !== target.ownerDocument) {\n    try {\n\n      // may fail on webkit\n      return target.ownerDocument.importNode(element, true);\n    } catch (e) {\n\n      // ignore\n    }\n  }\n\n  return element;\n}\n\n/**\n * appendTo utility\n */\n\n\n/**\n * Append a node to a target element and return the appended node.\n *\n * @param  {SVGElement} element\n * @param  {SVGElement} target\n *\n * @return {SVGElement} the appended node\n */\nfunction appendTo(element, target) {\n  return target.appendChild(ensureImported(element, target));\n}\n\n/**\n * append utility\n */\n\n\n/**\n * Append a node to an element\n *\n * @param  {SVGElement} element\n * @param  {SVGElement} node\n *\n * @return {SVGElement} the element\n */\nfunction append(target, node) {\n  appendTo(node, target);\n  return target;\n}\n\n/**\n * attribute accessor utility\n */\n\nvar LENGTH_ATTR = 2;\n\nvar CSS_PROPERTIES = {\n  'alignment-baseline': 1,\n  'baseline-shift': 1,\n  'clip': 1,\n  'clip-path': 1,\n  'clip-rule': 1,\n  'color': 1,\n  'color-interpolation': 1,\n  'color-interpolation-filters': 1,\n  'color-profile': 1,\n  'color-rendering': 1,\n  'cursor': 1,\n  'direction': 1,\n  'display': 1,\n  'dominant-baseline': 1,\n  'enable-background': 1,\n  'fill': 1,\n  'fill-opacity': 1,\n  'fill-rule': 1,\n  'filter': 1,\n  'flood-color': 1,\n  'flood-opacity': 1,\n  'font': 1,\n  'font-family': 1,\n  'font-size': LENGTH_ATTR,\n  'font-size-adjust': 1,\n  'font-stretch': 1,\n  'font-style': 1,\n  'font-variant': 1,\n  'font-weight': 1,\n  'glyph-orientation-horizontal': 1,\n  'glyph-orientation-vertical': 1,\n  'image-rendering': 1,\n  'kerning': 1,\n  'letter-spacing': 1,\n  'lighting-color': 1,\n  'marker': 1,\n  'marker-end': 1,\n  'marker-mid': 1,\n  'marker-start': 1,\n  'mask': 1,\n  'opacity': 1,\n  'overflow': 1,\n  'pointer-events': 1,\n  'shape-rendering': 1,\n  'stop-color': 1,\n  'stop-opacity': 1,\n  'stroke': 1,\n  'stroke-dasharray': 1,\n  'stroke-dashoffset': 1,\n  'stroke-linecap': 1,\n  'stroke-linejoin': 1,\n  'stroke-miterlimit': 1,\n  'stroke-opacity': 1,\n  'stroke-width': LENGTH_ATTR,\n  'text-anchor': 1,\n  'text-decoration': 1,\n  'text-rendering': 1,\n  'unicode-bidi': 1,\n  'visibility': 1,\n  'word-spacing': 1,\n  'writing-mode': 1\n};\n\n\nfunction getAttribute(node, name) {\n  if (CSS_PROPERTIES[name]) {\n    return node.style[name];\n  } else {\n    return node.getAttributeNS(null, name);\n  }\n}\n\nfunction setAttribute(node, name, value) {\n  var hyphenated = name.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n\n  var type = CSS_PROPERTIES[hyphenated];\n\n  if (type) {\n\n    // append pixel unit, unless present\n    if (type === LENGTH_ATTR && typeof value === 'number') {\n      value = String(value) + 'px';\n    }\n\n    node.style[hyphenated] = value;\n  } else {\n    node.setAttributeNS(null, name, value);\n  }\n}\n\nfunction setAttributes(node, attrs) {\n\n  var names = Object.keys(attrs), i, name;\n\n  for (i = 0, name; (name = names[i]); i++) {\n    setAttribute(node, name, attrs[name]);\n  }\n}\n\n/**\n * Gets or sets raw attributes on a node.\n *\n * @param  {SVGElement} node\n * @param  {Object} [attrs]\n * @param  {String} [name]\n * @param  {String} [value]\n *\n * @return {String}\n */\nfunction attr(node, name, value) {\n  if (typeof name === 'string') {\n    if (value !== undefined) {\n      setAttribute(node, name, value);\n    } else {\n      return getAttribute(node, name);\n    }\n  } else {\n    setAttributes(node, name);\n  }\n\n  return node;\n}\n\n/**\r\n * Taken from https://github.com/component/classes\r\n *\r\n * Without the component bits.\r\n */\r\n\r\n/**\r\n * toString reference.\r\n */\r\n\r\nconst toString = Object.prototype.toString;\r\n\r\n/**\r\n  * Wrap `el` in a `ClassList`.\r\n  *\r\n  * @param {Element} el\r\n  * @return {ClassList}\r\n  * @api public\r\n  */\r\n\r\nfunction classes(el) {\r\n  return new ClassList(el);\r\n}\r\n\r\nfunction ClassList(el) {\r\n  if (!el || !el.nodeType) {\r\n    throw new Error('A DOM element reference is required');\r\n  }\r\n  this.el = el;\r\n  this.list = el.classList;\r\n}\r\n\r\n/**\r\n  * Add class `name` if not already present.\r\n  *\r\n  * @param {String} name\r\n  * @return {ClassList}\r\n  * @api public\r\n  */\r\n\r\nClassList.prototype.add = function(name) {\r\n  this.list.add(name);\r\n  return this;\r\n};\r\n\r\n/**\r\n  * Remove class `name` when present, or\r\n  * pass a regular expression to remove\r\n  * any which match.\r\n  *\r\n  * @param {String|RegExp} name\r\n  * @return {ClassList}\r\n  * @api public\r\n  */\r\n\r\nClassList.prototype.remove = function(name) {\r\n  if ('[object RegExp]' == toString.call(name)) {\r\n    return this.removeMatching(name);\r\n  }\r\n\r\n  this.list.remove(name);\r\n  return this;\r\n};\r\n\r\n/**\r\n  * Remove all classes matching `re`.\r\n  *\r\n  * @param {RegExp} re\r\n  * @return {ClassList}\r\n  * @api private\r\n  */\r\n\r\nClassList.prototype.removeMatching = function(re) {\r\n  const arr = this.array();\r\n  for (let i = 0; i < arr.length; i++) {\r\n    if (re.test(arr[i])) {\r\n      this.remove(arr[i]);\r\n    }\r\n  }\r\n  return this;\r\n};\r\n\r\n/**\r\n  * Toggle class `name`, can force state via `force`.\r\n  *\r\n  * For browsers that support classList, but do not support `force` yet,\r\n  * the mistake will be detected and corrected.\r\n  *\r\n  * @param {String} name\r\n  * @param {Boolean} force\r\n  * @return {ClassList}\r\n  * @api public\r\n  */\r\n\r\nClassList.prototype.toggle = function(name, force) {\r\n  if ('undefined' !== typeof force) {\r\n    if (force !== this.list.toggle(name, force)) {\r\n      this.list.toggle(name); // toggle again to correct\r\n    }\r\n  } else {\r\n    this.list.toggle(name);\r\n  }\r\n  return this;\r\n};\r\n\r\n/**\r\n  * Return an array of classes.\r\n  *\r\n  * @return {Array}\r\n  * @api public\r\n  */\r\n\r\nClassList.prototype.array = function() {\r\n  return Array.from(this.list);\r\n};\r\n\r\n/**\r\n  * Check if class `name` is present.\r\n  *\r\n  * @param {String} name\r\n  * @return {ClassList}\r\n  * @api public\r\n  */\r\n\r\nClassList.prototype.has =\r\n ClassList.prototype.contains = function(name) {\r\n   return this.list.contains(name);\r\n };\n\n/**\n * Clear utility\n */\n\n/**\n * Removes all children from the given element\n *\n * @param  {SVGElement} element\n * @return {Element} the element (for chaining)\n */\nfunction clear(element) {\n  var child;\n\n  while ((child = element.firstChild)) {\n    element.removeChild(child);\n  }\n\n  return element;\n}\n\nfunction clone(element) {\n  return element.cloneNode(true);\n}\n\nvar ns = {\n  svg: 'http://www.w3.org/2000/svg'\n};\n\n/**\n * DOM parsing utility\n */\n\n\nvar SVG_START = '<svg xmlns=\"' + ns.svg + '\"';\n\nfunction parse(svg) {\n\n  var unwrap = false;\n\n  // ensure we import a valid svg document\n  if (svg.substring(0, 4) === '<svg') {\n    if (svg.indexOf(ns.svg) === -1) {\n      svg = SVG_START + svg.substring(4);\n    }\n  } else {\n\n    // namespace svg\n    svg = SVG_START + '>' + svg + '</svg>';\n    unwrap = true;\n  }\n\n  var parsed = parseDocument(svg);\n\n  if (!unwrap) {\n    return parsed;\n  }\n\n  var fragment = document.createDocumentFragment();\n\n  var parent = parsed.firstChild;\n\n  while (parent.firstChild) {\n    fragment.appendChild(parent.firstChild);\n  }\n\n  return fragment;\n}\n\nfunction parseDocument(svg) {\n\n  var parser;\n\n  // parse\n  parser = new DOMParser();\n  parser.async = false;\n\n  return parser.parseFromString(svg, 'text/xml');\n}\n\n/**\n * Create utility for SVG elements\n */\n\n\n\n/**\n * Create a specific type from name or SVG markup.\n *\n * @param {String} name the name or markup of the element\n * @param {Object} [attrs] attributes to set on the element\n *\n * @returns {SVGElement}\n */\nfunction create(name, attrs) {\n  var element;\n\n  name = name.trim();\n\n  if (name.charAt(0) === '<') {\n    element = parse(name).firstChild;\n    element = document.importNode(element, true);\n  } else {\n    element = document.createElementNS(ns.svg, name);\n  }\n\n  if (attrs) {\n    attr(element, attrs);\n  }\n\n  return element;\n}\n\n/**\n * Events handling utility\n */\n\nfunction on(node, event, listener, useCapture) {\n  node.addEventListener(event, listener, useCapture);\n}\n\nfunction off(node, event, listener, useCapture) {\n  node.removeEventListener(event, listener, useCapture);\n}\n\n/**\n * Geometry helpers\n */\n\n\n// fake node used to instantiate svg geometry elements\nvar node = null;\n\nfunction getNode() {\n  if (node === null) {\n    node = create('svg');\n  }\n\n  return node;\n}\n\nfunction extend(object, props) {\n  var i, k, keys = Object.keys(props);\n\n  for (i = 0; (k = keys[i]); i++) {\n    object[k] = props[k];\n  }\n\n  return object;\n}\n\n\nfunction createPoint(x, y) {\n  var point = getNode().createSVGPoint();\n\n  switch (arguments.length) {\n  case 0:\n    return point;\n  case 2:\n    x = {\n      x: x,\n      y: y\n    };\n    break;\n  }\n\n  return extend(point, x);\n}\n\n/**\n * Create matrix via args.\n *\n * @example\n *\n * createMatrix({ a: 1, b: 1 });\n * createMatrix();\n * createMatrix(1, 2, 0, 0, 30, 20);\n *\n * @return {SVGMatrix}\n */\nfunction createMatrix(a, b, c, d, e, f) {\n  var matrix = getNode().createSVGMatrix();\n\n  switch (arguments.length) {\n  case 0:\n    return matrix;\n  case 1:\n    return extend(matrix, a);\n  case 6:\n    return extend(matrix, {\n      a: a,\n      b: b,\n      c: c,\n      d: d,\n      e: e,\n      f: f\n    });\n  }\n}\n\nfunction createTransform(matrix) {\n  if (matrix) {\n    return getNode().createSVGTransformFromMatrix(matrix);\n  } else {\n    return getNode().createSVGTransform();\n  }\n}\n\n/**\n * Serialization util\n */\n\nvar TEXT_ENTITIES = /([&<>]{1})/g;\nvar ATTR_ENTITIES = /([&<>\\n\\r\"]{1})/g;\n\nvar ENTITY_REPLACEMENT = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '\\''\n};\n\nfunction escape(str, pattern) {\n\n  function replaceFn(match, entity) {\n    return ENTITY_REPLACEMENT[entity] || entity;\n  }\n\n  return str.replace(pattern, replaceFn);\n}\n\nfunction serialize(node, output) {\n\n  var i, len, attrMap, attrNode, childNodes;\n\n  switch (node.nodeType) {\n\n  // TEXT\n  case 3:\n\n    // replace special XML characters\n    output.push(escape(node.textContent, TEXT_ENTITIES));\n    break;\n\n  // ELEMENT\n  case 1:\n    output.push('<', node.tagName);\n\n    if (node.hasAttributes()) {\n      attrMap = node.attributes;\n      for (i = 0, len = attrMap.length; i < len; ++i) {\n        attrNode = attrMap.item(i);\n        output.push(' ', attrNode.name, '=\"', escape(attrNode.value, ATTR_ENTITIES), '\"');\n      }\n    }\n\n    if (node.hasChildNodes()) {\n      output.push('>');\n      childNodes = node.childNodes;\n      for (i = 0, len = childNodes.length; i < len; ++i) {\n        serialize(childNodes.item(i), output);\n      }\n      output.push('</', node.tagName, '>');\n    } else {\n      output.push('/>');\n    }\n    break;\n\n  // COMMENT\n  case 8:\n    output.push('<!--', escape(node.nodeValue, TEXT_ENTITIES), '-->');\n    break;\n\n  // CDATA\n  case 4:\n    output.push('<![CDATA[', node.nodeValue, ']]>');\n    break;\n\n  default:\n    throw new Error('unable to handle node ' + node.nodeType);\n  }\n\n  return output;\n}\n\n/**\n * innerHTML like functionality for SVG elements.\n * based on innerSVG (https://code.google.com/p/innersvg)\n */\n\n\n\nfunction set(element, svg) {\n\n  var parsed = parse(svg);\n\n  // clear element contents\n  clear(element);\n\n  if (!svg) {\n    return;\n  }\n\n  if (!isFragment(parsed)) {\n\n    // extract <svg> from parsed document\n    parsed = parsed.documentElement;\n  }\n\n  var nodes = slice(parsed.childNodes);\n\n  // import + append each node\n  for (var i = 0; i < nodes.length; i++) {\n    appendTo(nodes[i], element);\n  }\n\n}\n\nfunction get(element) {\n  var child = element.firstChild,\n      output = [];\n\n  while (child) {\n    serialize(child, output);\n    child = child.nextSibling;\n  }\n\n  return output.join('');\n}\n\nfunction isFragment(node) {\n  return node.nodeName === '#document-fragment';\n}\n\nfunction innerSVG(element, svg) {\n\n  if (svg !== undefined) {\n\n    try {\n      set(element, svg);\n    } catch (e) {\n      throw new Error('error parsing SVG: ' + e.message);\n    }\n\n    return element;\n  } else {\n    return get(element);\n  }\n}\n\n\nfunction slice(arr) {\n  return Array.prototype.slice.call(arr);\n}\n\n/**\n * Selection utilities\n */\n\nfunction select(node, selector) {\n  return node.querySelector(selector);\n}\n\nfunction selectAll(node, selector) {\n  var nodes = node.querySelectorAll(selector);\n\n  return [].map.call(nodes, function(element) {\n    return element;\n  });\n}\n\n/**\n * prependTo utility\n */\n\n\n/**\n * Prepend a node to a target element and return the prepended node.\n *\n * @param  {SVGElement} node\n * @param  {SVGElement} target\n *\n * @return {SVGElement} the prepended node\n */\nfunction prependTo(node, target) {\n  return target.insertBefore(ensureImported(node, target), target.firstChild || null);\n}\n\n/**\n * prepend utility\n */\n\n\n/**\n * Prepend a node to a target element\n *\n * @param  {SVGElement} target\n * @param  {SVGElement} node\n *\n * @return {SVGElement} the target element\n */\nfunction prepend(target, node) {\n  prependTo(node, target);\n  return target;\n}\n\nfunction remove(element) {\n  var parent = element.parentNode;\n\n  if (parent) {\n    parent.removeChild(element);\n  }\n\n  return element;\n}\n\n/**\n * Replace utility\n */\n\n\nfunction replace(element, replacement) {\n  element.parentNode.replaceChild(ensureImported(replacement, element), element);\n  return replacement;\n}\n\n/**\n * transform accessor utility\n */\n\nfunction wrapMatrix(transformList, transform) {\n  if (transform instanceof SVGMatrix) {\n    return transformList.createSVGTransformFromMatrix(transform);\n  }\n\n  return transform;\n}\n\n\nfunction setTransforms(transformList, transforms) {\n  var i, t;\n\n  transformList.clear();\n\n  for (i = 0; (t = transforms[i]); i++) {\n    transformList.appendItem(wrapMatrix(transformList, t));\n  }\n}\n\n/**\n * Get or set the transforms on the given node.\n *\n * @param {SVGElement} node\n * @param  {SVGTransform|SVGMatrix|Array<SVGTransform|SVGMatrix>} [transforms]\n *\n * @return {SVGTransform} the consolidated transform\n */\nfunction transform(node, transforms) {\n  var transformList = node.transform.baseVal;\n\n  if (transforms) {\n\n    if (!Array.isArray(transforms)) {\n      transforms = [ transforms ];\n    }\n\n    setTransforms(transformList, transforms);\n  }\n\n  return transformList.consolidate();\n}\n\nexport { append, appendTo, attr, classes, clear, clone, create, createMatrix, createPoint, createTransform, innerSVG, off, on, prepend, prependTo, remove, replace, select, selectAll, transform };\n", "import { getDi, is } from './ModelUtil';\n\n/**\n * @typedef {import('../model/Types').Element} Element\n * @typedef {import('../model/Types').ModdleElement} ModdleElement\n */\n\nexport var planeSuffix = '_plane';\n\n/**\n * Get primary shape ID for a plane.\n *\n * @param  {Element|ModdleElement} element\n *\n * @return {string}\n */\nexport function getShapeIdFromPlane(element) {\n  var id = element.id;\n\n  return removePlaneSuffix(id);\n}\n\n/**\n * Get plane ID for a primary shape.\n *\n * @param  {Element|ModdleElement} element\n *\n * @return {string}\n */\nexport function getPlaneIdFromShape(element) {\n  var id = element.id;\n\n  if (is(element, 'bpmn:SubProcess')) {\n    return addPlaneSuffix(id);\n  }\n\n  return id;\n}\n\n/**\n * Get plane ID for primary shape ID.\n *\n * @param {string} id\n *\n * @return {string}\n */\nexport function toPlaneId(id) {\n  return addPlaneSuffix(id);\n}\n\n/**\n * Check wether element is plane.\n *\n * @param  {Element|ModdleElement} element\n *\n * @return {boolean}\n */\nexport function isPlane(element) {\n  var di = getDi(element);\n\n  return is(di, 'bpmndi:BPMNPlane');\n}\n\nfunction addPlaneSuffix(id) {\n  return id + planeSuffix;\n}\n\nfunction removePlaneSuffix(id) {\n  return id.replace(new RegExp(planeSuffix + '$'), '');\n}"], "mappings": ";;;;;;AAAe,SAASA,EAASC,IAAMC,GAAAA;AACjCA,QACFD,GAAKE,SAASD,GACdD,GAAKG,YAAYC,OAAOC,OAAOJ,EAAUE,WAAW,EAClDG,aAAa,EACXC,OAAOP,IACPQ,YAAAA,OACAC,UAAAA,MACAC,cAAAA,KAAc,EAAA,CAAA;AAAA;;;;;;;ACRtB,MAAI,MAAM,OAAA,UAAiB,SAAU,MAAM,MAAM;AAC7C,QAAI,CAAC;AAAM,aAAO;AAClB,QAAI,SAAS;AAAW,aAAO;AAC/B,QAAI,QAAQ;AAAG,aAAO;AAEtB,QAAI,SAAS,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI;AACxD,aAAS,IAAI,GAAG,WAAW,UAAU,KAAK,GAAG;AACzC,eAAS,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI;;AAGhE,QAAI,MAAM,SAAS,KAAK,MAAM,MAAM;AAEpC,QAAI,MAAM;AAEV,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,MAAM,GAAG,KAAK;AACzC,UAAI,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,IAAI,EAAE,SAAS,IAAI;AACtD,YAAM,IAAI;;AAGd,QAAI,KAAK;AACL,UAAI,IAAI,KAAK,IAAI,MAAM,GAAG;AAC1B,UAAI,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,EAAE,SAAS,IAAI;AACnD,YAAM,IAAI;;AAGd,QAAI,SAAS,SAAS,KAAK,IAAI;AAC/B,QAAI,WAAW,YAAY,UAAU,KAAK,IAAI,GAAG,IAAI,GAAG;AACpD,aAAO,IAAI,MAAM,IAAI;;AAEpB,aAAO;;AAGhB,MAAI,OAAO,SAAU,MAAM,MAAM,UAAU;AACvC,QAAI,KAAK,SAAU,MAAM;AACrB,UAAI,QAAQ;AACZ,SAAG;AACC,YAAI,UAAW,IAAI;AACf,cAAI;AAAU,oBAAQ;;AACjB,kBAAM,IAAI,MAAM,uCAAuC;;AAGhE,YAAI,KAAK,IAAI,MAAM,IAAI;eAClB,OAAO,eAAe,KAAK,MAAM,EAAE;AAE5C,WAAK,EAAE,IAAI;AACX,aAAO;;AAEX,QAAI,OAAO,GAAG,OAAO,CAAA;AAErB,OAAG,MAAM,SAAU,IAAI;AACnB,aAAO,GAAG,KAAK,EAAE;;AAGrB,OAAG,MAAM,SAAU,IAAI,OAAO;AAC1B,SAAG,KAAK,EAAE,IAAI;AACd,aAAO;;AAGX,OAAG,OAAO,QAAQ;AAClB,OAAG,OAAO,QAAQ;AAClB,WAAO;;;AClDI,SAASC,IAAIC,MAAM;AAEhC,MAAI,EAAE,gBAAgBD,MAAM;AAC1B,WAAO,IAAIA,IAAIC,IAAI;;AAGrBA,SAAOA,QAAQ,CAAE,KAAK,IAAI,CAAC;AAC3B,OAAKC,QAAQD,KAAKE,SAASC,MAAIC,KAAKJ,KAAK,CAAC,GAAGA,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,IAAIA;;AAUnED,IAAIM,UAAUC,OAAO,SAASC,SAAS;AACrC,SAAO,KAAKN,MAAMM,WAAW,IAAI;;AAUnCR,IAAIM,UAAUG,eAAe,SAASC,QAAQF,SAAS;AACrD,MAAIG;AAEJ,KAAG;AACDA,SAAKD,SAAS,KAAKH,KAAK,IAAI;WACrB,KAAKK,SAASD,EAAE;AAGzB,OAAKE,MAAMF,IAAIH,OAAO;AAGtB,SAAOG;;AASTX,IAAIM,UAAUO,QAAQ,SAASF,IAAIH,SAAS;AAC1C,OAAKN,MAAMY,IAAIH,IAAIH,WAAW,IAAI;;AASpCR,IAAIM,UAAUM,WAAW,SAASD,IAAI;AACpC,SAAO,KAAKT,MAAMa,IAAIJ,EAAE,KAAK;;AAQ/BX,IAAIM,UAAUU,UAAU,SAASL,IAAI;AACnC,SAAO,KAAKT,MAAMe,KAAKN,EAAE;;AAO3BX,IAAIM,UAAUY,QAAQ,WAAW;AAE/B,MAAID,OAAO,KAAKf,MAAMe,MAClBN;AAEJ,OAAKA,MAAMM,MAAM;AACf,SAAKD,QAAQL,EAAE;;;;;;AC3FnB,SAAS,eAAe,SAAS,QAAQ;AAEvC,MAAI,QAAQ,kBAAkB,OAAO,eAAe;AAClD,QAAI;AAGF,aAAO,OAAO,cAAc,WAAW,SAAS,IAAI;AAAA,IACtD,SAASQ,IAAG;AAAA,IAGZ;AAAA,EACF;AAEA,SAAO;AACT;AAeA,SAAS,SAAS,SAAS,QAAQ;AACjC,SAAO,OAAO,YAAY,eAAe,SAAS,MAAM,CAAC;AAC3D;AAeA,SAAS,OAAO,QAAQC,OAAM;AAC5B,WAASA,OAAM,MAAM;AACrB,SAAO;AACT;AAMA,IAAI,cAAc;AAElB,IAAI,iBAAiB;AAAA,EACnB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,UAAU;AAAA,EACV,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAClB;AAGA,SAAS,aAAaA,OAAM,MAAM;AAChC,MAAI,eAAe,IAAI,GAAG;AACxB,WAAOA,MAAK,MAAM,IAAI;AAAA,EACxB,OAAO;AACL,WAAOA,MAAK,eAAe,MAAM,IAAI;AAAA,EACvC;AACF;AAEA,SAAS,aAAaA,OAAM,MAAM,OAAO;AACvC,MAAI,aAAa,KAAK,QAAQ,mBAAmB,OAAO,EAAE,YAAY;AAEtE,MAAI,OAAO,eAAe,UAAU;AAEpC,MAAI,MAAM;AAGR,QAAI,SAAS,eAAe,OAAO,UAAU,UAAU;AACrD,cAAQ,OAAO,KAAK,IAAI;AAAA,IAC1B;AAEA,IAAAA,MAAK,MAAM,UAAU,IAAI;AAAA,EAC3B,OAAO;AACL,IAAAA,MAAK,eAAe,MAAM,MAAM,KAAK;AAAA,EACvC;AACF;AAEA,SAAS,cAAcA,OAAM,OAAO;AAElC,MAAI,QAAQ,OAAO,KAAK,KAAK,GAAG,GAAG;AAEnC,OAAK,IAAI,GAAG,MAAO,OAAO,MAAM,CAAC,GAAI,KAAK;AACxC,iBAAaA,OAAM,MAAM,MAAM,IAAI,CAAC;AAAA,EACtC;AACF;AAYA,SAAS,KAAKA,OAAM,MAAM,OAAO;AAC/B,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,UAAU,QAAW;AACvB,mBAAaA,OAAM,MAAM,KAAK;AAAA,IAChC,OAAO;AACL,aAAO,aAAaA,OAAM,IAAI;AAAA,IAChC;AAAA,EACF,OAAO;AACL,kBAAcA,OAAM,IAAI;AAAA,EAC1B;AAEA,SAAOA;AACT;AAYA,IAAM,WAAW,OAAO,UAAU;AAUlC,SAAS,QAAQ,IAAI;AACnB,SAAO,IAAI,UAAU,EAAE;AACzB;AAEA,SAAS,UAAU,IAAI;AACrB,MAAI,CAAC,MAAM,CAAC,GAAG,UAAU;AACvB,UAAM,IAAI,MAAM,qCAAqC;AAAA,EACvD;AACA,OAAK,KAAK;AACV,OAAK,OAAO,GAAG;AACjB;AAUA,UAAU,UAAU,MAAM,SAAS,MAAM;AACvC,OAAK,KAAK,IAAI,IAAI;AAClB,SAAO;AACT;AAYA,UAAU,UAAU,SAAS,SAAS,MAAM;AAC1C,MAAI,qBAAqB,SAAS,KAAK,IAAI,GAAG;AAC5C,WAAO,KAAK,eAAe,IAAI;AAAA,EACjC;AAEA,OAAK,KAAK,OAAO,IAAI;AACrB,SAAO;AACT;AAUA,UAAU,UAAU,iBAAiB,SAAS,IAAI;AAChD,QAAM,MAAM,KAAK,MAAM;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG;AACnB,WAAK,OAAO,IAAI,CAAC,CAAC;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AAcA,UAAU,UAAU,SAAS,SAAS,MAAM,OAAO;AACjD,MAAI,gBAAgB,OAAO,OAAO;AAChC,QAAI,UAAU,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG;AAC3C,WAAK,KAAK,OAAO,IAAI;AAAA,IACvB;AAAA,EACF,OAAO;AACL,SAAK,KAAK,OAAO,IAAI;AAAA,EACvB;AACA,SAAO;AACT;AASA,UAAU,UAAU,QAAQ,WAAW;AACrC,SAAO,MAAM,KAAK,KAAK,IAAI;AAC7B;AAUA,UAAU,UAAU,MACnB,UAAU,UAAU,WAAW,SAAS,MAAM;AAC5C,SAAO,KAAK,KAAK,SAAS,IAAI;AAChC;AAYD,SAAS,MAAM,SAAS;AACtB,MAAI;AAEJ,SAAQ,QAAQ,QAAQ,YAAa;AACnC,YAAQ,YAAY,KAAK;AAAA,EAC3B;AAEA,SAAO;AACT;AAEA,SAAS,MAAM,SAAS;AACtB,SAAO,QAAQ,UAAU,IAAI;AAC/B;AAEA,IAAI,KAAK;AAAA,EACP,KAAK;AACP;AAOA,IAAI,YAAY,iBAAiB,GAAG,MAAM;AAE1C,SAAS,MAAM,KAAK;AAElB,MAAI,SAAS;AAGb,MAAI,IAAI,UAAU,GAAG,CAAC,MAAM,QAAQ;AAClC,QAAI,IAAI,QAAQ,GAAG,GAAG,MAAM,IAAI;AAC9B,YAAM,YAAY,IAAI,UAAU,CAAC;AAAA,IACnC;AAAA,EACF,OAAO;AAGL,UAAM,YAAY,MAAM,MAAM;AAC9B,aAAS;AAAA,EACX;AAEA,MAAI,SAAS,cAAc,GAAG;AAE9B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,SAAS,uBAAuB;AAE/C,MAAI,SAAS,OAAO;AAEpB,SAAO,OAAO,YAAY;AACxB,aAAS,YAAY,OAAO,UAAU;AAAA,EACxC;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK;AAE1B,MAAI;AAGJ,WAAS,IAAI,UAAU;AACvB,SAAO,QAAQ;AAEf,SAAO,OAAO,gBAAgB,KAAK,UAAU;AAC/C;AAgBA,SAAS,OAAO,MAAM,OAAO;AAC3B,MAAI;AAEJ,SAAO,KAAK,KAAK;AAEjB,MAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,cAAU,MAAM,IAAI,EAAE;AACtB,cAAU,SAAS,WAAW,SAAS,IAAI;AAAA,EAC7C,OAAO;AACL,cAAU,SAAS,gBAAgB,GAAG,KAAK,IAAI;AAAA,EACjD;AAEA,MAAI,OAAO;AACT,SAAK,SAAS,KAAK;AAAA,EACrB;AAEA,SAAO;AACT;AAoBA,IAAI,OAAO;AAEX,SAAS,UAAU;AACjB,MAAI,SAAS,MAAM;AACjB,WAAO,OAAO,KAAK;AAAA,EACrB;AAEA,SAAO;AACT;AAEA,SAAS,OAAO,QAAQ,OAAO;AAC7B,MAAI,GAAG,GAAG,OAAO,OAAO,KAAK,KAAK;AAElC,OAAK,IAAI,GAAI,IAAI,KAAK,CAAC,GAAI,KAAK;AAC9B,WAAO,CAAC,IAAI,MAAM,CAAC;AAAA,EACrB;AAEA,SAAO;AACT;AA+BA,SAAS,aAAa,GAAG,GAAG,GAAG,GAAGC,IAAG,GAAG;AACtC,MAAI,SAAS,QAAQ,EAAE,gBAAgB;AAEvC,UAAQ,UAAU,QAAQ;AAAA,IAC1B,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO,OAAO,QAAQ,CAAC;AAAA,IACzB,KAAK;AACH,aAAO,OAAO,QAAQ;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAGA;AAAA,QACH;AAAA,MACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,QAAQ;AACV,WAAO,QAAQ,EAAE,6BAA6B,MAAM;AAAA,EACtD,OAAO;AACL,WAAO,QAAQ,EAAE,mBAAmB;AAAA,EACtC;AACF;AAMA,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AAEpB,IAAI,qBAAqB;AAAA,EACvB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEA,SAAS,OAAO,KAAK,SAAS;AAE5B,WAAS,UAAU,OAAO,QAAQ;AAChC,WAAO,mBAAmB,MAAM,KAAK;AAAA,EACvC;AAEA,SAAO,IAAI,QAAQ,SAAS,SAAS;AACvC;AAEA,SAAS,UAAUC,OAAM,QAAQ;AAE/B,MAAI,GAAG,KAAK,SAAS,UAAU;AAE/B,UAAQA,MAAK,UAAU;AAAA,IAGvB,KAAK;AAGH,aAAO,KAAK,OAAOA,MAAK,aAAa,aAAa,CAAC;AACnD;AAAA,IAGF,KAAK;AACH,aAAO,KAAK,KAAKA,MAAK,OAAO;AAE7B,UAAIA,MAAK,cAAc,GAAG;AACxB,kBAAUA,MAAK;AACf,aAAK,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC9C,qBAAW,QAAQ,KAAK,CAAC;AACzB,iBAAO,KAAK,KAAK,SAAS,MAAM,MAAM,OAAO,SAAS,OAAO,aAAa,GAAG,GAAG;AAAA,QAClF;AAAA,MACF;AAEA,UAAIA,MAAK,cAAc,GAAG;AACxB,eAAO,KAAK,GAAG;AACf,qBAAaA,MAAK;AAClB,aAAK,IAAI,GAAG,MAAM,WAAW,QAAQ,IAAI,KAAK,EAAE,GAAG;AACjD,oBAAU,WAAW,KAAK,CAAC,GAAG,MAAM;AAAA,QACtC;AACA,eAAO,KAAK,MAAMA,MAAK,SAAS,GAAG;AAAA,MACrC,OAAO;AACL,eAAO,KAAK,IAAI;AAAA,MAClB;AACA;AAAA,IAGF,KAAK;AACH,aAAO,KAAK,QAAQ,OAAOA,MAAK,WAAW,aAAa,GAAG,KAAK;AAChE;AAAA,IAGF,KAAK;AACH,aAAO,KAAK,aAAaA,MAAK,WAAW,KAAK;AAC9C;AAAA,IAEF;AACE,YAAM,IAAI,MAAM,2BAA2BA,MAAK,QAAQ;AAAA,EAC1D;AAEA,SAAO;AACT;AASA,SAAS,IAAI,SAAS,KAAK;AAEzB,MAAI,SAAS,MAAM,GAAG;AAGtB,QAAM,OAAO;AAEb,MAAI,CAAC,KAAK;AACR;AAAA,EACF;AAEA,MAAI,CAAC,WAAW,MAAM,GAAG;AAGvB,aAAS,OAAO;AAAA,EAClB;AAEA,MAAI,QAAQ,MAAM,OAAO,UAAU;AAGnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAS,MAAM,CAAC,GAAG,OAAO;AAAA,EAC5B;AAEF;AAEA,SAAS,IAAI,SAAS;AACpB,MAAI,QAAQ,QAAQ,YAChB,SAAS,CAAC;AAEd,SAAO,OAAO;AACZ,cAAU,OAAO,MAAM;AACvB,YAAQ,MAAM;AAAA,EAChB;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;AAEA,SAAS,WAAWA,OAAM;AACxB,SAAOA,MAAK,aAAa;AAC3B;AAEA,SAAS,SAAS,SAAS,KAAK;AAE9B,MAAI,QAAQ,QAAW;AAErB,QAAI;AACF,UAAI,SAAS,GAAG;AAAA,IAClB,SAASD,IAAG;AACV,YAAM,IAAI,MAAM,wBAAwBA,GAAE,OAAO;AAAA,IACnD;AAEA,WAAO;AAAA,EACT,OAAO;AACL,WAAO,IAAI,OAAO;AAAA,EACpB;AACF;AAGA,SAAS,MAAM,KAAK;AAClB,SAAO,MAAM,UAAU,MAAM,KAAK,GAAG;AACvC;AAqDA,SAAS,OAAO,SAAS;AACvB,MAAI,SAAS,QAAQ;AAErB,MAAI,QAAQ;AACV,WAAO,YAAY,OAAO;AAAA,EAC5B;AAEA,SAAO;AACT;AAgBA,SAAS,WAAW,eAAeE,YAAW;AAC5C,MAAIA,sBAAqB,WAAW;AAClC,WAAO,cAAc,6BAA6BA,UAAS;AAAA,EAC7D;AAEA,SAAOA;AACT;AAGA,SAAS,cAAc,eAAe,YAAY;AAChD,MAAI,GAAG;AAEP,gBAAc,MAAM;AAEpB,OAAK,IAAI,GAAI,IAAI,WAAW,CAAC,GAAI,KAAK;AACpC,kBAAc,WAAW,WAAW,eAAe,CAAC,CAAC;AAAA,EACvD;AACF;AAUA,SAAS,UAAUC,OAAM,YAAY;AACnC,MAAI,gBAAgBA,MAAK,UAAU;AAEnC,MAAI,YAAY;AAEd,QAAI,CAAC,MAAM,QAAQ,UAAU,GAAG;AAC9B,mBAAa,CAAE,UAAW;AAAA,IAC5B;AAEA,kBAAc,eAAe,UAAU;AAAA,EACzC;AAEA,SAAO,cAAc,YAAY;AACnC;;;ACpwBO,IAAI,cAAc;AASlB,SAAS,oBAAoB,SAAS;AAC3C,MAAI,KAAK,QAAQ;AAEjB,SAAO,kBAAkB,EAAE;AAC7B;AASO,SAAS,oBAAoB,SAAS;AAC3C,MAAI,KAAK,QAAQ;AAEjB,MAAI,GAAG,SAAS,iBAAiB,GAAG;AAClC,WAAO,eAAe,EAAE;AAAA,EAC1B;AAEA,SAAO;AACT;AASO,SAAS,UAAU,IAAI;AAC5B,SAAO,eAAe,EAAE;AAC1B;AASO,SAAS,QAAQ,SAAS;AAC/B,MAAI,KAAK,MAAM,OAAO;AAEtB,SAAO,GAAG,IAAI,kBAAkB;AAClC;AAEA,SAAS,eAAe,IAAI;AAC1B,SAAO,KAAK;AACd;AAEA,SAAS,kBAAkB,IAAI;AAC7B,SAAO,GAAG,QAAQ,IAAI,OAAO,cAAc,GAAG,GAAG,EAAE;AACrD;", "names": ["inherits", "ctor", "superCtor", "super_", "prototype", "Object", "create", "constructor", "value", "enumerable", "writable", "configurable", "Ids", "seed", "_seed", "length", "hat", "rack", "prototype", "next", "element", "nextPrefixed", "prefix", "id", "assigned", "claim", "set", "get", "unclaim", "hats", "clear", "e", "node", "e", "node", "transform", "node"]}