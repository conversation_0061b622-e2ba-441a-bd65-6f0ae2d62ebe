import {
  MarkerModel_default,
  MarkerView_default,
  checkMarkerInSeries,
  dataFilter,
  dataTransform,
  zoneFilter
} from "./chunk-ZR4V7UZP.js";
import {
  isCoordinateSystemType
} from "./chunk-QAR3K42R.js";
import {
  SeriesData_default,
  getVisualFromData
} from "./chunk-H732WCN4.js";
import {
  Polygon_default,
  asc,
  getECData,
  getLabelStatesModels,
  makeInner,
  parseDataValue,
  parsePercent,
  setLabelStyle,
  setStatesStylesFromModel,
  toggleHoverEmphasis,
  updateProps
} from "./chunk-Q47K3BNQ.js";
import {
  Group_default,
  __extends,
  curry,
  extend,
  filter,
  isString,
  map,
  mergeAll,
  modifyAlpha,
  retrieve
} from "./chunk-MUBQFVAI.js";

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkAreaModel.js
var MarkAreaModel = (
  /** @class */
  function(_super) {
    __extends(MarkAreaModel2, _super);
    function MarkAreaModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkAreaModel2.type;
      return _this;
    }
    MarkAreaModel2.prototype.createMarkerModelFromSeries = function(markerOpt, masterMarkerModel, ecModel) {
      return new MarkAreaModel2(markerOpt, masterMarkerModel, ecModel);
    };
    MarkAreaModel2.type = "markArea";
    MarkAreaModel2.defaultOption = {
      // zlevel: 0,
      // PENDING
      z: 1,
      tooltip: {
        trigger: "item"
      },
      // markArea should fixed on the coordinate system
      animation: false,
      label: {
        show: true,
        position: "top"
      },
      itemStyle: {
        // color and borderColor default to use color from series
        // color: 'auto'
        // borderColor: 'auto'
        borderWidth: 0
      },
      emphasis: {
        label: {
          show: true,
          position: "top"
        }
      }
    };
    return MarkAreaModel2;
  }(MarkerModel_default)
);
var MarkAreaModel_default = MarkAreaModel;

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkAreaView.js
var inner = makeInner();
var markAreaTransform = function(seriesModel, coordSys, maModel, item) {
  var item0 = item[0];
  var item1 = item[1];
  if (!item0 || !item1) {
    return;
  }
  var lt = dataTransform(seriesModel, item0);
  var rb = dataTransform(seriesModel, item1);
  var ltCoord = lt.coord;
  var rbCoord = rb.coord;
  ltCoord[0] = retrieve(ltCoord[0], -Infinity);
  ltCoord[1] = retrieve(ltCoord[1], -Infinity);
  rbCoord[0] = retrieve(rbCoord[0], Infinity);
  rbCoord[1] = retrieve(rbCoord[1], Infinity);
  var result = mergeAll([{}, lt, rb]);
  result.coord = [lt.coord, rb.coord];
  result.x0 = lt.x;
  result.y0 = lt.y;
  result.x1 = rb.x;
  result.y1 = rb.y;
  return result;
};
function isInfinity(val) {
  return !isNaN(val) && !isFinite(val);
}
function ifMarkAreaHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {
  var otherDimIndex = 1 - dimIndex;
  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]);
}
function markAreaFilter(coordSys, item) {
  var fromCoord = item.coord[0];
  var toCoord = item.coord[1];
  var item0 = {
    coord: fromCoord,
    x: item.x0,
    y: item.y0
  };
  var item1 = {
    coord: toCoord,
    x: item.x1,
    y: item.y1
  };
  if (isCoordinateSystemType(coordSys, "cartesian2d")) {
    if (fromCoord && toCoord && (ifMarkAreaHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkAreaHasOnlyDim(0, fromCoord, toCoord, coordSys))) {
      return true;
    }
    return zoneFilter(coordSys, item0, item1);
  }
  return dataFilter(coordSys, item0) || dataFilter(coordSys, item1);
}
function getSingleMarkerEndPoint(data, idx, dims, seriesModel, api) {
  var coordSys = seriesModel.coordinateSystem;
  var itemModel = data.getItemModel(idx);
  var point;
  var xPx = parsePercent(itemModel.get(dims[0]), api.getWidth());
  var yPx = parsePercent(itemModel.get(dims[1]), api.getHeight());
  if (!isNaN(xPx) && !isNaN(yPx)) {
    point = [xPx, yPx];
  } else {
    if (seriesModel.getMarkerPosition) {
      var pointValue0 = data.getValues(["x0", "y0"], idx);
      var pointValue1 = data.getValues(["x1", "y1"], idx);
      var clampPointValue0 = coordSys.clampData(pointValue0);
      var clampPointValue1 = coordSys.clampData(pointValue1);
      var pointValue = [];
      if (dims[0] === "x0") {
        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue1[0] : pointValue0[0];
      } else {
        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue0[0] : pointValue1[0];
      }
      if (dims[1] === "y0") {
        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue1[1] : pointValue0[1];
      } else {
        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue0[1] : pointValue1[1];
      }
      point = seriesModel.getMarkerPosition(pointValue, dims, true);
    } else {
      var x = data.get(dims[0], idx);
      var y = data.get(dims[1], idx);
      var pt = [x, y];
      coordSys.clampData && coordSys.clampData(pt, pt);
      point = coordSys.dataToPoint(pt, true);
    }
    if (isCoordinateSystemType(coordSys, "cartesian2d")) {
      var xAxis = coordSys.getAxis("x");
      var yAxis = coordSys.getAxis("y");
      var x = data.get(dims[0], idx);
      var y = data.get(dims[1], idx);
      if (isInfinity(x)) {
        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[dims[0] === "x0" ? 0 : 1]);
      } else if (isInfinity(y)) {
        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[dims[1] === "y0" ? 0 : 1]);
      }
    }
    if (!isNaN(xPx)) {
      point[0] = xPx;
    }
    if (!isNaN(yPx)) {
      point[1] = yPx;
    }
  }
  return point;
}
var dimPermutations = [["x0", "y0"], ["x1", "y0"], ["x1", "y1"], ["x0", "y1"]];
var MarkAreaView = (
  /** @class */
  function(_super) {
    __extends(MarkAreaView2, _super);
    function MarkAreaView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkAreaView2.type;
      return _this;
    }
    MarkAreaView2.prototype.updateTransform = function(markAreaModel, ecModel, api) {
      ecModel.eachSeries(function(seriesModel) {
        var maModel = MarkerModel_default.getMarkerModelFromSeries(seriesModel, "markArea");
        if (maModel) {
          var areaData_1 = maModel.getData();
          areaData_1.each(function(idx) {
            var points = map(dimPermutations, function(dim) {
              return getSingleMarkerEndPoint(areaData_1, idx, dim, seriesModel, api);
            });
            areaData_1.setItemLayout(idx, points);
            var el = areaData_1.getItemGraphicEl(idx);
            el.setShape("points", points);
          });
        }
      }, this);
    };
    MarkAreaView2.prototype.renderSeries = function(seriesModel, maModel, ecModel, api) {
      var coordSys = seriesModel.coordinateSystem;
      var seriesId = seriesModel.id;
      var seriesData = seriesModel.getData();
      var areaGroupMap = this.markerGroupMap;
      var polygonGroup = areaGroupMap.get(seriesId) || areaGroupMap.set(seriesId, {
        group: new Group_default()
      });
      this.group.add(polygonGroup.group);
      this.markKeep(polygonGroup);
      var areaData = createList(coordSys, seriesModel, maModel);
      maModel.setData(areaData);
      areaData.each(function(idx) {
        var points = map(dimPermutations, function(dim) {
          return getSingleMarkerEndPoint(areaData, idx, dim, seriesModel, api);
        });
        var xAxisScale = coordSys.getAxis("x").scale;
        var yAxisScale = coordSys.getAxis("y").scale;
        var xAxisExtent = xAxisScale.getExtent();
        var yAxisExtent = yAxisScale.getExtent();
        var xPointExtent = [xAxisScale.parse(areaData.get("x0", idx)), xAxisScale.parse(areaData.get("x1", idx))];
        var yPointExtent = [yAxisScale.parse(areaData.get("y0", idx)), yAxisScale.parse(areaData.get("y1", idx))];
        asc(xPointExtent);
        asc(yPointExtent);
        var overlapped = !(xAxisExtent[0] > xPointExtent[1] || xAxisExtent[1] < xPointExtent[0] || yAxisExtent[0] > yPointExtent[1] || yAxisExtent[1] < yPointExtent[0]);
        var allClipped = !overlapped;
        areaData.setItemLayout(idx, {
          points,
          allClipped
        });
        var style = areaData.getItemModel(idx).getModel("itemStyle").getItemStyle();
        var color = getVisualFromData(seriesData, "color");
        if (!style.fill) {
          style.fill = color;
          if (isString(style.fill)) {
            style.fill = modifyAlpha(style.fill, 0.4);
          }
        }
        if (!style.stroke) {
          style.stroke = color;
        }
        areaData.setItemVisual(idx, "style", style);
      });
      areaData.diff(inner(polygonGroup).data).add(function(idx) {
        var layout = areaData.getItemLayout(idx);
        if (!layout.allClipped) {
          var polygon = new Polygon_default({
            shape: {
              points: layout.points
            }
          });
          areaData.setItemGraphicEl(idx, polygon);
          polygonGroup.group.add(polygon);
        }
      }).update(function(newIdx, oldIdx) {
        var polygon = inner(polygonGroup).data.getItemGraphicEl(oldIdx);
        var layout = areaData.getItemLayout(newIdx);
        if (!layout.allClipped) {
          if (polygon) {
            updateProps(polygon, {
              shape: {
                points: layout.points
              }
            }, maModel, newIdx);
          } else {
            polygon = new Polygon_default({
              shape: {
                points: layout.points
              }
            });
          }
          areaData.setItemGraphicEl(newIdx, polygon);
          polygonGroup.group.add(polygon);
        } else if (polygon) {
          polygonGroup.group.remove(polygon);
        }
      }).remove(function(idx) {
        var polygon = inner(polygonGroup).data.getItemGraphicEl(idx);
        polygonGroup.group.remove(polygon);
      }).execute();
      areaData.eachItemGraphicEl(function(polygon, idx) {
        var itemModel = areaData.getItemModel(idx);
        var style = areaData.getItemVisual(idx, "style");
        polygon.useStyle(areaData.getItemVisual(idx, "style"));
        setLabelStyle(polygon, getLabelStatesModels(itemModel), {
          labelFetcher: maModel,
          labelDataIndex: idx,
          defaultText: areaData.getName(idx) || "",
          inheritColor: isString(style.fill) ? modifyAlpha(style.fill, 1) : "#000"
        });
        setStatesStylesFromModel(polygon, itemModel);
        toggleHoverEmphasis(polygon, null, null, itemModel.get(["emphasis", "disabled"]));
        getECData(polygon).dataModel = maModel;
      });
      inner(polygonGroup).data = areaData;
      polygonGroup.group.silent = maModel.get("silent") || seriesModel.get("silent");
    };
    MarkAreaView2.type = "markArea";
    return MarkAreaView2;
  }(MarkerView_default)
);
function createList(coordSys, seriesModel, maModel) {
  var areaData;
  var dataDims;
  var dims = ["x0", "y0", "x1", "y1"];
  if (coordSys) {
    var coordDimsInfos_1 = map(coordSys && coordSys.dimensions, function(coordDim) {
      var data = seriesModel.getData();
      var info = data.getDimensionInfo(data.mapDimension(coordDim)) || {};
      return extend(extend({}, info), {
        name: coordDim,
        // DON'T use ordinalMeta to parse and collect ordinal.
        ordinalMeta: null
      });
    });
    dataDims = map(dims, function(dim, idx) {
      return {
        name: dim,
        type: coordDimsInfos_1[idx % 2].type
      };
    });
    areaData = new SeriesData_default(dataDims, maModel);
  } else {
    dataDims = [{
      name: "value",
      type: "float"
    }];
    areaData = new SeriesData_default(dataDims, maModel);
  }
  var optData = map(maModel.get("data"), curry(markAreaTransform, seriesModel, coordSys, maModel));
  if (coordSys) {
    optData = filter(optData, curry(markAreaFilter, coordSys));
  }
  var dimValueGetter = coordSys ? function(item, dimName, dataIndex, dimIndex) {
    var rawVal = item.coord[Math.floor(dimIndex / 2)][dimIndex % 2];
    return parseDataValue(rawVal, dataDims[dimIndex]);
  } : function(item, dimName, dataIndex, dimIndex) {
    return parseDataValue(item.value, dataDims[dimIndex]);
  };
  areaData.initData(optData, null, dimValueGetter);
  areaData.hasItemOption = true;
  return areaData;
}
var MarkAreaView_default = MarkAreaView;

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/installMarkArea.js
function install(registers) {
  registers.registerComponentModel(MarkAreaModel_default);
  registers.registerComponentView(MarkAreaView_default);
  registers.registerPreprocessor(function(opt) {
    if (checkMarkerInSeries(opt.series, "markArea")) {
      opt.markArea = opt.markArea || {};
    }
  });
}

export {
  install
};
//# sourceMappingURL=chunk-OORZMOXS.js.map
