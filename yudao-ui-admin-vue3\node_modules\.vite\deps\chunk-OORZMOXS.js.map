{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkAreaModel.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkAreaView.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/installMarkArea.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkAreaModel = /** @class */function (_super) {\n  __extends(MarkAreaModel, _super);\n  function MarkAreaModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaModel.type;\n    return _this;\n  }\n  MarkAreaModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkAreaModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkAreaModel.type = 'markArea';\n  MarkAreaModel.defaultOption = {\n    // zlevel: 0,\n    // PENDING\n    z: 1,\n    tooltip: {\n      trigger: 'item'\n    },\n    // markArea should fixed on the coordinate system\n    animation: false,\n    label: {\n      show: true,\n      position: 'top'\n    },\n    itemStyle: {\n      // color and borderColor default to use color from series\n      // color: 'auto'\n      // borderColor: 'auto'\n      borderWidth: 0\n    },\n    emphasis: {\n      label: {\n        show: true,\n        position: 'top'\n      }\n    }\n  };\n  return MarkAreaModel;\n}(MarkerModel);\nexport default MarkAreaModel;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n// TODO Optimize on polar\nimport * as colorUtil from 'zrender/lib/tool/color.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, setStatesStylesFromModel } from '../../util/states.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport { retrieve, mergeAll, map, curry, filter, extend, isString } from 'zrender/lib/core/util.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport MarkerModel from './MarkerModel.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nvar inner = makeInner();\nvar markAreaTransform = function (seriesModel, coordSys, maModel, item) {\n  // item may be null\n  var item0 = item[0];\n  var item1 = item[1];\n  if (!item0 || !item1) {\n    return;\n  }\n  var lt = markerHelper.dataTransform(seriesModel, item0);\n  var rb = markerHelper.dataTransform(seriesModel, item1);\n  // FIXME make sure lt is less than rb\n  var ltCoord = lt.coord;\n  var rbCoord = rb.coord;\n  ltCoord[0] = retrieve(ltCoord[0], -Infinity);\n  ltCoord[1] = retrieve(ltCoord[1], -Infinity);\n  rbCoord[0] = retrieve(rbCoord[0], Infinity);\n  rbCoord[1] = retrieve(rbCoord[1], Infinity);\n  // Merge option into one\n  var result = mergeAll([{}, lt, rb]);\n  result.coord = [lt.coord, rb.coord];\n  result.x0 = lt.x;\n  result.y0 = lt.y;\n  result.x1 = rb.x;\n  result.y1 = rb.y;\n  return result;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markArea has one dim\nfunction ifMarkAreaHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]);\n}\nfunction markAreaFilter(coordSys, item) {\n  var fromCoord = item.coord[0];\n  var toCoord = item.coord[1];\n  var item0 = {\n    coord: fromCoord,\n    x: item.x0,\n    y: item.y0\n  };\n  var item1 = {\n    coord: toCoord,\n    x: item.x1,\n    y: item.y1\n  };\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    // In case\n    // {\n    //  markArea: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkAreaHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkAreaHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n    // Directly returning true may also do the work,\n    // because markArea will not be shown automatically\n    // when it's not included in coordinate system.\n    // But filtering ahead can avoid keeping rendering markArea\n    // when there are too many of them.\n    return markerHelper.zoneFilter(coordSys, item0, item1);\n  }\n  return markerHelper.dataFilter(coordSys, item0) || markerHelper.dataFilter(coordSys, item1);\n}\n// dims can be ['x0', 'y0'], ['x1', 'y1'], ['x0', 'y1'], ['x1', 'y0']\nfunction getSingleMarkerEndPoint(data, idx, dims, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get(dims[0]), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get(dims[1]), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Consider the case that user input the right-bottom point first\n      // Pick the larger x and y as 'x1' and 'y1'\n      var pointValue0 = data.getValues(['x0', 'y0'], idx);\n      var pointValue1 = data.getValues(['x1', 'y1'], idx);\n      var clampPointValue0 = coordSys.clampData(pointValue0);\n      var clampPointValue1 = coordSys.clampData(pointValue1);\n      var pointValue = [];\n      if (dims[0] === 'x0') {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue1[0] : pointValue0[0];\n      } else {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue0[0] : pointValue1[0];\n      }\n      if (dims[1] === 'y0') {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue1[1] : pointValue0[1];\n      } else {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue0[1] : pointValue1[1];\n      }\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(pointValue, dims, true);\n    } else {\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      var pt = [x, y];\n      coordSys.clampData && coordSys.clampData(pt, pt);\n      point = coordSys.dataToPoint(pt, true);\n    }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      if (isInfinity(x)) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[dims[0] === 'x0' ? 0 : 1]);\n      } else if (isInfinity(y)) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[dims[1] === 'y0' ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  return point;\n}\nexport var dimPermutations = [['x0', 'y0'], ['x1', 'y0'], ['x1', 'y1'], ['x0', 'y1']];\nvar MarkAreaView = /** @class */function (_super) {\n  __extends(MarkAreaView, _super);\n  function MarkAreaView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaView.type;\n    return _this;\n  }\n  MarkAreaView.prototype.updateTransform = function (markAreaModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var maModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markArea');\n      if (maModel) {\n        var areaData_1 = maModel.getData();\n        areaData_1.each(function (idx) {\n          var points = map(dimPermutations, function (dim) {\n            return getSingleMarkerEndPoint(areaData_1, idx, dim, seriesModel, api);\n          });\n          // Layout\n          areaData_1.setItemLayout(idx, points);\n          var el = areaData_1.getItemGraphicEl(idx);\n          el.setShape('points', points);\n        });\n      }\n    }, this);\n  };\n  MarkAreaView.prototype.renderSeries = function (seriesModel, maModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var areaGroupMap = this.markerGroupMap;\n    var polygonGroup = areaGroupMap.get(seriesId) || areaGroupMap.set(seriesId, {\n      group: new graphic.Group()\n    });\n    this.group.add(polygonGroup.group);\n    this.markKeep(polygonGroup);\n    var areaData = createList(coordSys, seriesModel, maModel);\n    // Line data for tooltip and formatter\n    maModel.setData(areaData);\n    // Update visual and layout of line\n    areaData.each(function (idx) {\n      // Layout\n      var points = map(dimPermutations, function (dim) {\n        return getSingleMarkerEndPoint(areaData, idx, dim, seriesModel, api);\n      });\n      var xAxisScale = coordSys.getAxis('x').scale;\n      var yAxisScale = coordSys.getAxis('y').scale;\n      var xAxisExtent = xAxisScale.getExtent();\n      var yAxisExtent = yAxisScale.getExtent();\n      var xPointExtent = [xAxisScale.parse(areaData.get('x0', idx)), xAxisScale.parse(areaData.get('x1', idx))];\n      var yPointExtent = [yAxisScale.parse(areaData.get('y0', idx)), yAxisScale.parse(areaData.get('y1', idx))];\n      numberUtil.asc(xPointExtent);\n      numberUtil.asc(yPointExtent);\n      var overlapped = !(xAxisExtent[0] > xPointExtent[1] || xAxisExtent[1] < xPointExtent[0] || yAxisExtent[0] > yPointExtent[1] || yAxisExtent[1] < yPointExtent[0]);\n      // If none of the area is inside coordSys, allClipped is set to be true\n      // in layout so that label will not be displayed. See #12591\n      var allClipped = !overlapped;\n      areaData.setItemLayout(idx, {\n        points: points,\n        allClipped: allClipped\n      });\n      var style = areaData.getItemModel(idx).getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n        if (isString(style.fill)) {\n          style.fill = colorUtil.modifyAlpha(style.fill, 0.4);\n        }\n      }\n      if (!style.stroke) {\n        style.stroke = color;\n      }\n      // Visual\n      areaData.setItemVisual(idx, 'style', style);\n    });\n    areaData.diff(inner(polygonGroup).data).add(function (idx) {\n      var layout = areaData.getItemLayout(idx);\n      if (!layout.allClipped) {\n        var polygon = new graphic.Polygon({\n          shape: {\n            points: layout.points\n          }\n        });\n        areaData.setItemGraphicEl(idx, polygon);\n        polygonGroup.group.add(polygon);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(oldIdx);\n      var layout = areaData.getItemLayout(newIdx);\n      if (!layout.allClipped) {\n        if (polygon) {\n          graphic.updateProps(polygon, {\n            shape: {\n              points: layout.points\n            }\n          }, maModel, newIdx);\n        } else {\n          polygon = new graphic.Polygon({\n            shape: {\n              points: layout.points\n            }\n          });\n        }\n        areaData.setItemGraphicEl(newIdx, polygon);\n        polygonGroup.group.add(polygon);\n      } else if (polygon) {\n        polygonGroup.group.remove(polygon);\n      }\n    }).remove(function (idx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(idx);\n      polygonGroup.group.remove(polygon);\n    }).execute();\n    areaData.eachItemGraphicEl(function (polygon, idx) {\n      var itemModel = areaData.getItemModel(idx);\n      var style = areaData.getItemVisual(idx, 'style');\n      polygon.useStyle(areaData.getItemVisual(idx, 'style'));\n      setLabelStyle(polygon, getLabelStatesModels(itemModel), {\n        labelFetcher: maModel,\n        labelDataIndex: idx,\n        defaultText: areaData.getName(idx) || '',\n        inheritColor: isString(style.fill) ? colorUtil.modifyAlpha(style.fill, 1) : '#000'\n      });\n      setStatesStylesFromModel(polygon, itemModel);\n      toggleHoverEmphasis(polygon, null, null, itemModel.get(['emphasis', 'disabled']));\n      getECData(polygon).dataModel = maModel;\n    });\n    inner(polygonGroup).data = areaData;\n    polygonGroup.group.silent = maModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkAreaView.type = 'markArea';\n  return MarkAreaView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, maModel) {\n  var areaData;\n  var dataDims;\n  var dims = ['x0', 'y0', 'x1', 'y1'];\n  if (coordSys) {\n    var coordDimsInfos_1 = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var data = seriesModel.getData();\n      var info = data.getDimensionInfo(data.mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n    dataDims = map(dims, function (dim, idx) {\n      return {\n        name: dim,\n        type: coordDimsInfos_1[idx % 2].type\n      };\n    });\n    areaData = new SeriesData(dataDims, maModel);\n  } else {\n    dataDims = [{\n      name: 'value',\n      type: 'float'\n    }];\n    areaData = new SeriesData(dataDims, maModel);\n  }\n  var optData = map(maModel.get('data'), curry(markAreaTransform, seriesModel, coordSys, maModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markAreaFilter, coordSys));\n  }\n  var dimValueGetter = coordSys ? function (item, dimName, dataIndex, dimIndex) {\n    // TODO should convert to ParsedValue?\n    var rawVal = item.coord[Math.floor(dimIndex / 2)][dimIndex % 2];\n    return parseDataValue(rawVal, dataDims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dataDims[dimIndex]);\n  };\n  areaData.initData(optData, null, dimValueGetter);\n  areaData.hasItemOption = true;\n  return areaData;\n}\nexport default MarkAreaView;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkAreaModel from './MarkAreaModel.js';\nimport MarkAreaView from './MarkAreaView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkAreaModel);\n  registers.registerComponentView(MarkAreaView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markArea')) {\n      // Make sure markArea component is enabled\n      opt.markArea = opt.markArea || {};\n    }\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUA,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACvB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,eAAc;AAC3B,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,8BAA8B,SAAU,WAAW,mBAAmB,SAAS;AACrG,aAAO,IAAIA,eAAc,WAAW,mBAAmB,OAAO;AAAA,IAChE;AACA,IAAAA,eAAc,OAAO;AACrB,IAAAA,eAAc,gBAAgB;AAAA;AAAA;AAAA,MAG5B,GAAG;AAAA,MACH,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA;AAAA,MAEA,WAAW;AAAA,MACX,OAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,WAAW;AAAA;AAAA;AAAA;AAAA,QAIT,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,OAAO;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,mBAAW;AAAA;AACb,IAAO,wBAAQ;;;ACxBf,IAAI,QAAQ,UAAU;AACtB,IAAI,oBAAoB,SAAU,aAAa,UAAU,SAAS,MAAM;AAEtE,MAAI,QAAQ,KAAK,CAAC;AAClB,MAAI,QAAQ,KAAK,CAAC;AAClB,MAAI,CAAC,SAAS,CAAC,OAAO;AACpB;AAAA,EACF;AACA,MAAI,KAAkB,cAAc,aAAa,KAAK;AACtD,MAAI,KAAkB,cAAc,aAAa,KAAK;AAEtD,MAAI,UAAU,GAAG;AACjB,MAAI,UAAU,GAAG;AACjB,UAAQ,CAAC,IAAI,SAAS,QAAQ,CAAC,GAAG,SAAS;AAC3C,UAAQ,CAAC,IAAI,SAAS,QAAQ,CAAC,GAAG,SAAS;AAC3C,UAAQ,CAAC,IAAI,SAAS,QAAQ,CAAC,GAAG,QAAQ;AAC1C,UAAQ,CAAC,IAAI,SAAS,QAAQ,CAAC,GAAG,QAAQ;AAE1C,MAAI,SAAS,SAAS,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC;AAClC,SAAO,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClC,SAAO,KAAK,GAAG;AACf,SAAO,KAAK,GAAG;AACf,SAAO,KAAK,GAAG;AACf,SAAO,KAAK,GAAG;AACf,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,GAAG;AACrC;AAEA,SAAS,qBAAqB,UAAU,WAAW,SAAS,UAAU;AACpE,MAAI,gBAAgB,IAAI;AACxB,SAAO,WAAW,UAAU,aAAa,CAAC,KAAK,WAAW,QAAQ,aAAa,CAAC;AAClF;AACA,SAAS,eAAe,UAAU,MAAM;AACtC,MAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,MAAI,UAAU,KAAK,MAAM,CAAC;AAC1B,MAAI,QAAQ;AAAA,IACV,OAAO;AAAA,IACP,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACV;AACA,MAAI,QAAQ;AAAA,IACV,OAAO;AAAA,IACP,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACV;AACA,MAAI,uBAAuB,UAAU,aAAa,GAAG;AAOnD,QAAI,aAAa,YAAY,qBAAqB,GAAG,WAAW,SAAS,QAAQ,KAAK,qBAAqB,GAAG,WAAW,SAAS,QAAQ,IAAI;AAC5I,aAAO;AAAA,IACT;AAMA,WAAoB,WAAW,UAAU,OAAO,KAAK;AAAA,EACvD;AACA,SAAoB,WAAW,UAAU,KAAK,KAAkB,WAAW,UAAU,KAAK;AAC5F;AAEA,SAAS,wBAAwB,MAAM,KAAK,MAAM,aAAa,KAAK;AAClE,MAAI,WAAW,YAAY;AAC3B,MAAI,YAAY,KAAK,aAAa,GAAG;AACrC,MAAI;AACJ,MAAI,MAAiB,aAAa,UAAU,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,SAAS,CAAC;AACxE,MAAI,MAAiB,aAAa,UAAU,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC;AACzE,MAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,YAAQ,CAAC,KAAK,GAAG;AAAA,EACnB,OAAO;AAEL,QAAI,YAAY,mBAAmB;AAGjC,UAAI,cAAc,KAAK,UAAU,CAAC,MAAM,IAAI,GAAG,GAAG;AAClD,UAAI,cAAc,KAAK,UAAU,CAAC,MAAM,IAAI,GAAG,GAAG;AAClD,UAAI,mBAAmB,SAAS,UAAU,WAAW;AACrD,UAAI,mBAAmB,SAAS,UAAU,WAAW;AACrD,UAAI,aAAa,CAAC;AAClB,UAAI,KAAK,CAAC,MAAM,MAAM;AACpB,mBAAW,CAAC,IAAI,iBAAiB,CAAC,IAAI,iBAAiB,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC;AAAA,MAC5F,OAAO;AACL,mBAAW,CAAC,IAAI,iBAAiB,CAAC,IAAI,iBAAiB,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC;AAAA,MAC5F;AACA,UAAI,KAAK,CAAC,MAAM,MAAM;AACpB,mBAAW,CAAC,IAAI,iBAAiB,CAAC,IAAI,iBAAiB,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC;AAAA,MAC5F,OAAO;AACL,mBAAW,CAAC,IAAI,iBAAiB,CAAC,IAAI,iBAAiB,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC;AAAA,MAC5F;AAEA,cAAQ,YAAY,kBAAkB,YAAY,MAAM,IAAI;AAAA,IAC9D,OAAO;AACL,UAAI,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;AAC7B,UAAI,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;AAC7B,UAAI,KAAK,CAAC,GAAG,CAAC;AACd,eAAS,aAAa,SAAS,UAAU,IAAI,EAAE;AAC/C,cAAQ,SAAS,YAAY,IAAI,IAAI;AAAA,IACvC;AACA,QAAI,uBAAuB,UAAU,aAAa,GAAG;AAEnD,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAChC,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAChC,UAAI,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;AAC7B,UAAI,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;AAC7B,UAAI,WAAW,CAAC,GAAG;AACjB,cAAM,CAAC,IAAI,MAAM,cAAc,MAAM,UAAU,EAAE,KAAK,CAAC,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAC5E,WAAW,WAAW,CAAC,GAAG;AACxB,cAAM,CAAC,IAAI,MAAM,cAAc,MAAM,UAAU,EAAE,KAAK,CAAC,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAC5E;AAAA,IACF;AAEA,QAAI,CAAC,MAAM,GAAG,GAAG;AACf,YAAM,CAAC,IAAI;AAAA,IACb;AACA,QAAI,CAAC,MAAM,GAAG,GAAG;AACf,YAAM,CAAC,IAAI;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAI,kBAAkB,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC;AACpF,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACtB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,cAAa;AAC1B,aAAO;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,kBAAkB,SAAU,eAAe,SAAS,KAAK;AAC9E,cAAQ,WAAW,SAAU,aAAa;AACxC,YAAI,UAAU,oBAAY,yBAAyB,aAAa,UAAU;AAC1E,YAAI,SAAS;AACX,cAAI,aAAa,QAAQ,QAAQ;AACjC,qBAAW,KAAK,SAAU,KAAK;AAC7B,gBAAI,SAAS,IAAI,iBAAiB,SAAU,KAAK;AAC/C,qBAAO,wBAAwB,YAAY,KAAK,KAAK,aAAa,GAAG;AAAA,YACvE,CAAC;AAED,uBAAW,cAAc,KAAK,MAAM;AACpC,gBAAI,KAAK,WAAW,iBAAiB,GAAG;AACxC,eAAG,SAAS,UAAU,MAAM;AAAA,UAC9B,CAAC;AAAA,QACH;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,eAAe,SAAU,aAAa,SAAS,SAAS,KAAK;AAClF,UAAI,WAAW,YAAY;AAC3B,UAAI,WAAW,YAAY;AAC3B,UAAI,aAAa,YAAY,QAAQ;AACrC,UAAI,eAAe,KAAK;AACxB,UAAI,eAAe,aAAa,IAAI,QAAQ,KAAK,aAAa,IAAI,UAAU;AAAA,QAC1E,OAAO,IAAY,cAAM;AAAA,MAC3B,CAAC;AACD,WAAK,MAAM,IAAI,aAAa,KAAK;AACjC,WAAK,SAAS,YAAY;AAC1B,UAAI,WAAW,WAAW,UAAU,aAAa,OAAO;AAExD,cAAQ,QAAQ,QAAQ;AAExB,eAAS,KAAK,SAAU,KAAK;AAE3B,YAAI,SAAS,IAAI,iBAAiB,SAAU,KAAK;AAC/C,iBAAO,wBAAwB,UAAU,KAAK,KAAK,aAAa,GAAG;AAAA,QACrE,CAAC;AACD,YAAI,aAAa,SAAS,QAAQ,GAAG,EAAE;AACvC,YAAI,aAAa,SAAS,QAAQ,GAAG,EAAE;AACvC,YAAI,cAAc,WAAW,UAAU;AACvC,YAAI,cAAc,WAAW,UAAU;AACvC,YAAI,eAAe,CAAC,WAAW,MAAM,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,WAAW,MAAM,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC;AACxG,YAAI,eAAe,CAAC,WAAW,MAAM,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,WAAW,MAAM,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC;AACxG,QAAW,IAAI,YAAY;AAC3B,QAAW,IAAI,YAAY;AAC3B,YAAI,aAAa,EAAE,YAAY,CAAC,IAAI,aAAa,CAAC,KAAK,YAAY,CAAC,IAAI,aAAa,CAAC,KAAK,YAAY,CAAC,IAAI,aAAa,CAAC,KAAK,YAAY,CAAC,IAAI,aAAa,CAAC;AAG9J,YAAI,aAAa,CAAC;AAClB,iBAAS,cAAc,KAAK;AAAA,UAC1B;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,QAAQ,SAAS,aAAa,GAAG,EAAE,SAAS,WAAW,EAAE,aAAa;AAC1E,YAAI,QAAQ,kBAAkB,YAAY,OAAO;AACjD,YAAI,CAAC,MAAM,MAAM;AACf,gBAAM,OAAO;AACb,cAAI,SAAS,MAAM,IAAI,GAAG;AACxB,kBAAM,OAAiB,YAAY,MAAM,MAAM,GAAG;AAAA,UACpD;AAAA,QACF;AACA,YAAI,CAAC,MAAM,QAAQ;AACjB,gBAAM,SAAS;AAAA,QACjB;AAEA,iBAAS,cAAc,KAAK,SAAS,KAAK;AAAA,MAC5C,CAAC;AACD,eAAS,KAAK,MAAM,YAAY,EAAE,IAAI,EAAE,IAAI,SAAU,KAAK;AACzD,YAAI,SAAS,SAAS,cAAc,GAAG;AACvC,YAAI,CAAC,OAAO,YAAY;AACtB,cAAI,UAAU,IAAY,gBAAQ;AAAA,YAChC,OAAO;AAAA,cACL,QAAQ,OAAO;AAAA,YACjB;AAAA,UACF,CAAC;AACD,mBAAS,iBAAiB,KAAK,OAAO;AACtC,uBAAa,MAAM,IAAI,OAAO;AAAA,QAChC;AAAA,MACF,CAAC,EAAE,OAAO,SAAU,QAAQ,QAAQ;AAClC,YAAI,UAAU,MAAM,YAAY,EAAE,KAAK,iBAAiB,MAAM;AAC9D,YAAI,SAAS,SAAS,cAAc,MAAM;AAC1C,YAAI,CAAC,OAAO,YAAY;AACtB,cAAI,SAAS;AACX,YAAQ,YAAY,SAAS;AAAA,cAC3B,OAAO;AAAA,gBACL,QAAQ,OAAO;AAAA,cACjB;AAAA,YACF,GAAG,SAAS,MAAM;AAAA,UACpB,OAAO;AACL,sBAAU,IAAY,gBAAQ;AAAA,cAC5B,OAAO;AAAA,gBACL,QAAQ,OAAO;AAAA,cACjB;AAAA,YACF,CAAC;AAAA,UACH;AACA,mBAAS,iBAAiB,QAAQ,OAAO;AACzC,uBAAa,MAAM,IAAI,OAAO;AAAA,QAChC,WAAW,SAAS;AAClB,uBAAa,MAAM,OAAO,OAAO;AAAA,QACnC;AAAA,MACF,CAAC,EAAE,OAAO,SAAU,KAAK;AACvB,YAAI,UAAU,MAAM,YAAY,EAAE,KAAK,iBAAiB,GAAG;AAC3D,qBAAa,MAAM,OAAO,OAAO;AAAA,MACnC,CAAC,EAAE,QAAQ;AACX,eAAS,kBAAkB,SAAU,SAAS,KAAK;AACjD,YAAI,YAAY,SAAS,aAAa,GAAG;AACzC,YAAI,QAAQ,SAAS,cAAc,KAAK,OAAO;AAC/C,gBAAQ,SAAS,SAAS,cAAc,KAAK,OAAO,CAAC;AACrD,sBAAc,SAAS,qBAAqB,SAAS,GAAG;AAAA,UACtD,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,aAAa,SAAS,QAAQ,GAAG,KAAK;AAAA,UACtC,cAAc,SAAS,MAAM,IAAI,IAAc,YAAY,MAAM,MAAM,CAAC,IAAI;AAAA,QAC9E,CAAC;AACD,iCAAyB,SAAS,SAAS;AAC3C,4BAAoB,SAAS,MAAM,MAAM,UAAU,IAAI,CAAC,YAAY,UAAU,CAAC,CAAC;AAChF,kBAAU,OAAO,EAAE,YAAY;AAAA,MACjC,CAAC;AACD,YAAM,YAAY,EAAE,OAAO;AAC3B,mBAAa,MAAM,SAAS,QAAQ,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ;AAAA,IAC/E;AACA,IAAAA,cAAa,OAAO;AACpB,WAAOA;AAAA,EACT,EAAE,kBAAU;AAAA;AACZ,SAAS,WAAW,UAAU,aAAa,SAAS;AAClD,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAClC,MAAI,UAAU;AACZ,QAAI,mBAAmB,IAAI,YAAY,SAAS,YAAY,SAAU,UAAU;AAC9E,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,OAAO,KAAK,iBAAiB,KAAK,aAAa,QAAQ,CAAC,KAAK,CAAC;AAElE,aAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,QAC9B,MAAM;AAAA;AAAA,QAEN,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AACD,eAAW,IAAI,MAAM,SAAU,KAAK,KAAK;AACvC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,iBAAiB,MAAM,CAAC,EAAE;AAAA,MAClC;AAAA,IACF,CAAC;AACD,eAAW,IAAI,mBAAW,UAAU,OAAO;AAAA,EAC7C,OAAO;AACL,eAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AACD,eAAW,IAAI,mBAAW,UAAU,OAAO;AAAA,EAC7C;AACA,MAAI,UAAU,IAAI,QAAQ,IAAI,MAAM,GAAG,MAAM,mBAAmB,aAAa,UAAU,OAAO,CAAC;AAC/F,MAAI,UAAU;AACZ,cAAU,OAAO,SAAS,MAAM,gBAAgB,QAAQ,CAAC;AAAA,EAC3D;AACA,MAAI,iBAAiB,WAAW,SAAU,MAAM,SAAS,WAAW,UAAU;AAE5E,QAAI,SAAS,KAAK,MAAM,KAAK,MAAM,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC;AAC9D,WAAO,eAAe,QAAQ,SAAS,QAAQ,CAAC;AAAA,EAClD,IAAI,SAAU,MAAM,SAAS,WAAW,UAAU;AAChD,WAAO,eAAe,KAAK,OAAO,SAAS,QAAQ,CAAC;AAAA,EACtD;AACA,WAAS,SAAS,SAAS,MAAM,cAAc;AAC/C,WAAS,gBAAgB;AACzB,SAAO;AACT;AACA,IAAO,uBAAQ;;;AC3TR,SAAS,QAAQ,WAAW;AACjC,YAAU,uBAAuB,qBAAa;AAC9C,YAAU,sBAAsB,oBAAY;AAC5C,YAAU,qBAAqB,SAAU,KAAK;AAC5C,QAAI,oBAAoB,IAAI,QAAQ,UAAU,GAAG;AAE/C,UAAI,WAAW,IAAI,YAAY,CAAC;AAAA,IAClC;AAAA,EACF,CAAC;AACH;", "names": ["MarkAreaModel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}