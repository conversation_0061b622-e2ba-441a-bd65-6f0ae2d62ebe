{"version": 3, "sources": ["../../.pnpm/min-dom@4.2.1/node_modules/min-dom/node_modules/min-dash/dist/index.esm.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/style.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/attr.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/classes.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/clear.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/closest.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/node_modules/component-event/index.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/delegate.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/node_modules/domify/index.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/matches.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/query.js", "../../.pnpm/min-dom@4.2.1/node_modules/min-dom/lib/remove.js"], "sourcesContent": ["/**\n * Flatten array, one level deep.\n *\n * @template T\n *\n * @param {T[][] | T[] | null} [arr]\n *\n * @return {T[]}\n */\nfunction flatten(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\n\nconst nativeToString = Object.prototype.toString;\nconst nativeHasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction isUndefined(obj) {\n  return obj === undefined;\n}\n\nfunction isDefined(obj) {\n  return obj !== undefined;\n}\n\nfunction isNil(obj) {\n  return obj == null;\n}\n\nfunction isArray(obj) {\n  return nativeToString.call(obj) === '[object Array]';\n}\n\nfunction isObject(obj) {\n  return nativeToString.call(obj) === '[object Object]';\n}\n\nfunction isNumber(obj) {\n  return nativeToString.call(obj) === '[object Number]';\n}\n\n/**\n * @param {any} obj\n *\n * @return {boolean}\n */\nfunction isFunction(obj) {\n  const tag = nativeToString.call(obj);\n\n  return (\n    tag === '[object Function]' ||\n    tag === '[object AsyncFunction]' ||\n    tag === '[object GeneratorFunction]' ||\n    tag === '[object AsyncGeneratorFunction]' ||\n    tag === '[object Proxy]'\n  );\n}\n\nfunction isString(obj) {\n  return nativeToString.call(obj) === '[object String]';\n}\n\n\n/**\n * Ensure collection is an array.\n *\n * @param {Object} obj\n */\nfunction ensureArray(obj) {\n\n  if (isArray(obj)) {\n    return;\n  }\n\n  throw new Error('must supply array');\n}\n\n/**\n * Return true, if target owns a property with the given key.\n *\n * @param {Object} target\n * @param {String} key\n *\n * @return {Boolean}\n */\nfunction has(target, key) {\n  return nativeHasOwnProperty.call(target, key);\n}\n\n/**\n * @template T\n * @typedef { (\n *   ((e: T) => boolean) |\n *   ((e: T, idx: number) => boolean) |\n *   ((e: T, key: string) => boolean) |\n *   string |\n *   number\n * ) } Matcher\n */\n\n/**\n * @template T\n * @template U\n *\n * @typedef { (\n *   ((e: T) => U) | string | number\n * ) } Extractor\n */\n\n\n/**\n * @template T\n * @typedef { (val: T, key: any) => boolean } MatchFn\n */\n\n/**\n * @template T\n * @typedef { T[] } ArrayCollection\n */\n\n/**\n * @template T\n * @typedef { { [key: string]: T } } StringKeyValueCollection\n */\n\n/**\n * @template T\n * @typedef { { [key: number]: T } } NumberKeyValueCollection\n */\n\n/**\n * @template T\n * @typedef { StringKeyValueCollection<T> | NumberKeyValueCollection<T> } KeyValueCollection\n */\n\n/**\n * @template T\n * @typedef { KeyValueCollection<T> | ArrayCollection<T> } Collection\n */\n\n/**\n * Find element in collection.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param {Matcher<T>} matcher\n *\n * @return {Object}\n */\nfunction find(collection, matcher) {\n\n  const matchFn = toMatcher(matcher);\n\n  let match;\n\n  forEach(collection, function(val, key) {\n    if (matchFn(val, key)) {\n      match = val;\n\n      return false;\n    }\n  });\n\n  return match;\n\n}\n\n\n/**\n * Find element index in collection.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param {Matcher<T>} matcher\n *\n * @return {number}\n */\nfunction findIndex(collection, matcher) {\n\n  const matchFn = toMatcher(matcher);\n\n  let idx = isArray(collection) ? -1 : undefined;\n\n  forEach(collection, function(val, key) {\n    if (matchFn(val, key)) {\n      idx = key;\n\n      return false;\n    }\n  });\n\n  return idx;\n}\n\n\n/**\n * Filter elements in collection.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param {Matcher<T>} matcher\n *\n * @return {T[]} result\n */\nfunction filter(collection, matcher) {\n\n  const matchFn = toMatcher(matcher);\n\n  let result = [];\n\n  forEach(collection, function(val, key) {\n    if (matchFn(val, key)) {\n      result.push(val);\n    }\n  });\n\n  return result;\n}\n\n\n/**\n * Iterate over collection; returning something\n * (non-undefined) will stop iteration.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param { ((item: T, idx: number) => (boolean|void)) | ((item: T, key: string) => (boolean|void)) } iterator\n *\n * @return {T} return result that stopped the iteration\n */\nfunction forEach(collection, iterator) {\n\n  let val,\n      result;\n\n  if (isUndefined(collection)) {\n    return;\n  }\n\n  const convertKey = isArray(collection) ? toNum : identity;\n\n  for (let key in collection) {\n\n    if (has(collection, key)) {\n      val = collection[key];\n\n      result = iterator(val, convertKey(key));\n\n      if (result === false) {\n        return val;\n      }\n    }\n  }\n}\n\n/**\n * Return collection without element.\n *\n * @template T\n * @param {ArrayCollection<T>} arr\n * @param {Matcher<T>} matcher\n *\n * @return {T[]}\n */\nfunction without(arr, matcher) {\n\n  if (isUndefined(arr)) {\n    return [];\n  }\n\n  ensureArray(arr);\n\n  const matchFn = toMatcher(matcher);\n\n  return arr.filter(function(el, idx) {\n    return !matchFn(el, idx);\n  });\n\n}\n\n\n/**\n * Reduce collection, returning a single result.\n *\n * @template T\n * @template V\n *\n * @param {Collection<T>} collection\n * @param {(result: V, entry: T, index: any) => V} iterator\n * @param {V} result\n *\n * @return {V} result returned from last iterator\n */\nfunction reduce(collection, iterator, result) {\n\n  forEach(collection, function(value, idx) {\n    result = iterator(result, value, idx);\n  });\n\n  return result;\n}\n\n\n/**\n * Return true if every element in the collection\n * matches the criteria.\n *\n * @param  {Object|Array} collection\n * @param  {Function} matcher\n *\n * @return {Boolean}\n */\nfunction every(collection, matcher) {\n\n  return !!reduce(collection, function(matches, val, key) {\n    return matches && matcher(val, key);\n  }, true);\n}\n\n\n/**\n * Return true if some elements in the collection\n * match the criteria.\n *\n * @param  {Object|Array} collection\n * @param  {Function} matcher\n *\n * @return {Boolean}\n */\nfunction some(collection, matcher) {\n\n  return !!find(collection, matcher);\n}\n\n\n/**\n * Transform a collection into another collection\n * by piping each member through the given fn.\n *\n * @param  {Object|Array}   collection\n * @param  {Function} fn\n *\n * @return {Array} transformed collection\n */\nfunction map(collection, fn) {\n\n  let result = [];\n\n  forEach(collection, function(val, key) {\n    result.push(fn(val, key));\n  });\n\n  return result;\n}\n\n\n/**\n * Get the collections keys.\n *\n * @param  {Object|Array} collection\n *\n * @return {Array}\n */\nfunction keys(collection) {\n  return collection && Object.keys(collection) || [];\n}\n\n\n/**\n * Shorthand for `keys(o).length`.\n *\n * @param  {Object|Array} collection\n *\n * @return {Number}\n */\nfunction size(collection) {\n  return keys(collection).length;\n}\n\n\n/**\n * Get the values in the collection.\n *\n * @param  {Object|Array} collection\n *\n * @return {Array}\n */\nfunction values(collection) {\n  return map(collection, (val) => val);\n}\n\n\n/**\n * Group collection members by attribute.\n *\n * @param {Object|Array} collection\n * @param {Extractor} extractor\n *\n * @return {Object} map with { attrValue => [ a, b, c ] }\n */\nfunction groupBy(collection, extractor, grouped = {}) {\n\n  extractor = toExtractor(extractor);\n\n  forEach(collection, function(val) {\n    let discriminator = extractor(val) || '_';\n\n    let group = grouped[discriminator];\n\n    if (!group) {\n      group = grouped[discriminator] = [];\n    }\n\n    group.push(val);\n  });\n\n  return grouped;\n}\n\n\nfunction uniqueBy(extractor, ...collections) {\n\n  extractor = toExtractor(extractor);\n\n  let grouped = {};\n\n  forEach(collections, (c) => groupBy(c, extractor, grouped));\n\n  let result = map(grouped, function(val, key) {\n    return val[0];\n  });\n\n  return result;\n}\n\n\nconst unionBy = uniqueBy;\n\n\n\n/**\n * Sort collection by criteria.\n *\n * @template T\n *\n * @param {Collection<T>} collection\n * @param {Extractor<T, number | string>} extractor\n *\n * @return {Array}\n */\nfunction sortBy(collection, extractor) {\n\n  extractor = toExtractor(extractor);\n\n  let sorted = [];\n\n  forEach(collection, function(value, key) {\n    let disc = extractor(value, key);\n\n    let entry = {\n      d: disc,\n      v: value\n    };\n\n    for (var idx = 0; idx < sorted.length; idx++) {\n      let { d } = sorted[idx];\n\n      if (disc < d) {\n        sorted.splice(idx, 0, entry);\n        return;\n      }\n    }\n\n    // not inserted, append (!)\n    sorted.push(entry);\n  });\n\n  return map(sorted, (e) => e.v);\n}\n\n\n/**\n * Create an object pattern matcher.\n *\n * @example\n *\n * ```javascript\n * const matcher = matchPattern({ id: 1 });\n *\n * let element = find(elements, matcher);\n * ```\n *\n * @template T\n *\n * @param {T} pattern\n *\n * @return { (el: any) =>  boolean } matcherFn\n */\nfunction matchPattern(pattern) {\n\n  return function(el) {\n\n    return every(pattern, function(val, key) {\n      return el[key] === val;\n    });\n\n  };\n}\n\n\n/**\n * @param {string | ((e: any) => any) } extractor\n *\n * @return { (e: any) => any }\n */\nfunction toExtractor(extractor) {\n\n  /**\n   * @satisfies { (e: any) => any }\n   */\n  return isFunction(extractor) ? extractor : (e) => {\n\n    // @ts-ignore: just works\n    return e[extractor];\n  };\n}\n\n\n/**\n * @template T\n * @param {Matcher<T>} matcher\n *\n * @return {MatchFn<T>}\n */\nfunction toMatcher(matcher) {\n  return isFunction(matcher) ? matcher : (e) => {\n    return e === matcher;\n  };\n}\n\n\nfunction identity(arg) {\n  return arg;\n}\n\nfunction toNum(arg) {\n  return Number(arg);\n}\n\n/* global setTimeout clearTimeout */\n\n/**\n * @typedef { {\n *   (...args: any[]): any;\n *   flush: () => void;\n *   cancel: () => void;\n * } } DebouncedFunction\n */\n\n/**\n * Debounce fn, calling it only once if the given time\n * elapsed between calls.\n *\n * Lodash-style the function exposes methods to `#clear`\n * and `#flush` to control internal behavior.\n *\n * @param  {Function} fn\n * @param  {Number} timeout\n *\n * @return {DebouncedFunction} debounced function\n */\nfunction debounce(fn, timeout) {\n\n  let timer;\n\n  let lastArgs;\n  let lastThis;\n\n  let lastNow;\n\n  function fire(force) {\n\n    let now = Date.now();\n\n    let scheduledDiff = force ? 0 : (lastNow + timeout) - now;\n\n    if (scheduledDiff > 0) {\n      return schedule(scheduledDiff);\n    }\n\n    fn.apply(lastThis, lastArgs);\n\n    clear();\n  }\n\n  function schedule(timeout) {\n    timer = setTimeout(fire, timeout);\n  }\n\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n    }\n\n    timer = lastNow = lastArgs = lastThis = undefined;\n  }\n\n  function flush() {\n    if (timer) {\n      fire(true);\n    }\n\n    clear();\n  }\n\n  /**\n   * @type { DebouncedFunction }\n   */\n  function callback(...args) {\n    lastNow = Date.now();\n\n    lastArgs = args;\n    lastThis = this;\n\n    // ensure an execution is scheduled\n    if (!timer) {\n      schedule(timeout);\n    }\n  }\n\n  callback.flush = flush;\n  callback.cancel = clear;\n\n  return callback;\n}\n\n/**\n * Throttle fn, calling at most once\n * in the given interval.\n *\n * @param  {Function} fn\n * @param  {Number} interval\n *\n * @return {Function} throttled function\n */\nfunction throttle(fn, interval) {\n  let throttling = false;\n\n  return function(...args) {\n\n    if (throttling) {\n      return;\n    }\n\n    fn(...args);\n    throttling = true;\n\n    setTimeout(() => {\n      throttling = false;\n    }, interval);\n  };\n}\n\n/**\n * Bind function against target <this>.\n *\n * @param  {Function} fn\n * @param  {Object}   target\n *\n * @return {Function} bound function\n */\nfunction bind(fn, target) {\n  return fn.bind(target);\n}\n\n/**\n * Convenience wrapper for `Object.assign`.\n *\n * @param {Object} target\n * @param {...Object} others\n *\n * @return {Object} the target\n */\nfunction assign(target, ...others) {\n  return Object.assign(target, ...others);\n}\n\n/**\n * Sets a nested property of a given object to the specified value.\n *\n * This mutates the object and returns it.\n *\n * @template T\n *\n * @param {T} target The target of the set operation.\n * @param {(string|number)[]} path The path to the nested value.\n * @param {any} value The value to set.\n *\n * @return {T}\n */\nfunction set(target, path, value) {\n\n  let currentTarget = target;\n\n  forEach(path, function(key, idx) {\n\n    if (typeof key !== 'number' && typeof key !== 'string') {\n      throw new Error('illegal key type: ' + typeof key + '. Key should be of type number or string.');\n    }\n\n    if (key === 'constructor') {\n      throw new Error('illegal key: constructor');\n    }\n\n    if (key === '__proto__') {\n      throw new Error('illegal key: __proto__');\n    }\n\n    let nextKey = path[idx + 1];\n    let nextTarget = currentTarget[key];\n\n    if (isDefined(nextKey) && isNil(nextTarget)) {\n      nextTarget = currentTarget[key] = isNaN(+nextKey) ? {} : [];\n    }\n\n    if (isUndefined(nextKey)) {\n      if (isUndefined(value)) {\n        delete currentTarget[key];\n      } else {\n        currentTarget[key] = value;\n      }\n    } else {\n      currentTarget = nextTarget;\n    }\n  });\n\n  return target;\n}\n\n\n/**\n * Gets a nested property of a given object.\n *\n * @param {Object} target The target of the get operation.\n * @param {(string|number)[]} path The path to the nested value.\n * @param {any} [defaultValue] The value to return if no value exists.\n *\n * @return {any}\n */\nfunction get(target, path, defaultValue) {\n\n  let currentTarget = target;\n\n  forEach(path, function(key) {\n\n    // accessing nil property yields <undefined>\n    if (isNil(currentTarget)) {\n      currentTarget = undefined;\n\n      return false;\n    }\n\n    currentTarget = currentTarget[key];\n  });\n\n  return isUndefined(currentTarget) ? defaultValue : currentTarget;\n}\n\n/**\n * Pick properties from the given target.\n *\n * @template T\n * @template {any[]} V\n *\n * @param {T} target\n * @param {V} properties\n *\n * @return Pick<T, V>\n */\nfunction pick(target, properties) {\n\n  let result = {};\n\n  let obj = Object(target);\n\n  forEach(properties, function(prop) {\n\n    if (prop in obj) {\n      result[prop] = target[prop];\n    }\n  });\n\n  return result;\n}\n\n/**\n * Pick all target properties, excluding the given ones.\n *\n * @template T\n * @template {any[]} V\n *\n * @param {T} target\n * @param {V} properties\n *\n * @return {Omit<T, V>} target\n */\nfunction omit(target, properties) {\n\n  let result = {};\n\n  let obj = Object(target);\n\n  forEach(obj, function(prop, key) {\n\n    if (properties.indexOf(key) === -1) {\n      result[key] = prop;\n    }\n  });\n\n  return result;\n}\n\n/**\n * Recursively merge `...sources` into given target.\n *\n * Does support merging objects; does not support merging arrays.\n *\n * @param {Object} target\n * @param {...Object} sources\n *\n * @return {Object} the target\n */\nfunction merge(target, ...sources) {\n\n  if (!sources.length) {\n    return target;\n  }\n\n  forEach(sources, function(source) {\n\n    // skip non-obj sources, i.e. null\n    if (!source || !isObject(source)) {\n      return;\n    }\n\n    forEach(source, function(sourceVal, key) {\n\n      if (key === '__proto__') {\n        return;\n      }\n\n      let targetVal = target[key];\n\n      if (isObject(sourceVal)) {\n\n        if (!isObject(targetVal)) {\n\n          // override target[key] with object\n          targetVal = {};\n        }\n\n        target[key] = merge(targetVal, sourceVal);\n      } else {\n        target[key] = sourceVal;\n      }\n\n    });\n  });\n\n  return target;\n}\n\nexport { assign, bind, debounce, ensureArray, every, filter, find, findIndex, flatten, forEach, get, groupBy, has, isArray, isDefined, isFunction, isNil, isNumber, isObject, isString, isUndefined, keys, map, matchPattern, merge, omit, pick, reduce, set, size, some, sortBy, throttle, unionBy, uniqueBy, values, without };\n", "import { forEach } from 'min-dash';\n\n/**\n * Assigns style attributes in a style-src compliant way.\n *\n * @param {Element} element\n * @param {...Object} styleSources\n *\n * @return {Element} the element\n */\nexport function assign(element, ...styleSources) {\n  const target = element.style;\n\n  forEach(styleSources, function(style) {\n    if (!style) {\n      return;\n    }\n\n    forEach(style, function(value, key) {\n      target[key] = value;\n    });\n  });\n\n  return element;\n}\n", "/**\n * Set attribute `name` to `val`, or get attr `name`.\n *\n * @param {Element} el\n * @param {String} name\n * @param {String} [val]\n * @api public\n */\nexport default function attr(el, name, val) {\n\n  // get\n  if (arguments.length == 2) {\n    return el.getAttribute(name);\n  }\n\n  // remove\n  if (val === null) {\n    return el.removeAttribute(name);\n  }\n\n  // set\n  el.setAttribute(name, val);\n\n  return el;\n}", "/**\n * Taken from https://github.com/component/classes\n *\n * Without the component bits.\n */\n\n/**\n * toString reference.\n */\n\nconst toString = Object.prototype.toString;\n\n/**\n * Wrap `el` in a `ClassList`.\n *\n * @param {Element} el\n * @return {ClassList}\n * @api public\n */\n\nexport default function classes(el) {\n  return new ClassList(el);\n}\n\n/**\n * Initialize a new ClassList for `el`.\n *\n * @param {Element} el\n * @api private\n */\n\nfunction ClassList(el) {\n  if (!el || !el.nodeType) {\n    throw new Error('A DOM element reference is required');\n  }\n  this.el = el;\n  this.list = el.classList;\n}\n\n/**\n * Add class `name` if not already present.\n *\n * @param {String} name\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.add = function(name) {\n  this.list.add(name);\n  return this;\n};\n\n/**\n * Remove class `name` when present, or\n * pass a regular expression to remove\n * any which match.\n *\n * @param {String|RegExp} name\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.remove = function(name) {\n  if ('[object RegExp]' == toString.call(name)) {\n    return this.removeMatching(name);\n  }\n\n  this.list.remove(name);\n  return this;\n};\n\n/**\n * Remove all classes matching `re`.\n *\n * @param {RegExp} re\n * @return {ClassList}\n * @api private\n */\n\nClassList.prototype.removeMatching = function(re) {\n  const arr = this.array();\n  for (let i = 0; i < arr.length; i++) {\n    if (re.test(arr[i])) {\n      this.remove(arr[i]);\n    }\n  }\n  return this;\n};\n\n/**\n * Toggle class `name`, can force state via `force`.\n *\n * For browsers that support classList, but do not support `force` yet,\n * the mistake will be detected and corrected.\n *\n * @param {String} name\n * @param {Boolean} force\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.toggle = function(name, force) {\n  if ('undefined' !== typeof force) {\n    if (force !== this.list.toggle(name, force)) {\n      this.list.toggle(name); // toggle again to correct\n    }\n  } else {\n    this.list.toggle(name);\n  }\n  return this;\n};\n\n/**\n * Return an array of classes.\n *\n * @return {Array}\n * @api public\n */\n\nClassList.prototype.array = function() {\n  return Array.from(this.list);\n};\n\n/**\n * Check if class `name` is present.\n *\n * @param {String} name\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.has =\nClassList.prototype.contains = function(name) {\n  return this.list.contains(name);\n};\n", "/**\n * Clear utility\n */\n\n/**\n * Removes all children from the given element\n *\n * @param {Element} element\n *\n * @return {Element} the element (for chaining)\n */\nexport default function clear(element) {\n  var child;\n\n  while ((child = element.firstChild)) {\n    element.removeChild(child);\n  }\n\n  return element;\n}", "/**\n * Closest\n *\n * @param {Element} el\n * @param {string} selector\n * @param {boolean} checkYourSelf (optional)\n */\nexport default function(element, selector, checkYourSelf) {\n  var actualElement = checkYourSelf ? element : element.parentNode;\n\n  return actualElement && typeof actualElement.closest === 'function' && actualElement.closest(selector) || null;\n}\n", "var bind, unbind, prefix;\n\nfunction detect () {\n  bind = window.addEventListener ? 'addEventListener' : 'attachEvent';\n  unbind = window.removeEventListener ? 'removeEventListener' : 'detachEvent';\n  prefix = bind !== 'addEventListener' ? 'on' : '';\n}\n\n/**\n * Bind `el` event `type` to `fn`.\n *\n * @param {Element} el\n * @param {String} type\n * @param {Function} fn\n * @param {Boolean} capture\n * @return {Function}\n * @api public\n */\n\nexports.bind = function(el, type, fn, capture){\n  if (!bind) detect();\n  el[bind](prefix + type, fn, capture || false);\n  return fn;\n};\n\n/**\n * Unbind `el` event `type`'s callback `fn`.\n *\n * @param {Element} el\n * @param {String} type\n * @param {Function} fn\n * @param {Boolean} capture\n * @return {Function}\n * @api public\n */\n\nexports.unbind = function(el, type, fn, capture){\n  if (!unbind) detect();\n  el[unbind](prefix + type, fn, capture || false);\n  return fn;\n};\n", "/**\n * Module dependencies.\n */\n\nimport closest from './closest';\nimport event from './event';\n\n/**\n * Delegate event `type` to `selector`\n * and invoke `fn(e)`. A callback function\n * is returned which may be passed to `.unbind()`.\n *\n * @param {Element} el\n * @param {String} selector\n * @param {String} type\n * @param {Function} fn\n * @param {Boolean} capture\n * @return {Function}\n * @api public\n */\n\n// Some events don't bubble, so we want to bind to the capture phase instead\n// when delegating.\nvar forceCaptureEvents = [ 'focus', 'blur' ];\n\nfunction bind(el, selector, type, fn, capture) {\n  if (forceCaptureEvents.indexOf(type) !== -1) {\n    capture = true;\n  }\n\n  return event.bind(el, type, function(e) {\n    var target = e.target || e.srcElement;\n    e.delegateTarget = closest(target, selector, true, el);\n    if (e.delegateTarget) {\n      fn.call(el, e);\n    }\n  }, capture);\n}\n\n/**\n * Unbind event `type`'s callback `fn`.\n *\n * @param {Element} el\n * @param {String} type\n * @param {Function} fn\n * @param {Boolean} capture\n * @api public\n */\nfunction unbind(el, type, fn, capture) {\n  if (forceCaptureEvents.indexOf(type) !== -1) {\n    capture = true;\n  }\n\n  return event.unbind(el, type, fn, capture);\n}\n\nexport default {\n  bind,\n  unbind\n};", "\n/**\n * Expose `parse`.\n */\n\nmodule.exports = parse;\n\n/**\n * Tests for browser support.\n */\n\nvar innerHTMLBug = false;\nvar bugTestDiv;\nif (typeof document !== 'undefined') {\n  bugTestDiv = document.createElement('div');\n  // Setup\n  bugTestDiv.innerHTML = '  <link/><table></table><a href=\"/a\">a</a><input type=\"checkbox\"/>';\n  // Make sure that link elements get serialized correctly by innerHTML\n  // This requires a wrapper element in IE\n  innerHTMLBug = !bugTestDiv.getElementsByTagName('link').length;\n  bugTestDiv = undefined;\n}\n\n/**\n * Wrap map from jquery.\n */\n\nvar map = {\n  legend: [1, '<fieldset>', '</fieldset>'],\n  tr: [2, '<table><tbody>', '</tbody></table>'],\n  col: [2, '<table><tbody></tbody><colgroup>', '</colgroup></table>'],\n  // for script/link/style tags to work in IE6-8, you have to wrap\n  // in a div with a non-whitespace character in front, ha!\n  _default: innerHTMLBug ? [1, 'X<div>', '</div>'] : [0, '', '']\n};\n\nmap.td =\nmap.th = [3, '<table><tbody><tr>', '</tr></tbody></table>'];\n\nmap.option =\nmap.optgroup = [1, '<select multiple=\"multiple\">', '</select>'];\n\nmap.thead =\nmap.tbody =\nmap.colgroup =\nmap.caption =\nmap.tfoot = [1, '<table>', '</table>'];\n\nmap.polyline =\nmap.ellipse =\nmap.polygon =\nmap.circle =\nmap.text =\nmap.line =\nmap.path =\nmap.rect =\nmap.g = [1, '<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">','</svg>'];\n\n/**\n * Parse `html` and return a DOM Node instance, which could be a TextNode,\n * HTML DOM Node of some kind (<div> for example), or a DocumentFragment\n * instance, depending on the contents of the `html` string.\n *\n * @param {String} html - HTML string to \"domify\"\n * @param {Document} doc - The `document` instance to create the Node for\n * @return {DOMNode} the TextNode, DOM Node, or DocumentFragment instance\n * @api private\n */\n\nfunction parse(html, doc) {\n  if ('string' != typeof html) throw new TypeError('String expected');\n\n  // default to the global `document` object\n  if (!doc) doc = document;\n\n  // tag name\n  var m = /<([\\w:]+)/.exec(html);\n  if (!m) return doc.createTextNode(html);\n\n  html = html.replace(/^\\s+|\\s+$/g, ''); // Remove leading/trailing whitespace\n\n  var tag = m[1];\n\n  // body support\n  if (tag == 'body') {\n    var el = doc.createElement('html');\n    el.innerHTML = html;\n    return el.removeChild(el.lastChild);\n  }\n\n  // wrap map\n  var wrap = Object.prototype.hasOwnProperty.call(map, tag) ? map[tag] : map._default;\n  var depth = wrap[0];\n  var prefix = wrap[1];\n  var suffix = wrap[2];\n  var el = doc.createElement('div');\n  el.innerHTML = prefix + html + suffix;\n  while (depth--) el = el.lastChild;\n\n  // one element\n  if (el.firstChild == el.lastChild) {\n    return el.removeChild(el.firstChild);\n  }\n\n  // several elements\n  var fragment = doc.createDocumentFragment();\n  while (el.firstChild) {\n    fragment.appendChild(el.removeChild(el.firstChild));\n  }\n\n  return fragment;\n}\n", "/**\n * @param { HTMLElement } element\n * @param { String } selector\n *\n * @return { boolean }\n */\nexport default function matches(element, selector) {\n  return element && typeof element.matches === 'function' && element.matches(selector) || false;\n}", "export default function query(selector, el) {\n  el = el || document;\n\n  return el.querySelector(selector);\n}\n\nexport function all(selector, el) {\n  el = el || document;\n\n  return el.querySelectorAll(selector);\n}", "export default function remove(el) {\n  el.parentNode && el.parentNode.removeChild(el);\n}"], "mappings": ";;;;;;;;;;;;;;;;;AAaA,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,uBAAuB,OAAO,UAAU;AAE9C,SAAS,YAAY,KAAK;AACxB,SAAO,QAAQ;AACjB;AAUA,SAAS,QAAQ,KAAK;AACpB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AAsDA,SAAS,IAAI,QAAQ,KAAK;AACxB,SAAO,qBAAqB,KAAK,QAAQ,GAAG;AAC9C;AA+IA,SAAS,QAAQ,YAAY,UAAU;AAErC,MAAI,KACA;AAEJ,MAAI,YAAY,UAAU,GAAG;AAC3B;EACJ;AAEE,QAAM,aAAa,QAAQ,UAAU,IAAI,QAAQ;AAEjD,WAAS,OAAO,YAAY;AAE1B,QAAI,IAAI,YAAY,GAAG,GAAG;AACxB,YAAM,WAAW,GAAG;AAEpB,eAAS,SAAS,KAAK,WAAW,GAAG,CAAC;AAEtC,UAAI,WAAW,OAAO;AACpB,eAAO;MACf;IACA;EACA;AACA;AAgSA,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AAEA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO,GAAG;AACnB;ACxhBO,SAAS,OAAO,YAAY,cAAc;AAC/C,QAAM,SAAS,QAAQ;AAEvB,UAAQ,cAAc,SAAS,OAAO;AACpC,QAAI,CAAC,OAAO;AACV;IACN;AAEI,YAAQ,OAAO,SAAS,OAAO,KAAK;AAClC,aAAO,GAAG,IAAI;IACpB,CAAK;EACL,CAAG;AAED,SAAO;AACT;AChBe,SAAS,KAAK,IAAI,MAAM,KAAK;AAG1C,MAAI,UAAU,UAAU,GAAG;AACzB,WAAO,GAAG,aAAa,IAAI;EAC/B;AAGE,MAAI,QAAQ,MAAM;AAChB,WAAO,GAAG,gBAAgB,IAAI;EAClC;AAGE,KAAG,aAAa,MAAM,GAAG;AAEzB,SAAO;AACT;ACdA,IAAM,WAAW,OAAO,UAAU;AAUnB,SAAS,QAAQ,IAAI;AAClC,SAAO,IAAI,UAAU,EAAE;AACzB;AASA,SAAS,UAAU,IAAI;AACrB,MAAI,CAAC,MAAM,CAAC,GAAG,UAAU;AACvB,UAAM,IAAI,MAAM,qCAAqC;EACzD;AACE,OAAK,KAAK;AACV,OAAK,OAAO,GAAG;AACjB;AAUA,UAAU,UAAU,MAAM,SAAS,MAAM;AACvC,OAAK,KAAK,IAAI,IAAI;AAClB,SAAO;AACT;AAYA,UAAU,UAAU,SAAS,SAAS,MAAM;AAC1C,MAAI,qBAAqB,SAAS,KAAK,IAAI,GAAG;AAC5C,WAAO,KAAK,eAAe,IAAI;EACnC;AAEE,OAAK,KAAK,OAAO,IAAI;AACrB,SAAO;AACT;AAUA,UAAU,UAAU,iBAAiB,SAAS,IAAI;AAChD,QAAM,MAAM,KAAK,MAAK;AACtB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG;AACnB,WAAK,OAAO,IAAI,CAAC,CAAC;IACxB;EACA;AACE,SAAO;AACT;AAcA,UAAU,UAAU,SAAS,SAAS,MAAM,OAAO;AACjD,MAAI,gBAAgB,OAAO,OAAO;AAChC,QAAI,UAAU,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG;AAC3C,WAAK,KAAK,OAAO,IAAI;IAC3B;EACA,OAAS;AACL,SAAK,KAAK,OAAO,IAAI;EACzB;AACE,SAAO;AACT;AASA,UAAU,UAAU,QAAQ,WAAW;AACrC,SAAO,MAAM,KAAK,KAAK,IAAI;AAC7B;AAUA,UAAU,UAAU,MACpB,UAAU,UAAU,WAAW,SAAS,MAAM;AAC5C,SAAO,KAAK,KAAK,SAAS,IAAI;AAChC;AC3He,SAAS,MAAM,SAAS;AACrC,MAAI;AAEJ,SAAQ,QAAQ,QAAQ,YAAa;AACnC,YAAQ,YAAY,KAAK;EAC7B;AAEE,SAAO;AACT;ACZe,SAAA,QAAS,SAAS,UAAU,eAAe;AACxD,MAAI,gBAAgB,gBAAgB,UAAU,QAAQ;AAEtD,SAAO,iBAAiB,OAAO,cAAc,YAAY,cAAc,cAAc,QAAQ,QAAQ,KAAK;AAC5G;;ACXA,IAAIA;AAAJ,IAAUC;AAAV,IAAkB;AAElB,SAAS,SAAU;AACjBD,WAAO,OAAO,mBAAmB,qBAAqB;AACtDC,aAAS,OAAO,sBAAsB,wBAAwB;AAC9D,WAASD,WAAS,qBAAqB,OAAO;AAChD;AAaA,IAAY,SAAA,eAAA,OAAG,SAAS,IAAI,MAAM,IAAI,SAAQ;AAC5C,MAAI,CAACA;AAAM,WAAM;AACjB,KAAGA,MAAI,EAAE,SAAS,MAAM,IAAI,WAAW,KAAK;AAC5C,SAAO;AACT;AAaA,IAAc,WAAA,eAAA,SAAG,SAAS,IAAI,MAAM,IAAI,SAAQ;AAC9C,MAAI,CAACC;AAAQ,WAAM;AACnB,KAAGA,QAAM,EAAE,SAAS,MAAM,IAAI,WAAW,KAAK;AAC9C,SAAO;AACT;;;;;;;ACjBA,IAAI,qBAAqB,CAAE,SAAS,MAAM;AAE1C,SAAS,KAAK,IAAI,UAAU,MAAM,IAAI,SAAS;AAC7C,MAAI,mBAAmB,QAAQ,IAAI,MAAM,IAAI;AAC3C,cAAU;EACd;AAEE,SAAO,MAAM,KAAK,IAAI,MAAM,SAAS,GAAG;AACtC,QAAI,SAAS,EAAE,UAAU,EAAE;AAC3B,MAAE,iBAAiB,QAAQ,QAAQ,UAAU,IAAQ;AACrD,QAAI,EAAE,gBAAgB;AACpB,SAAG,KAAK,IAAI,CAAC;IACnB;EACA,GAAK,OAAO;AACZ;AAWA,SAAS,OAAO,IAAI,MAAM,IAAI,SAAS;AACrC,MAAI,mBAAmB,QAAQ,IAAI,MAAM,IAAI;AAC3C,cAAU;EACd;AAEE,SAAO,MAAM,OAAO,IAAI,MAAM,IAAI,OAAO;AAC3C;AAEA,IAAA,WAAe;EACb;EACA;AACF;ICtDA,SAAiB;AAMjB,IAAI,eAAe;AACnB,IAAI;AACJ,IAAI,OAAO,aAAa,aAAa;AACnC,eAAa,SAAS,cAAc,KAAK;AAEzC,aAAW,YAAY;AAGvB,iBAAe,CAAC,WAAW,qBAAqB,MAAM,EAAE;AACxD,eAAa;AACf;AAMA,IAAI,MAAM;EACR,QAAQ,CAAC,GAAG,cAAc,aAAa;EACvC,IAAI,CAAC,GAAG,kBAAkB,kBAAkB;EAC5C,KAAK,CAAC,GAAG,oCAAoC,qBAAqB;;;EAGlE,UAAU,eAAe,CAAC,GAAG,UAAU,QAAQ,IAAI,CAAC,GAAG,IAAI,EAAE;AAC/D;AAEA,IAAI,KACJ,IAAI,KAAK,CAAC,GAAG,sBAAsB,uBAAuB;AAE1D,IAAI,SACJ,IAAI,WAAW,CAAC,GAAG,gCAAgC,WAAW;AAE9D,IAAI,QACJ,IAAI,QACJ,IAAI,WACJ,IAAI,UACJ,IAAI,QAAQ,CAAC,GAAG,WAAW,UAAU;AAErC,IAAI,WACJ,IAAI,UACJ,IAAI,UACJ,IAAI,SACJ,IAAI,OACJ,IAAI,OACJ,IAAI,OACJ,IAAI,OACJ,IAAI,IAAI,CAAC,GAAG,0DAAyD,QAAQ;AAa7E,SAAS,MAAM,MAAM,KAAK;AACxB,MAAI,YAAY,OAAO;AAAM,UAAM,IAAI,UAAU,iBAAiB;AAGlE,MAAI,CAAC;AAAK,UAAM;AAGhB,MAAI,IAAI,YAAY,KAAK,IAAI;AAC7B,MAAI,CAAC;AAAG,WAAO,IAAI,eAAe,IAAI;AAEtC,SAAO,KAAK,QAAQ,cAAc,EAAE;AAEpC,MAAI,MAAM,EAAE,CAAC;AAGb,MAAI,OAAO,QAAQ;AACjB,QAAI,KAAK,IAAI,cAAc,MAAM;AACjC,OAAG,YAAY;AACf,WAAO,GAAG,YAAY,GAAG,SAAS;EACtC;AAGE,MAAI,OAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI;AAC3E,MAAI,QAAQ,KAAK,CAAC;AAClB,MAAIC,UAAS,KAAK,CAAC;AACnB,MAAI,SAAS,KAAK,CAAC;AACnB,MAAI,KAAK,IAAI,cAAc,KAAK;AAChC,KAAG,YAAYA,UAAS,OAAO;AAC/B,SAAO;AAAS,SAAK,GAAG;AAGxB,MAAI,GAAG,cAAc,GAAG,WAAW;AACjC,WAAO,GAAG,YAAY,GAAG,UAAU;EACvC;AAGE,MAAI,WAAW,IAAI,uBAAsB;AACzC,SAAO,GAAG,YAAY;AACpB,aAAS,YAAY,GAAG,YAAY,GAAG,UAAU,CAAC;EACtD;AAEE,SAAO;AACT;;ACzGe,SAAS,QAAQ,SAAS,UAAU;AACjD,SAAO,WAAW,OAAO,QAAQ,YAAY,cAAc,QAAQ,QAAQ,QAAQ,KAAK;AAC1F;ACRe,SAAS,MAAM,UAAU,IAAI;AAC1C,OAAK,MAAM;AAEX,SAAO,GAAG,cAAc,QAAQ;AAClC;AAEO,SAAS,IAAI,UAAU,IAAI;AAChC,OAAK,MAAM;AAEX,SAAO,GAAG,iBAAiB,QAAQ;AACrC;ACVe,SAAS,OAAO,IAAI;AACjC,KAAG,cAAc,GAAG,WAAW,YAAY,EAAE;AAC/C;", "names": ["bind", "unbind", "prefix"]}