import {
  MarkerModel_default,
  <PERSON><PERSON><PERSON>iew_default,
  checkMarkerInSeries,
  createMarkerDimV<PERSON>ueGetter,
  dataFilter,
  dataTransform
} from "./chunk-ZR4V7UZP.js";
import {
  SymbolDraw_default
} from "./chunk-6MK55XBE.js";
import {
  SeriesData_default,
  getVisualFromData
} from "./chunk-H732WCN4.js";
import {
  getECData,
  parsePercent
} from "./chunk-Q47K3BNQ.js";
import {
  __extends,
  curry,
  extend,
  filter,
  isFunction,
  map
} from "./chunk-MUBQFVAI.js";

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkPointModel.js
var MarkPointModel = (
  /** @class */
  function(_super) {
    __extends(MarkPointModel2, _super);
    function MarkPointModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkPointModel2.type;
      return _this;
    }
    MarkPointModel2.prototype.createMarkerModelFromSeries = function(markerOpt, masterMarkerModel, ecModel) {
      return new MarkPointModel2(markerOpt, masterMarkerModel, ecModel);
    };
    MarkPointModel2.type = "markPoint";
    MarkPointModel2.defaultOption = {
      // zlevel: 0,
      z: 5,
      symbol: "pin",
      symbolSize: 50,
      // symbolRotate: 0,
      // symbolOffset: [0, 0]
      tooltip: {
        trigger: "item"
      },
      label: {
        show: true,
        position: "inside"
      },
      itemStyle: {
        borderWidth: 2
      },
      emphasis: {
        label: {
          show: true
        }
      }
    };
    return MarkPointModel2;
  }(MarkerModel_default)
);
var MarkPointModel_default = MarkPointModel;

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkPointView.js
function updateMarkerLayout(mpData, seriesModel, api) {
  var coordSys = seriesModel.coordinateSystem;
  mpData.each(function(idx) {
    var itemModel = mpData.getItemModel(idx);
    var point;
    var xPx = parsePercent(itemModel.get("x"), api.getWidth());
    var yPx = parsePercent(itemModel.get("y"), api.getHeight());
    if (!isNaN(xPx) && !isNaN(yPx)) {
      point = [xPx, yPx];
    } else if (seriesModel.getMarkerPosition) {
      point = seriesModel.getMarkerPosition(mpData.getValues(mpData.dimensions, idx));
    } else if (coordSys) {
      var x = mpData.get(coordSys.dimensions[0], idx);
      var y = mpData.get(coordSys.dimensions[1], idx);
      point = coordSys.dataToPoint([x, y]);
    }
    if (!isNaN(xPx)) {
      point[0] = xPx;
    }
    if (!isNaN(yPx)) {
      point[1] = yPx;
    }
    mpData.setItemLayout(idx, point);
  });
}
var MarkPointView = (
  /** @class */
  function(_super) {
    __extends(MarkPointView2, _super);
    function MarkPointView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkPointView2.type;
      return _this;
    }
    MarkPointView2.prototype.updateTransform = function(markPointModel, ecModel, api) {
      ecModel.eachSeries(function(seriesModel) {
        var mpModel = MarkerModel_default.getMarkerModelFromSeries(seriesModel, "markPoint");
        if (mpModel) {
          updateMarkerLayout(mpModel.getData(), seriesModel, api);
          this.markerGroupMap.get(seriesModel.id).updateLayout();
        }
      }, this);
    };
    MarkPointView2.prototype.renderSeries = function(seriesModel, mpModel, ecModel, api) {
      var coordSys = seriesModel.coordinateSystem;
      var seriesId = seriesModel.id;
      var seriesData = seriesModel.getData();
      var symbolDrawMap = this.markerGroupMap;
      var symbolDraw = symbolDrawMap.get(seriesId) || symbolDrawMap.set(seriesId, new SymbolDraw_default());
      var mpData = createData(coordSys, seriesModel, mpModel);
      mpModel.setData(mpData);
      updateMarkerLayout(mpModel.getData(), seriesModel, api);
      mpData.each(function(idx) {
        var itemModel = mpData.getItemModel(idx);
        var symbol = itemModel.getShallow("symbol");
        var symbolSize = itemModel.getShallow("symbolSize");
        var symbolRotate = itemModel.getShallow("symbolRotate");
        var symbolOffset = itemModel.getShallow("symbolOffset");
        var symbolKeepAspect = itemModel.getShallow("symbolKeepAspect");
        if (isFunction(symbol) || isFunction(symbolSize) || isFunction(symbolRotate) || isFunction(symbolOffset)) {
          var rawIdx = mpModel.getRawValue(idx);
          var dataParams = mpModel.getDataParams(idx);
          if (isFunction(symbol)) {
            symbol = symbol(rawIdx, dataParams);
          }
          if (isFunction(symbolSize)) {
            symbolSize = symbolSize(rawIdx, dataParams);
          }
          if (isFunction(symbolRotate)) {
            symbolRotate = symbolRotate(rawIdx, dataParams);
          }
          if (isFunction(symbolOffset)) {
            symbolOffset = symbolOffset(rawIdx, dataParams);
          }
        }
        var style = itemModel.getModel("itemStyle").getItemStyle();
        var color = getVisualFromData(seriesData, "color");
        if (!style.fill) {
          style.fill = color;
        }
        mpData.setItemVisual(idx, {
          symbol,
          symbolSize,
          symbolRotate,
          symbolOffset,
          symbolKeepAspect,
          style
        });
      });
      symbolDraw.updateData(mpData);
      this.group.add(symbolDraw.group);
      mpData.eachItemGraphicEl(function(el) {
        el.traverse(function(child) {
          getECData(child).dataModel = mpModel;
        });
      });
      this.markKeep(symbolDraw);
      symbolDraw.group.silent = mpModel.get("silent") || seriesModel.get("silent");
    };
    MarkPointView2.type = "markPoint";
    return MarkPointView2;
  }(MarkerView_default)
);
function createData(coordSys, seriesModel, mpModel) {
  var coordDimsInfos;
  if (coordSys) {
    coordDimsInfos = map(coordSys && coordSys.dimensions, function(coordDim) {
      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};
      return extend(extend({}, info), {
        name: coordDim,
        // DON'T use ordinalMeta to parse and collect ordinal.
        ordinalMeta: null
      });
    });
  } else {
    coordDimsInfos = [{
      name: "value",
      type: "float"
    }];
  }
  var mpData = new SeriesData_default(coordDimsInfos, mpModel);
  var dataOpt = map(mpModel.get("data"), curry(dataTransform, seriesModel));
  if (coordSys) {
    dataOpt = filter(dataOpt, curry(dataFilter, coordSys));
  }
  var dimValueGetter = createMarkerDimValueGetter(!!coordSys, coordDimsInfos);
  mpData.initData(dataOpt, null, dimValueGetter);
  return mpData;
}
var MarkPointView_default = MarkPointView;

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/installMarkPoint.js
function install(registers) {
  registers.registerComponentModel(MarkPointModel_default);
  registers.registerComponentView(MarkPointView_default);
  registers.registerPreprocessor(function(opt) {
    if (checkMarkerInSeries(opt.series, "markPoint")) {
      opt.markPoint = opt.markPoint || {};
    }
  });
}

export {
  install
};
//# sourceMappingURL=chunk-SAGDWLW7.js.map
