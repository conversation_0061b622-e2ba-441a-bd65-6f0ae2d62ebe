{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkPointModel.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkPointView.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/installMarkPoint.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkPointModel = /** @class */function (_super) {\n  __extends(MarkPointModel, _super);\n  function MarkPointModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkPointModel.type;\n    return _this;\n  }\n  MarkPointModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkPointModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkPointModel.type = 'markPoint';\n  MarkPointModel.defaultOption = {\n    // zlevel: 0,\n    z: 5,\n    symbol: 'pin',\n    symbolSize: 50,\n    // symbolRotate: 0,\n    // symbolOffset: [0, 0]\n    tooltip: {\n      trigger: 'item'\n    },\n    label: {\n      show: true,\n      position: 'inside'\n    },\n    itemStyle: {\n      borderWidth: 2\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    }\n  };\n  return MarkPointModel;\n}(MarkerModel);\nexport default MarkPointModel;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../../chart/helper/SymbolDraw.js';\nimport * as numberUtil from '../../util/number.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isFunction, map, filter, curry, extend } from 'zrender/lib/core/util.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nfunction updateMarkerLayout(mpData, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  mpData.each(function (idx) {\n    var itemModel = mpData.getItemModel(idx);\n    var point;\n    var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n    var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n    if (!isNaN(xPx) && !isNaN(yPx)) {\n      point = [xPx, yPx];\n    }\n    // Chart like bar may have there own marker positioning logic\n    else if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(mpData.getValues(mpData.dimensions, idx));\n    } else if (coordSys) {\n      var x = mpData.get(coordSys.dimensions[0], idx);\n      var y = mpData.get(coordSys.dimensions[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n    mpData.setItemLayout(idx, point);\n  });\n}\nvar MarkPointView = /** @class */function (_super) {\n  __extends(MarkPointView, _super);\n  function MarkPointView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkPointView.type;\n    return _this;\n  }\n  MarkPointView.prototype.updateTransform = function (markPointModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mpModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markPoint');\n      if (mpModel) {\n        updateMarkerLayout(mpModel.getData(), seriesModel, api);\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkPointView.prototype.renderSeries = function (seriesModel, mpModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var symbolDrawMap = this.markerGroupMap;\n    var symbolDraw = symbolDrawMap.get(seriesId) || symbolDrawMap.set(seriesId, new SymbolDraw());\n    var mpData = createData(coordSys, seriesModel, mpModel);\n    // FIXME\n    mpModel.setData(mpData);\n    updateMarkerLayout(mpModel.getData(), seriesModel, api);\n    mpData.each(function (idx) {\n      var itemModel = mpData.getItemModel(idx);\n      var symbol = itemModel.getShallow('symbol');\n      var symbolSize = itemModel.getShallow('symbolSize');\n      var symbolRotate = itemModel.getShallow('symbolRotate');\n      var symbolOffset = itemModel.getShallow('symbolOffset');\n      var symbolKeepAspect = itemModel.getShallow('symbolKeepAspect');\n      // TODO: refactor needed: single data item should not support callback function\n      if (isFunction(symbol) || isFunction(symbolSize) || isFunction(symbolRotate) || isFunction(symbolOffset)) {\n        var rawIdx = mpModel.getRawValue(idx);\n        var dataParams = mpModel.getDataParams(idx);\n        if (isFunction(symbol)) {\n          symbol = symbol(rawIdx, dataParams);\n        }\n        if (isFunction(symbolSize)) {\n          // FIXME 这里不兼容 ECharts 2.x，2.x 貌似参数是整个数据？\n          symbolSize = symbolSize(rawIdx, dataParams);\n        }\n        if (isFunction(symbolRotate)) {\n          symbolRotate = symbolRotate(rawIdx, dataParams);\n        }\n        if (isFunction(symbolOffset)) {\n          symbolOffset = symbolOffset(rawIdx, dataParams);\n        }\n      }\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n      }\n      mpData.setItemVisual(idx, {\n        symbol: symbol,\n        symbolSize: symbolSize,\n        symbolRotate: symbolRotate,\n        symbolOffset: symbolOffset,\n        symbolKeepAspect: symbolKeepAspect,\n        style: style\n      });\n    });\n    // TODO Text are wrong\n    symbolDraw.updateData(mpData);\n    this.group.add(symbolDraw.group);\n    // Set host model for tooltip\n    // FIXME\n    mpData.eachItemGraphicEl(function (el) {\n      el.traverse(function (child) {\n        getECData(child).dataModel = mpModel;\n      });\n    });\n    this.markKeep(symbolDraw);\n    symbolDraw.group.silent = mpModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkPointView.type = 'markPoint';\n  return MarkPointView;\n}(MarkerView);\nfunction createData(coordSys, seriesModel, mpModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var mpData = new SeriesData(coordDimsInfos, mpModel);\n  var dataOpt = map(mpModel.get('data'), curry(markerHelper.dataTransform, seriesModel));\n  if (coordSys) {\n    dataOpt = filter(dataOpt, curry(markerHelper.dataFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  mpData.initData(dataOpt, null, dimValueGetter);\n  return mpData;\n}\nexport default MarkPointView;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkPointModel from './MarkPointModel.js';\nimport MarkPointView from './MarkPointView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkPointModel);\n  registers.registerComponentView(MarkPointView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markPoint')) {\n      // Make sure markPoint component is enabled\n      opt.markPoint = opt.markPoint || {};\n    }\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAClD,cAAUA,iBAAgB,MAAM;AAChC,aAASA,kBAAiB;AACxB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,gBAAe;AAC5B,aAAO;AAAA,IACT;AACA,IAAAA,gBAAe,UAAU,8BAA8B,SAAU,WAAW,mBAAmB,SAAS;AACtG,aAAO,IAAIA,gBAAe,WAAW,mBAAmB,OAAO;AAAA,IACjE;AACA,IAAAA,gBAAe,OAAO;AACtB,IAAAA,gBAAe,gBAAgB;AAAA;AAAA,MAE7B,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,YAAY;AAAA;AAAA;AAAA,MAGZ,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,WAAW;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,OAAO;AAAA,UACL,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,mBAAW;AAAA;AACb,IAAO,yBAAQ;;;AC5Bf,SAAS,mBAAmB,QAAQ,aAAa,KAAK;AACpD,MAAI,WAAW,YAAY;AAC3B,SAAO,KAAK,SAAU,KAAK;AACzB,QAAI,YAAY,OAAO,aAAa,GAAG;AACvC,QAAI;AACJ,QAAI,MAAiB,aAAa,UAAU,IAAI,GAAG,GAAG,IAAI,SAAS,CAAC;AACpE,QAAI,MAAiB,aAAa,UAAU,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC;AACrE,QAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,cAAQ,CAAC,KAAK,GAAG;AAAA,IACnB,WAES,YAAY,mBAAmB;AAEtC,cAAQ,YAAY,kBAAkB,OAAO,UAAU,OAAO,YAAY,GAAG,CAAC;AAAA,IAChF,WAAW,UAAU;AACnB,UAAI,IAAI,OAAO,IAAI,SAAS,WAAW,CAAC,GAAG,GAAG;AAC9C,UAAI,IAAI,OAAO,IAAI,SAAS,WAAW,CAAC,GAAG,GAAG;AAC9C,cAAQ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC;AAAA,IACrC;AAEA,QAAI,CAAC,MAAM,GAAG,GAAG;AACf,YAAM,CAAC,IAAI;AAAA,IACb;AACA,QAAI,CAAC,MAAM,GAAG,GAAG;AACf,YAAM,CAAC,IAAI;AAAA,IACb;AACA,WAAO,cAAc,KAAK,KAAK;AAAA,EACjC,CAAC;AACH;AACA,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACvB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,eAAc;AAC3B,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,kBAAkB,SAAU,gBAAgB,SAAS,KAAK;AAChF,cAAQ,WAAW,SAAU,aAAa;AACxC,YAAI,UAAU,oBAAY,yBAAyB,aAAa,WAAW;AAC3E,YAAI,SAAS;AACX,6BAAmB,QAAQ,QAAQ,GAAG,aAAa,GAAG;AACtD,eAAK,eAAe,IAAI,YAAY,EAAE,EAAE,aAAa;AAAA,QACvD;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,aAAa,SAAS,SAAS,KAAK;AACnF,UAAI,WAAW,YAAY;AAC3B,UAAI,WAAW,YAAY;AAC3B,UAAI,aAAa,YAAY,QAAQ;AACrC,UAAI,gBAAgB,KAAK;AACzB,UAAI,aAAa,cAAc,IAAI,QAAQ,KAAK,cAAc,IAAI,UAAU,IAAI,mBAAW,CAAC;AAC5F,UAAI,SAAS,WAAW,UAAU,aAAa,OAAO;AAEtD,cAAQ,QAAQ,MAAM;AACtB,yBAAmB,QAAQ,QAAQ,GAAG,aAAa,GAAG;AACtD,aAAO,KAAK,SAAU,KAAK;AACzB,YAAI,YAAY,OAAO,aAAa,GAAG;AACvC,YAAI,SAAS,UAAU,WAAW,QAAQ;AAC1C,YAAI,aAAa,UAAU,WAAW,YAAY;AAClD,YAAI,eAAe,UAAU,WAAW,cAAc;AACtD,YAAI,eAAe,UAAU,WAAW,cAAc;AACtD,YAAI,mBAAmB,UAAU,WAAW,kBAAkB;AAE9D,YAAI,WAAW,MAAM,KAAK,WAAW,UAAU,KAAK,WAAW,YAAY,KAAK,WAAW,YAAY,GAAG;AACxG,cAAI,SAAS,QAAQ,YAAY,GAAG;AACpC,cAAI,aAAa,QAAQ,cAAc,GAAG;AAC1C,cAAI,WAAW,MAAM,GAAG;AACtB,qBAAS,OAAO,QAAQ,UAAU;AAAA,UACpC;AACA,cAAI,WAAW,UAAU,GAAG;AAE1B,yBAAa,WAAW,QAAQ,UAAU;AAAA,UAC5C;AACA,cAAI,WAAW,YAAY,GAAG;AAC5B,2BAAe,aAAa,QAAQ,UAAU;AAAA,UAChD;AACA,cAAI,WAAW,YAAY,GAAG;AAC5B,2BAAe,aAAa,QAAQ,UAAU;AAAA,UAChD;AAAA,QACF;AACA,YAAI,QAAQ,UAAU,SAAS,WAAW,EAAE,aAAa;AACzD,YAAI,QAAQ,kBAAkB,YAAY,OAAO;AACjD,YAAI,CAAC,MAAM,MAAM;AACf,gBAAM,OAAO;AAAA,QACf;AACA,eAAO,cAAc,KAAK;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,iBAAW,WAAW,MAAM;AAC5B,WAAK,MAAM,IAAI,WAAW,KAAK;AAG/B,aAAO,kBAAkB,SAAU,IAAI;AACrC,WAAG,SAAS,SAAU,OAAO;AAC3B,oBAAU,KAAK,EAAE,YAAY;AAAA,QAC/B,CAAC;AAAA,MACH,CAAC;AACD,WAAK,SAAS,UAAU;AACxB,iBAAW,MAAM,SAAS,QAAQ,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ;AAAA,IAC7E;AACA,IAAAA,eAAc,OAAO;AACrB,WAAOA;AAAA,EACT,EAAE,kBAAU;AAAA;AACZ,SAAS,WAAW,UAAU,aAAa,SAAS;AAClD,MAAI;AACJ,MAAI,UAAU;AACZ,qBAAiB,IAAI,YAAY,SAAS,YAAY,SAAU,UAAU;AACxE,UAAI,OAAO,YAAY,QAAQ,EAAE,iBAAiB,YAAY,QAAQ,EAAE,aAAa,QAAQ,CAAC,KAAK,CAAC;AAEpG,aAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,QAC9B,MAAM;AAAA;AAAA,QAEN,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,OAAO;AACL,qBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,MAAI,SAAS,IAAI,mBAAW,gBAAgB,OAAO;AACnD,MAAI,UAAU,IAAI,QAAQ,IAAI,MAAM,GAAG,MAAmB,eAAe,WAAW,CAAC;AACrF,MAAI,UAAU;AACZ,cAAU,OAAO,SAAS,MAAmB,YAAY,QAAQ,CAAC;AAAA,EACpE;AACA,MAAI,iBAA8B,2BAA2B,CAAC,CAAC,UAAU,cAAc;AACvF,SAAO,SAAS,SAAS,MAAM,cAAc;AAC7C,SAAO;AACT;AACA,IAAO,wBAAQ;;;AClKR,SAAS,QAAQ,WAAW;AACjC,YAAU,uBAAuB,sBAAc;AAC/C,YAAU,sBAAsB,qBAAa;AAC7C,YAAU,qBAAqB,SAAU,KAAK;AAC5C,QAAI,oBAAoB,IAAI,QAAQ,WAAW,GAAG;AAEhD,UAAI,YAAY,IAAI,aAAa,CAAC;AAAA,IACpC;AAAA,EACF,CAAC;AACH;", "names": ["MarkPointModel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}