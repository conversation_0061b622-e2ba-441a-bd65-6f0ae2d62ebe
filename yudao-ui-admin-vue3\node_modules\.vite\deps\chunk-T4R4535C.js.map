{"version": 3, "sources": ["../../.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/util/Geometry.js", "../../.pnpm/path-intersection@3.1.0/node_modules/path-intersection/intersect.js", "../../.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/util/ModelUtil.js", "../../.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/layout/LayoutUtil.js", "../../.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/util/Elements.js"], "sourcesContent": ["import {\n  every\n} from 'min-dash';\n\n/**\n * @typedef {import('../util/Types').Axis} Axis\n * @typedef {import('../util/Types').Point} Point\n * @typedef {import('../util/Types').Rect} Rect\n */\n\n/**\n * Computes the distance between two points.\n *\n * @param {Point} a\n * @param {Point} b\n *\n * @return {number} The distance between the two points.\n */\nexport function pointDistance(a, b) {\n  if (!a || !b) {\n    return -1;\n  }\n\n  return Math.sqrt(\n    Math.pow(a.x - b.x, 2) +\n    Math.pow(a.y - b.y, 2)\n  );\n}\n\n\n/**\n * Returns true if the point r is on the line between p and q.\n *\n * @param {Point} p\n * @param {Point} q\n * @param {Point} r\n * @param {number} [accuracy=5] The accuracy with which to check (lower is better).\n *\n * @return {boolean}\n */\nexport function pointsOnLine(p, q, r, accuracy) {\n\n  if (typeof accuracy === 'undefined') {\n    accuracy = 5;\n  }\n\n  if (!p || !q || !r) {\n    return false;\n  }\n\n  var val = (q.x - p.x) * (r.y - p.y) - (q.y - p.y) * (r.x - p.x),\n      dist = pointDistance(p, q);\n\n  // @see http://stackoverflow.com/a/907491/412190\n  return Math.abs(val / dist) <= accuracy;\n}\n\n\nvar ALIGNED_THRESHOLD = 2;\n\n/**\n * Check whether two points are horizontally or vertically aligned.\n *\n * @param {Point[]|Point} a\n * @param {Point} [b]\n *\n * @return {string|boolean} If and how the two points are aligned ('h', 'v' or `false`).\n */\nexport function pointsAligned(a, b) {\n  var points = Array.from(arguments).flat();\n\n  const axisMap = {\n    'x': 'v',\n    'y': 'h'\n  };\n\n  for (const [ axis, orientation ] of Object.entries(axisMap)) {\n    if (pointsAlignedOnAxis(axis, points)) {\n      return orientation;\n    }\n  }\n\n  return false;\n}\n\n/**\n * @param {Axis} axis\n * @param {Point[]} points\n *\n * @return {boolean}\n */\nexport function pointsAlignedOnAxis(axis, points) {\n  const referencePoint = points[0];\n\n  return every(points, function(point) {\n    return Math.abs(referencePoint[axis] - point[axis]) <= ALIGNED_THRESHOLD;\n  });\n}\n\n/**\n * Returns true if the point p is inside the rectangle rect\n *\n * @param {Point} p\n * @param {Rect} rect\n * @param {number} tolerance\n *\n * @return {boolean}\n */\nexport function pointInRect(p, rect, tolerance) {\n  tolerance = tolerance || 0;\n\n  return p.x > rect.x - tolerance &&\n         p.y > rect.y - tolerance &&\n         p.x < rect.x + rect.width + tolerance &&\n         p.y < rect.y + rect.height + tolerance;\n}\n\n/**\n * Returns a point in the middle of points p and q\n *\n * @param {Point} p\n * @param {Point} q\n *\n * @return {Point} The mid point between the two points.\n */\nexport function getMidPoint(p, q) {\n  return {\n    x: Math.round(p.x + ((q.x - p.x) / 2.0)),\n    y: Math.round(p.y + ((q.y - p.y) / 2.0))\n  };\n}\n", "/**\n * This file contains source code adapted from Snap.svg (licensed Apache-2.0).\n *\n * @see https://github.com/adobe-webplatform/Snap.svg/blob/master/src/path.js\n */\n\n/* eslint no-fallthrough: \"off\" */\n\nvar p2s = /,?([a-z]),?/gi,\n    toFloat = parseFloat,\n    math = Math,\n    PI = math.PI,\n    mmin = math.min,\n    mmax = math.max,\n    pow = math.pow,\n    abs = math.abs,\n    pathCommand = /([a-z])[\\s,]*((-?\\d*\\.?\\d*(?:e[-+]?\\d+)?[\\s]*,?[\\s]*)+)/ig,\n    pathValues = /(-?\\d*\\.?\\d*(?:e[-+]?\\d+)?)[\\s]*,?[\\s]*/ig;\n\nvar isArray = Array.isArray || function(o) { return o instanceof Array; };\n\nfunction hasProperty(obj, property) {\n  return Object.prototype.hasOwnProperty.call(obj, property);\n}\n\nfunction clone(obj) {\n\n  if (typeof obj == 'function' || Object(obj) !== obj) {\n    return obj;\n  }\n\n  var res = new obj.constructor;\n\n  for (var key in obj) {\n    if (hasProperty(obj, key)) {\n      res[key] = clone(obj[key]);\n    }\n  }\n\n  return res;\n}\n\nfunction repush(array, item) {\n  for (var i = 0, ii = array.length; i < ii; i++) if (array[i] === item) {\n    return array.push(array.splice(i, 1)[0]);\n  }\n}\n\nfunction cacher(f) {\n\n  function newf() {\n\n    var arg = Array.prototype.slice.call(arguments, 0),\n        args = arg.join('\\u2400'),\n        cache = newf.cache = newf.cache || {},\n        count = newf.count = newf.count || [];\n\n    if (hasProperty(cache, args)) {\n      repush(count, args);\n      return cache[args];\n    }\n\n    count.length >= 1e3 && delete cache[count.shift()];\n    count.push(args);\n    cache[args] = f(...arguments);\n\n    return cache[args];\n  }\n  return newf;\n}\n\nfunction parsePathString(pathString) {\n\n  if (!pathString) {\n    return null;\n  }\n\n  var pth = paths(pathString);\n\n  if (pth.arr) {\n    return clone(pth.arr);\n  }\n\n  var paramCounts = { a: 7, c: 6, h: 1, l: 2, m: 2, q: 4, s: 4, t: 2, v: 1, z: 0 },\n      data = [];\n\n  if (isArray(pathString) && isArray(pathString[0])) { // rough assumption\n    data = clone(pathString);\n  }\n\n  if (!data.length) {\n\n    String(pathString).replace(pathCommand, function(a, b, c) {\n      var params = [],\n          name = b.toLowerCase();\n\n      c.replace(pathValues, function(a, b) {\n        b && params.push(+b);\n      });\n\n      if (name == 'm' && params.length > 2) {\n        data.push([ b, ...params.splice(0, 2) ]);\n        name = 'l';\n        b = b == 'm' ? 'l' : 'L';\n      }\n\n      while (params.length >= paramCounts[name]) {\n        data.push([ b, ...params.splice(0, paramCounts[name]) ]);\n        if (!paramCounts[name]) {\n          break;\n        }\n      }\n    });\n  }\n\n  data.toString = paths.toString;\n  pth.arr = clone(data);\n\n  return data;\n}\n\nfunction paths(ps) {\n  var p = paths.ps = paths.ps || {};\n\n  if (p[ps]) {\n    p[ps].sleep = 100;\n  } else {\n    p[ps] = {\n      sleep: 100\n    };\n  }\n\n  setTimeout(function() {\n    for (var key in p) {\n      if (hasProperty(p, key) && key != ps) {\n        p[key].sleep--;\n        !p[key].sleep && delete p[key];\n      }\n    }\n  });\n\n  return p[ps];\n}\n\nfunction rectBBox(x, y, width, height) {\n\n  if (arguments.length === 1) {\n    y = x.y;\n    width = x.width;\n    height = x.height;\n    x = x.x;\n  }\n\n  return {\n    x: x,\n    y: y,\n    width: width,\n    height: height,\n    x2: x + width,\n    y2: y + height\n  };\n}\n\nfunction pathToString() {\n  return this.join(',').replace(p2s, '$1');\n}\n\nfunction pathClone(pathArray) {\n  var res = clone(pathArray);\n  res.toString = pathToString;\n  return res;\n}\n\nfunction findDotsAtSegment(p1x, p1y, c1x, c1y, c2x, c2y, p2x, p2y, t) {\n  var t1 = 1 - t,\n      t13 = pow(t1, 3),\n      t12 = pow(t1, 2),\n      t2 = t * t,\n      t3 = t2 * t,\n      x = t13 * p1x + t12 * 3 * t * c1x + t1 * 3 * t * t * c2x + t3 * p2x,\n      y = t13 * p1y + t12 * 3 * t * c1y + t1 * 3 * t * t * c2y + t3 * p2y;\n\n  return {\n    x: fixError(x),\n    y: fixError(y)\n  };\n}\n\nfunction bezierBBox(points) {\n\n  var bbox = curveBBox(...points);\n\n  return rectBBox(\n    bbox.x0,\n    bbox.y0,\n    bbox.x1 - bbox.x0,\n    bbox.y1 - bbox.y0\n  );\n}\n\nfunction isPointInsideBBox(bbox, x, y) {\n  return x >= bbox.x &&\n    x <= bbox.x + bbox.width &&\n    y >= bbox.y &&\n    y <= bbox.y + bbox.height;\n}\n\nfunction isBBoxIntersect(bbox1, bbox2) {\n  bbox1 = rectBBox(bbox1);\n  bbox2 = rectBBox(bbox2);\n  return isPointInsideBBox(bbox2, bbox1.x, bbox1.y)\n    || isPointInsideBBox(bbox2, bbox1.x2, bbox1.y)\n    || isPointInsideBBox(bbox2, bbox1.x, bbox1.y2)\n    || isPointInsideBBox(bbox2, bbox1.x2, bbox1.y2)\n    || isPointInsideBBox(bbox1, bbox2.x, bbox2.y)\n    || isPointInsideBBox(bbox1, bbox2.x2, bbox2.y)\n    || isPointInsideBBox(bbox1, bbox2.x, bbox2.y2)\n    || isPointInsideBBox(bbox1, bbox2.x2, bbox2.y2)\n    || (bbox1.x < bbox2.x2 && bbox1.x > bbox2.x\n        || bbox2.x < bbox1.x2 && bbox2.x > bbox1.x)\n    && (bbox1.y < bbox2.y2 && bbox1.y > bbox2.y\n        || bbox2.y < bbox1.y2 && bbox2.y > bbox1.y);\n}\n\nfunction base3(t, p1, p2, p3, p4) {\n  var t1 = -3 * p1 + 9 * p2 - 9 * p3 + 3 * p4,\n      t2 = t * t1 + 6 * p1 - 12 * p2 + 6 * p3;\n  return t * t2 - 3 * p1 + 3 * p2;\n}\n\nfunction bezlen(x1, y1, x2, y2, x3, y3, x4, y4, z) {\n\n  if (z == null) {\n    z = 1;\n  }\n\n  z = z > 1 ? 1 : z < 0 ? 0 : z;\n\n  var z2 = z / 2,\n      n = 12,\n      Tvalues = [ -.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816 ],\n      Cvalues = [ 0.2491,0.2491,0.2335,0.2335,0.2032,0.2032,0.1601,0.1601,0.1069,0.1069,0.0472,0.0472 ],\n      sum = 0;\n\n  for (var i = 0; i < n; i++) {\n    var ct = z2 * Tvalues[i] + z2,\n        xbase = base3(ct, x1, x2, x3, x4),\n        ybase = base3(ct, y1, y2, y3, y4),\n        comb = xbase * xbase + ybase * ybase;\n\n    sum += Cvalues[i] * math.sqrt(comb);\n  }\n\n  return z2 * sum;\n}\n\n\nfunction intersectLines(x1, y1, x2, y2, x3, y3, x4, y4) {\n\n  if (\n    mmax(x1, x2) < mmin(x3, x4) ||\n      mmin(x1, x2) > mmax(x3, x4) ||\n      mmax(y1, y2) < mmin(y3, y4) ||\n      mmin(y1, y2) > mmax(y3, y4)\n  ) {\n    return;\n  }\n\n  var nx = (x1 * y2 - y1 * x2) * (x3 - x4) - (x1 - x2) * (x3 * y4 - y3 * x4),\n      ny = (x1 * y2 - y1 * x2) * (y3 - y4) - (y1 - y2) * (x3 * y4 - y3 * x4),\n      denominator = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);\n\n  if (!denominator) {\n    return;\n  }\n\n  var px = fixError(nx / denominator),\n      py = fixError(ny / denominator),\n      px2 = +px.toFixed(2),\n      py2 = +py.toFixed(2);\n\n  if (\n    px2 < +mmin(x1, x2).toFixed(2) ||\n      px2 > +mmax(x1, x2).toFixed(2) ||\n      px2 < +mmin(x3, x4).toFixed(2) ||\n      px2 > +mmax(x3, x4).toFixed(2) ||\n      py2 < +mmin(y1, y2).toFixed(2) ||\n      py2 > +mmax(y1, y2).toFixed(2) ||\n      py2 < +mmin(y3, y4).toFixed(2) ||\n      py2 > +mmax(y3, y4).toFixed(2)\n  ) {\n    return;\n  }\n\n  return { x: px, y: py };\n}\n\nfunction fixError(number) {\n  return Math.round(number * 100000000000) / 100000000000;\n}\n\nfunction findBezierIntersections(bez1, bez2, justCount) {\n  var bbox1 = bezierBBox(bez1),\n      bbox2 = bezierBBox(bez2);\n\n  if (!isBBoxIntersect(bbox1, bbox2)) {\n    return justCount ? 0 : [];\n  }\n\n  // As an optimization, lines will have only 1 segment\n\n  var l1 = bezlen(...bez1),\n      l2 = bezlen(...bez2),\n      n1 = isLine(bez1) ? 1 : ~~(l1 / 5) || 1,\n      n2 = isLine(bez2) ? 1 : ~~(l2 / 5) || 1,\n      dots1 = [],\n      dots2 = [],\n      xy = {},\n      res = justCount ? 0 : [];\n\n  for (var i = 0; i < n1 + 1; i++) {\n    var p = findDotsAtSegment(...bez1, i / n1);\n    dots1.push({ x: p.x, y: p.y, t: i / n1 });\n  }\n\n  for (i = 0; i < n2 + 1; i++) {\n    p = findDotsAtSegment(...bez2, i / n2);\n    dots2.push({ x: p.x, y: p.y, t: i / n2 });\n  }\n\n  for (i = 0; i < n1; i++) {\n\n    for (var j = 0; j < n2; j++) {\n      var di = dots1[i],\n          di1 = dots1[i + 1],\n          dj = dots2[j],\n          dj1 = dots2[j + 1],\n          ci = abs(di1.x - di.x) < .01 ? 'y' : 'x',\n          cj = abs(dj1.x - dj.x) < .01 ? 'y' : 'x',\n          is = intersectLines(di.x, di.y, di1.x, di1.y, dj.x, dj.y, dj1.x, dj1.y),\n          key;\n\n      if (is) {\n        key = is.x.toFixed(9) + '#' + is.y.toFixed(9);\n\n        if (xy[key]) {\n          continue;\n        }\n\n        xy[key] = true;\n\n        var t1 = di.t + abs((is[ci] - di[ci]) / (di1[ci] - di[ci])) * (di1.t - di.t),\n            t2 = dj.t + abs((is[cj] - dj[cj]) / (dj1[cj] - dj[cj])) * (dj1.t - dj.t);\n\n        if (t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1) {\n\n          if (justCount) {\n            res++;\n          } else {\n            res.push({\n              x: is.x,\n              y: is.y,\n              t1: t1,\n              t2: t2\n            });\n          }\n        }\n      }\n    }\n  }\n\n  return res;\n}\n\n\n/**\n * Find or counts the intersections between two SVG paths.\n *\n * Returns a number in counting mode and a list of intersections otherwise.\n *\n * A single intersection entry contains the intersection coordinates (x, y)\n * as well as additional information regarding the intersecting segments\n * on each path (segment1, segment2) and the relative location of the\n * intersection on these segments (t1, t2).\n *\n * The path may be an SVG path string or a list of path components\n * such as `[ [ 'M', 0, 10 ], [ 'L', 20, 0 ] ]`.\n *\n * @example\n *\n * var intersections = findPathIntersections(\n *   'M0,0L100,100',\n *   [ [ 'M', 0, 100 ], [ 'L', 100, 0 ] ]\n * );\n *\n * // intersections = [\n * //   { x: 50, y: 50, segment1: 1, segment2: 1, t1: 0.5, t2: 0.5 }\n * // ]\n *\n * @param {String|Array<PathDef>} path1\n * @param {String|Array<PathDef>} path2\n * @param {Boolean} [justCount=false]\n *\n * @return {Array<Intersection>|Number}\n */\nexport default function findPathIntersections(path1, path2, justCount) {\n  path1 = pathToCurve(path1);\n  path2 = pathToCurve(path2);\n\n  var x1, y1, x2, y2, x1m, y1m, x2m, y2m, bez1, bez2,\n      res = justCount ? 0 : [];\n\n  for (var i = 0, ii = path1.length; i < ii; i++) {\n    var pi = path1[i];\n\n    if (pi[0] == 'M') {\n      x1 = x1m = pi[1];\n      y1 = y1m = pi[2];\n    } else {\n\n      if (pi[0] == 'C') {\n        bez1 = [ x1, y1, ...pi.slice(1) ];\n        x1 = bez1[6];\n        y1 = bez1[7];\n      } else {\n        bez1 = [ x1, y1, x1, y1, x1m, y1m, x1m, y1m ];\n        x1 = x1m;\n        y1 = y1m;\n      }\n\n      for (var j = 0, jj = path2.length; j < jj; j++) {\n        var pj = path2[j];\n\n        if (pj[0] == 'M') {\n          x2 = x2m = pj[1];\n          y2 = y2m = pj[2];\n        } else {\n\n          if (pj[0] == 'C') {\n            bez2 = [ x2, y2, ...pj.slice(1) ];\n            x2 = bez2[6];\n            y2 = bez2[7];\n          } else {\n            bez2 = [ x2, y2, x2, y2, x2m, y2m, x2m, y2m ];\n            x2 = x2m;\n            y2 = y2m;\n          }\n\n          var intr = findBezierIntersections(bez1, bez2, justCount);\n\n          if (justCount) {\n            res += intr;\n          } else {\n\n            for (var k = 0, kk = intr.length; k < kk; k++) {\n              intr[k].segment1 = i;\n              intr[k].segment2 = j;\n              intr[k].bez1 = bez1;\n              intr[k].bez2 = bez2;\n            }\n\n            res = res.concat(intr);\n          }\n        }\n      }\n    }\n  }\n\n  return res;\n}\n\n\nfunction pathToAbsolute(pathArray) {\n  var pth = paths(pathArray);\n\n  if (pth.abs) {\n    return pathClone(pth.abs);\n  }\n\n  if (!isArray(pathArray) || !isArray(pathArray && pathArray[0])) { // rough assumption\n    pathArray = parsePathString(pathArray);\n  }\n\n  if (!pathArray || !pathArray.length) {\n    return [ [ 'M', 0, 0 ] ];\n  }\n\n  var res = [],\n      x = 0,\n      y = 0,\n      mx = 0,\n      my = 0,\n      start = 0,\n      pa0;\n\n  if (pathArray[0][0] == 'M') {\n    x = +pathArray[0][1];\n    y = +pathArray[0][2];\n    mx = x;\n    my = y;\n    start++;\n    res[0] = [ 'M', x, y ];\n  }\n\n  for (var r, pa, i = start, ii = pathArray.length; i < ii; i++) {\n    res.push(r = []);\n    pa = pathArray[i];\n    pa0 = pa[0];\n\n    if (pa0 != pa0.toUpperCase()) {\n      r[0] = pa0.toUpperCase();\n\n      switch (r[0]) {\n      case 'A':\n        r[1] = pa[1];\n        r[2] = pa[2];\n        r[3] = pa[3];\n        r[4] = pa[4];\n        r[5] = pa[5];\n        r[6] = +pa[6] + x;\n        r[7] = +pa[7] + y;\n        break;\n      case 'V':\n        r[1] = +pa[1] + y;\n        break;\n      case 'H':\n        r[1] = +pa[1] + x;\n        break;\n      case 'M':\n        mx = +pa[1] + x;\n        my = +pa[2] + y;\n      default:\n        for (var j = 1, jj = pa.length; j < jj; j++) {\n          r[j] = +pa[j] + ((j % 2) ? x : y);\n        }\n      }\n    } else {\n      for (var k = 0, kk = pa.length; k < kk; k++) {\n        r[k] = pa[k];\n      }\n    }\n    pa0 = pa0.toUpperCase();\n\n    switch (r[0]) {\n    case 'Z':\n      x = +mx;\n      y = +my;\n      break;\n    case 'H':\n      x = r[1];\n      break;\n    case 'V':\n      y = r[1];\n      break;\n    case 'M':\n      mx = r[r.length - 2];\n      my = r[r.length - 1];\n    default:\n      x = r[r.length - 2];\n      y = r[r.length - 1];\n    }\n  }\n\n  res.toString = pathToString;\n  pth.abs = pathClone(res);\n\n  return res;\n}\n\nfunction isLine(bez) {\n  return (\n    bez[0] === bez[2] &&\n    bez[1] === bez[3] &&\n    bez[4] === bez[6] &&\n    bez[5] === bez[7]\n  );\n}\n\nfunction lineToCurve(x1, y1, x2, y2) {\n  return [\n    x1, y1, x2,\n    y2, x2, y2\n  ];\n}\n\nfunction qubicToCurve(x1, y1, ax, ay, x2, y2) {\n  var _13 = 1 / 3,\n      _23 = 2 / 3;\n\n  return [\n    _13 * x1 + _23 * ax,\n    _13 * y1 + _23 * ay,\n    _13 * x2 + _23 * ax,\n    _13 * y2 + _23 * ay,\n    x2,\n    y2\n  ];\n}\n\nfunction arcToCurve(x1, y1, rx, ry, angle, large_arc_flag, sweep_flag, x2, y2, recursive) {\n\n  // for more information of where this math came from visit:\n  // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes\n  var _120 = PI * 120 / 180,\n      rad = PI / 180 * (+angle || 0),\n      res = [],\n      xy,\n      rotate = cacher(function(x, y, rad) {\n        var X = x * math.cos(rad) - y * math.sin(rad),\n            Y = x * math.sin(rad) + y * math.cos(rad);\n\n        return { x: X, y: Y };\n      });\n\n  if (!recursive) {\n    xy = rotate(x1, y1, -rad);\n    x1 = xy.x;\n    y1 = xy.y;\n    xy = rotate(x2, y2, -rad);\n    x2 = xy.x;\n    y2 = xy.y;\n\n    var x = (x1 - x2) / 2,\n        y = (y1 - y2) / 2;\n\n    var h = (x * x) / (rx * rx) + (y * y) / (ry * ry);\n\n    if (h > 1) {\n      h = math.sqrt(h);\n      rx = h * rx;\n      ry = h * ry;\n    }\n\n    var rx2 = rx * rx,\n        ry2 = ry * ry,\n        k = (large_arc_flag == sweep_flag ? -1 : 1) *\n            math.sqrt(abs((rx2 * ry2 - rx2 * y * y - ry2 * x * x) / (rx2 * y * y + ry2 * x * x))),\n        cx = k * rx * y / ry + (x1 + x2) / 2,\n        cy = k * -ry * x / rx + (y1 + y2) / 2,\n        f1 = math.asin(((y1 - cy) / ry).toFixed(9)),\n        f2 = math.asin(((y2 - cy) / ry).toFixed(9));\n\n    f1 = x1 < cx ? PI - f1 : f1;\n    f2 = x2 < cx ? PI - f2 : f2;\n    f1 < 0 && (f1 = PI * 2 + f1);\n    f2 < 0 && (f2 = PI * 2 + f2);\n\n    if (sweep_flag && f1 > f2) {\n      f1 = f1 - PI * 2;\n    }\n    if (!sweep_flag && f2 > f1) {\n      f2 = f2 - PI * 2;\n    }\n  } else {\n    f1 = recursive[0];\n    f2 = recursive[1];\n    cx = recursive[2];\n    cy = recursive[3];\n  }\n\n  var df = f2 - f1;\n\n  if (abs(df) > _120) {\n    var f2old = f2,\n        x2old = x2,\n        y2old = y2;\n\n    f2 = f1 + _120 * (sweep_flag && f2 > f1 ? 1 : -1);\n    x2 = cx + rx * math.cos(f2);\n    y2 = cy + ry * math.sin(f2);\n    res = arcToCurve(x2, y2, rx, ry, angle, 0, sweep_flag, x2old, y2old, [ f2, f2old, cx, cy ]);\n  }\n\n  df = f2 - f1;\n\n  var c1 = math.cos(f1),\n      s1 = math.sin(f1),\n      c2 = math.cos(f2),\n      s2 = math.sin(f2),\n      t = math.tan(df / 4),\n      hx = 4 / 3 * rx * t,\n      hy = 4 / 3 * ry * t,\n      m1 = [ x1, y1 ],\n      m2 = [ x1 + hx * s1, y1 - hy * c1 ],\n      m3 = [ x2 + hx * s2, y2 - hy * c2 ],\n      m4 = [ x2, y2 ];\n\n  m2[0] = 2 * m1[0] - m2[0];\n  m2[1] = 2 * m1[1] - m2[1];\n\n  if (recursive) {\n    return [ m2, m3, m4 ].concat(res);\n  } else {\n    res = [ m2, m3, m4 ].concat(res).join().split(',');\n    var newres = [];\n\n    for (var i = 0, ii = res.length; i < ii; i++) {\n      newres[i] = i % 2 ? rotate(res[i - 1], res[i], rad).y : rotate(res[i], res[i + 1], rad).x;\n    }\n\n    return newres;\n  }\n}\n\n// Returns bounding box of cubic bezier curve.\n// Source: http://blog.hackers-cafe.net/2009/06/how-to-calculate-bezier-curves-bounding.html\n// Original version: NISHIO Hirokazu\n// Modifications: https://github.com/timo22345\nfunction curveBBox(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var tvalues = [],\n      bounds = [ [], [] ],\n      a, b, c, t, t1, t2, b2ac, sqrtb2ac;\n\n  for (var i = 0; i < 2; ++i) {\n\n    if (i == 0) {\n      b = 6 * x0 - 12 * x1 + 6 * x2;\n      a = -3 * x0 + 9 * x1 - 9 * x2 + 3 * x3;\n      c = 3 * x1 - 3 * x0;\n    } else {\n      b = 6 * y0 - 12 * y1 + 6 * y2;\n      a = -3 * y0 + 9 * y1 - 9 * y2 + 3 * y3;\n      c = 3 * y1 - 3 * y0;\n    }\n\n    if (abs(a) < 1e-12) {\n\n      if (abs(b) < 1e-12) {\n        continue;\n      }\n\n      t = -c / b;\n\n      if (0 < t && t < 1) {\n        tvalues.push(t);\n      }\n\n      continue;\n    }\n\n    b2ac = b * b - 4 * c * a;\n    sqrtb2ac = math.sqrt(b2ac);\n\n    if (b2ac < 0) {\n      continue;\n    }\n\n    t1 = (-b + sqrtb2ac) / (2 * a);\n\n    if (0 < t1 && t1 < 1) {\n      tvalues.push(t1);\n    }\n\n    t2 = (-b - sqrtb2ac) / (2 * a);\n\n    if (0 < t2 && t2 < 1) {\n      tvalues.push(t2);\n    }\n  }\n\n  var j = tvalues.length,\n      jlen = j,\n      mt;\n\n  while (j--) {\n    t = tvalues[j];\n    mt = 1 - t;\n    bounds[0][j] = (mt * mt * mt * x0) + (3 * mt * mt * t * x1) + (3 * mt * t * t * x2) + (t * t * t * x3);\n    bounds[1][j] = (mt * mt * mt * y0) + (3 * mt * mt * t * y1) + (3 * mt * t * t * y2) + (t * t * t * y3);\n  }\n\n  bounds[0][jlen] = x0;\n  bounds[1][jlen] = y0;\n  bounds[0][jlen + 1] = x3;\n  bounds[1][jlen + 1] = y3;\n  bounds[0].length = bounds[1].length = jlen + 2;\n\n  return {\n    x0: mmin(...bounds[0]),\n    y0: mmin(...bounds[1]),\n    x1: mmax(...bounds[0]),\n    y1: mmax(...bounds[1])\n  };\n}\n\nfunction pathToCurve(path) {\n\n  var pth = paths(path);\n\n  // return cached curve, if existing\n  if (pth.curve) {\n    return pathClone(pth.curve);\n  }\n\n  var curvedPath = pathToAbsolute(path),\n      attrs = { x: 0, y: 0, bx: 0, by: 0, X: 0, Y: 0, qx: null, qy: null },\n      processPath = function(path, d, pathCommand) {\n        var nx, ny;\n\n        if (!path) {\n          return [ 'C', d.x, d.y, d.x, d.y, d.x, d.y ];\n        }\n\n        !(path[0] in { T: 1, Q: 1 }) && (d.qx = d.qy = null);\n\n        switch (path[0]) {\n        case 'M':\n          d.X = path[1];\n          d.Y = path[2];\n          break;\n        case 'A':\n          path = [ 'C', ...arcToCurve(d.x, d.y, ...path.slice(1)) ];\n          break;\n        case 'S':\n          if (pathCommand == 'C' || pathCommand == 'S') {\n\n            // In 'S' case we have to take into account, if the previous command is C/S.\n            nx = d.x * 2 - d.bx;\n\n            // And reflect the previous\n            ny = d.y * 2 - d.by;\n\n            // command's control point relative to the current point.\n          }\n          else {\n\n            // or some else or nothing\n            nx = d.x;\n            ny = d.y;\n          }\n          path = [ 'C', nx, ny, ...path.slice(1) ];\n          break;\n        case 'T':\n          if (pathCommand == 'Q' || pathCommand == 'T') {\n\n            // In 'T' case we have to take into account, if the previous command is Q/T.\n            d.qx = d.x * 2 - d.qx;\n\n            // And make a reflection similar\n            d.qy = d.y * 2 - d.qy;\n\n            // to case 'S'.\n          }\n          else {\n\n            // or something else or nothing\n            d.qx = d.x;\n            d.qy = d.y;\n          }\n          path = [ 'C', ...qubicToCurve(d.x, d.y, d.qx, d.qy, path[1], path[2]) ];\n          break;\n        case 'Q':\n          d.qx = path[1];\n          d.qy = path[2];\n          path = [ 'C', ...qubicToCurve(d.x, d.y, path[1], path[2], path[3], path[4]) ];\n          break;\n        case 'L':\n          path = [ 'C', ...lineToCurve(d.x, d.y, path[1], path[2]) ];\n          break;\n        case 'H':\n          path = [ 'C', ...lineToCurve(d.x, d.y, path[1], d.y) ];\n          break;\n        case 'V':\n          path = [ 'C', ...lineToCurve(d.x, d.y, d.x, path[1]) ];\n          break;\n        case 'Z':\n          path = [ 'C', ...lineToCurve(d.x, d.y, d.X, d.Y) ];\n          break;\n        }\n\n        return path;\n      },\n\n      fixArc = function(pp, i) {\n\n        if (pp[i].length > 7) {\n          pp[i].shift();\n          var pi = pp[i];\n\n          while (pi.length) {\n            pathCommands[i] = 'A'; // if created multiple C:s, their original seg is saved\n            pp.splice(i++, 0, [ 'C', ...pi.splice(0, 6) ]);\n          }\n\n          pp.splice(i, 1);\n          ii = curvedPath.length;\n        }\n      },\n\n      pathCommands = [], // path commands of original path p\n      pfirst = '', // temporary holder for original path command\n      pathCommand = ''; // holder for previous path command of original path\n\n  for (var i = 0, ii = curvedPath.length; i < ii; i++) {\n    curvedPath[i] && (pfirst = curvedPath[i][0]); // save current path command\n\n    if (pfirst != 'C') // C is not saved yet, because it may be result of conversion\n    {\n      pathCommands[i] = pfirst; // Save current path command\n      i && (pathCommand = pathCommands[i - 1]); // Get previous path command pathCommand\n    }\n    curvedPath[i] = processPath(curvedPath[i], attrs, pathCommand); // Previous path command is inputted to processPath\n\n    if (pathCommands[i] != 'A' && pfirst == 'C') pathCommands[i] = 'C'; // A is the only command\n    // which may produce multiple C:s\n    // so we have to make sure that C is also C in original path\n\n    fixArc(curvedPath, i); // fixArc adds also the right amount of A:s to pathCommands\n\n    var seg = curvedPath[i],\n        seglen = seg.length;\n\n    attrs.x = seg[seglen - 2];\n    attrs.y = seg[seglen - 1];\n    attrs.bx = toFloat(seg[seglen - 4]) || attrs.x;\n    attrs.by = toFloat(seg[seglen - 3]) || attrs.y;\n  }\n\n  // cache curve\n  pth.curve = pathClone(curvedPath);\n\n  return curvedPath;\n}", "import {\n  has,\n  isNil,\n  isObject\n} from 'min-dash';\n\n/**\n * Checks whether a value is an instance of Connection.\n *\n * @param {any} value\n *\n * @return {boolean}\n */\nexport function isConnection(value) {\n  return isObject(value) && has(value, 'waypoints');\n}\n\n/**\n * Checks whether a value is an instance of Label.\n *\n * @param {any} value\n *\n * @return {boolean}\n */\nexport function isLabel(value) {\n  return isObject(value) && has(value, 'labelTarget');\n}\n\n/**\n * Checks whether a value is an instance of Root.\n *\n * @param {any} value\n *\n * @return {boolean}\n */\nexport function isRoot(value) {\n  return isObject(value) && isNil(value.parent);\n}", "import {\n  isObject,\n  sortBy\n} from 'min-dash';\n\nimport {\n  pointDistance,\n  pointsOnLine\n} from '../util/Geometry';\n\nimport intersectPaths from 'path-intersection';\n\nimport { isConnection } from '../util/ModelUtil';\n\n/**\n * @typedef {import('../core/Types').ElementLike} Element\n * @typedef {import('../core/Types').ConnectionLike} Connection\n *\n * @typedef {import('../util/Types').DirectionTRBL} DirectionTRBL\n * @typedef {import('../util/Types').Intersection} Intersection\n * @typedef {import('../util/Types').Point} Point\n * @typedef {import('../util/Types').Rect} Rect\n * @typedef {import('../util/Types').RectTRBL} RectTRBL\n */\n\n/**\n * @param {Rect} bounds\n *\n * @returns {Rect}\n */\nexport function roundBounds(bounds) {\n  return {\n    x: Math.round(bounds.x),\n    y: Math.round(bounds.y),\n    width: Math.round(bounds.width),\n    height: Math.round(bounds.height)\n  };\n}\n\n/**\n * @param {Point} point\n *\n * @returns {Point}\n */\nexport function roundPoint(point) {\n\n  return {\n    x: Math.round(point.x),\n    y: Math.round(point.y)\n  };\n}\n\n\n/**\n * Convert the given bounds to a { top, left, bottom, right } descriptor.\n *\n * @param {Point|Rect} bounds\n *\n * @return {RectTRBL}\n */\nexport function asTRBL(bounds) {\n  return {\n    top: bounds.y,\n    right: bounds.x + (bounds.width || 0),\n    bottom: bounds.y + (bounds.height || 0),\n    left: bounds.x\n  };\n}\n\n\n/**\n * Convert a { top, left, bottom, right } to an objects bounds.\n *\n * @param {RectTRBL} trbl\n *\n * @return {Rect}\n */\nexport function asBounds(trbl) {\n  return {\n    x: trbl.left,\n    y: trbl.top,\n    width: trbl.right - trbl.left,\n    height: trbl.bottom - trbl.top\n  };\n}\n\n\n/**\n * Get the mid of the given bounds or point.\n *\n * @param {Point|Rect} bounds\n *\n * @return {Point}\n */\nexport function getBoundsMid(bounds) {\n  return roundPoint({\n    x: bounds.x + (bounds.width || 0) / 2,\n    y: bounds.y + (bounds.height || 0) / 2\n  });\n}\n\n\n/**\n * Get the mid of the given Connection.\n *\n * @param {Connection} connection\n *\n * @return {Point}\n */\nexport function getConnectionMid(connection) {\n  var waypoints = connection.waypoints;\n\n  // calculate total length and length of each segment\n  var parts = waypoints.reduce(function(parts, point, index) {\n\n    var lastPoint = waypoints[index - 1];\n\n    if (lastPoint) {\n      var lastPart = parts[parts.length - 1];\n\n      var startLength = lastPart && lastPart.endLength || 0;\n      var length = distance(lastPoint, point);\n\n      parts.push({\n        start: lastPoint,\n        end: point,\n        startLength: startLength,\n        endLength: startLength + length,\n        length: length\n      });\n    }\n\n    return parts;\n  }, []);\n\n  var totalLength = parts.reduce(function(length, part) {\n    return length + part.length;\n  }, 0);\n\n  // find which segement contains middle point\n  var midLength = totalLength / 2;\n\n  var i = 0;\n  var midSegment = parts[i];\n\n  while (midSegment.endLength < midLength) {\n    midSegment = parts[++i];\n  }\n\n  // calculate relative position on mid segment\n  var segmentProgress = (midLength - midSegment.startLength) / midSegment.length;\n\n  var midPoint = {\n    x: midSegment.start.x + (midSegment.end.x - midSegment.start.x) * segmentProgress,\n    y: midSegment.start.y + (midSegment.end.y - midSegment.start.y) * segmentProgress\n  };\n\n  return midPoint;\n}\n\n\n/**\n * Get the mid of the given Element.\n *\n * @param {Element} element\n *\n * @return {Point}\n */\nexport function getMid(element) {\n  if (isConnection(element)) {\n    return getConnectionMid(element);\n  }\n\n  return getBoundsMid(element);\n}\n\n// orientation utils //////////////////////\n\n/**\n * Get orientation of the given rectangle with respect to\n * the reference rectangle.\n *\n * A padding (positive or negative) may be passed to influence\n * horizontal / vertical orientation and intersection.\n *\n * @param {Rect} rect\n * @param {Rect} reference\n * @param {Point|number} padding\n *\n * @return {DirectionTRBL|Intersection} the orientation; one of top, top-left, left, ..., bottom, right or intersect.\n */\nexport function getOrientation(rect, reference, padding) {\n\n  padding = padding || 0;\n\n  // make sure we can use an object, too\n  // for individual { x, y } padding\n  if (!isObject(padding)) {\n    padding = { x: padding, y: padding };\n  }\n\n\n  var rectOrientation = asTRBL(rect),\n      referenceOrientation = asTRBL(reference);\n\n  var top = rectOrientation.bottom + padding.y <= referenceOrientation.top,\n      right = rectOrientation.left - padding.x >= referenceOrientation.right,\n      bottom = rectOrientation.top - padding.y >= referenceOrientation.bottom,\n      left = rectOrientation.right + padding.x <= referenceOrientation.left;\n\n  var vertical = top ? 'top' : (bottom ? 'bottom' : null),\n      horizontal = left ? 'left' : (right ? 'right' : null);\n\n  if (horizontal && vertical) {\n    return vertical + '-' + horizontal;\n  } else {\n    return horizontal || vertical || 'intersect';\n  }\n}\n\n\n// intersection utils //////////////////////\n\n/**\n * Get intersection between an element and a line path.\n *\n * @param {string} elementPath\n * @param {string} linePath\n * @param {boolean} cropStart Whether to crop start or end.\n *\n * @return {Point}\n */\nexport function getElementLineIntersection(elementPath, linePath, cropStart) {\n\n  var intersections = getIntersections(elementPath, linePath);\n\n  // recognize intersections\n  // only one -> choose\n  // two close together -> choose first\n  // two or more distinct -> pull out appropriate one\n  // none -> ok (fallback to point itself)\n  if (intersections.length === 1) {\n    return roundPoint(intersections[0]);\n  } else if (intersections.length === 2 && pointDistance(intersections[0], intersections[1]) < 1) {\n    return roundPoint(intersections[0]);\n  } else if (intersections.length > 1) {\n\n    // sort by intersections based on connection segment +\n    // distance from start\n    intersections = sortBy(intersections, function(i) {\n      var distance = Math.floor(i.t2 * 100) || 1;\n\n      distance = 100 - distance;\n\n      distance = (distance < 10 ? '0' : '') + distance;\n\n      // create a sort string that makes sure we sort\n      // line segment ASC + line segment position DESC (for cropStart)\n      // line segment ASC + line segment position ASC (for cropEnd)\n      return i.segment2 + '#' + distance;\n    });\n\n    return roundPoint(intersections[cropStart ? 0 : intersections.length - 1]);\n  }\n\n  return null;\n}\n\n\nexport function getIntersections(a, b) {\n  return intersectPaths(a, b);\n}\n\n\nexport function filterRedundantWaypoints(waypoints) {\n\n  // alter copy of waypoints, not original\n  waypoints = waypoints.slice();\n\n  var idx = 0,\n      point,\n      previousPoint,\n      nextPoint;\n\n  while (waypoints[idx]) {\n    point = waypoints[idx];\n    previousPoint = waypoints[idx - 1];\n    nextPoint = waypoints[idx + 1];\n\n    if (pointDistance(point, nextPoint) === 0 ||\n        pointsOnLine(previousPoint, nextPoint, point)) {\n\n      // remove point, if overlapping with {nextPoint}\n      // or on line with {previousPoint} -> {point} -> {nextPoint}\n      waypoints.splice(idx, 1);\n    } else {\n      idx++;\n    }\n  }\n\n  return waypoints;\n}\n\n// helpers //////////////////////\n\nfunction distance(a, b) {\n  return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));\n}", "import {\n  assign,\n  filter,\n  find,\n  isArray,\n  isNumber,\n  isObject,\n  isUndefined,\n  groupBy,\n  forEach\n} from 'min-dash';\n\n/**\n * @typedef {import('../model/Types').Connection} Connection\n * @typedef {import('../model/Types').Element} Element\n * @typedef {import('../model/Types').Shape} Shape\n *\n * @typedef {import('../util/Types').Rect} Rect\n *\n * @typedef { {\n *   allShapes: Record<string, Shape>,\n *   allConnections: Record<string, Connection>,\n *   topLevel: Record<string, Element>,\n *   enclosedConnections: Record<string, Connection>,\n *   enclosedElements: Record<string, Element>\n * } } Closure\n */\n\n/**\n * Get parent elements.\n *\n * @param {Element[]} elements\n *\n * @return {Element[]}\n */\nexport function getParents(elements) {\n\n  // find elements that are not children of any other elements\n  return filter(elements, function(element) {\n    return !find(elements, function(e) {\n      return e !== element && getParent(element, e);\n    });\n  });\n}\n\n\nfunction getParent(element, parent) {\n  if (!parent) {\n    return;\n  }\n\n  if (element === parent) {\n    return parent;\n  }\n\n  if (!element.parent) {\n    return;\n  }\n\n  return getParent(element.parent, parent);\n}\n\n\n/**\n * Adds an element to a collection and returns true if the\n * element was added.\n *\n * @param {Object[]} elements\n * @param {Object} element\n * @param {boolean} [unique]\n */\nexport function add(elements, element, unique) {\n  var canAdd = !unique || elements.indexOf(element) === -1;\n\n  if (canAdd) {\n    elements.push(element);\n  }\n\n  return canAdd;\n}\n\n\n/**\n * Iterate over each element in a collection, calling the iterator function `fn`\n * with (element, index, recursionDepth).\n *\n * Recurse into all elements that are returned by `fn`.\n *\n * @param {Element|Element[]} elements\n * @param {(element: Element, index: number, depth: number) => Element[] | boolean | undefined} fn\n * @param {number} [depth] maximum recursion depth\n */\nexport function eachElement(elements, fn, depth) {\n\n  depth = depth || 0;\n\n  if (!isArray(elements)) {\n    elements = [ elements ];\n  }\n\n  forEach(elements, function(s, i) {\n    var filter = fn(s, i, depth);\n\n    if (isArray(filter) && filter.length) {\n      eachElement(filter, fn, depth + 1);\n    }\n  });\n}\n\n\n/**\n * Collects self + child elements up to a given depth from a list of elements.\n *\n * @param {Element|Element[]} elements the elements to select the children from\n * @param {boolean} unique whether to return a unique result set (no duplicates)\n * @param {number} maxDepth the depth to search through or -1 for infinite\n *\n * @return {Element[]} found elements\n */\nexport function selfAndChildren(elements, unique, maxDepth) {\n  var result = [],\n      processedChildren = [];\n\n  eachElement(elements, function(element, i, depth) {\n    add(result, element, unique);\n\n    var children = element.children;\n\n    // max traversal depth not reached yet\n    if (maxDepth === -1 || depth < maxDepth) {\n\n      // children exist && children not yet processed\n      if (children && add(processedChildren, children, unique)) {\n        return children;\n      }\n    }\n  });\n\n  return result;\n}\n\n/**\n * Return self + direct children for a number of elements\n *\n * @param {Element[]} elements to query\n * @param {boolean} [allowDuplicates] to allow duplicates in the result set\n *\n * @return {Element[]} the collected elements\n */\nexport function selfAndDirectChildren(elements, allowDuplicates) {\n  return selfAndChildren(elements, !allowDuplicates, 1);\n}\n\n\n/**\n * Return self + ALL children for a number of elements\n *\n * @param {Element[]} elements to query\n * @param {boolean} [allowDuplicates] to allow duplicates in the result set\n *\n * @return {Element[]} the collected elements\n */\nexport function selfAndAllChildren(elements, allowDuplicates) {\n  return selfAndChildren(elements, !allowDuplicates, -1);\n}\n\n\n/**\n * Gets the the closure for all selected elements,\n * their enclosed children and connections.\n *\n * @param {Element[]} elements\n * @param {boolean} [isTopLevel=true]\n * @param {Closure} [closure]\n *\n * @return {Closure} newClosure\n */\nexport function getClosure(elements, isTopLevel, closure) {\n\n  if (isUndefined(isTopLevel)) {\n    isTopLevel = true;\n  }\n\n  if (isObject(isTopLevel)) {\n    closure = isTopLevel;\n    isTopLevel = true;\n  }\n\n\n  closure = closure || {};\n\n  var allShapes = copyObject(closure.allShapes),\n      allConnections = copyObject(closure.allConnections),\n      enclosedElements = copyObject(closure.enclosedElements),\n      enclosedConnections = copyObject(closure.enclosedConnections);\n\n  var topLevel = copyObject(\n    closure.topLevel,\n    isTopLevel && groupBy(elements, function(e) { return e.id; })\n  );\n\n\n  function handleConnection(c) {\n    if (topLevel[c.source.id] && topLevel[c.target.id]) {\n      topLevel[c.id] = [ c ];\n    }\n\n    // not enclosed as a child, but maybe logically\n    // (connecting two moved elements?)\n    if (allShapes[c.source.id] && allShapes[c.target.id]) {\n      enclosedConnections[c.id] = enclosedElements[c.id] = c;\n    }\n\n    allConnections[c.id] = c;\n  }\n\n  function handleElement(element) {\n\n    enclosedElements[element.id] = element;\n\n    if (element.waypoints) {\n\n      // remember connection\n      enclosedConnections[element.id] = allConnections[element.id] = element;\n    } else {\n\n      // remember shape\n      allShapes[element.id] = element;\n\n      // remember all connections\n      forEach(element.incoming, handleConnection);\n\n      forEach(element.outgoing, handleConnection);\n\n      // recurse into children\n      return element.children;\n    }\n  }\n\n  eachElement(elements, handleElement);\n\n  return {\n    allShapes: allShapes,\n    allConnections: allConnections,\n    topLevel: topLevel,\n    enclosedConnections: enclosedConnections,\n    enclosedElements: enclosedElements\n  };\n}\n\n/**\n * Returns the surrounding bbox for all elements in\n * the array or the element primitive.\n *\n * @param {Element|Element[]} elements\n * @param {boolean} [stopRecursion=false]\n *\n * @return {Rect}\n */\nexport function getBBox(elements, stopRecursion) {\n\n  stopRecursion = !!stopRecursion;\n  if (!isArray(elements)) {\n    elements = [ elements ];\n  }\n\n  var minX,\n      minY,\n      maxX,\n      maxY;\n\n  forEach(elements, function(element) {\n\n    // If element is a connection the bbox must be computed first\n    var bbox = element;\n    if (element.waypoints && !stopRecursion) {\n      bbox = getBBox(element.waypoints, true);\n    }\n\n    var x = bbox.x,\n        y = bbox.y,\n        height = bbox.height || 0,\n        width = bbox.width || 0;\n\n    if (x < minX || minX === undefined) {\n      minX = x;\n    }\n    if (y < minY || minY === undefined) {\n      minY = y;\n    }\n\n    if ((x + width) > maxX || maxX === undefined) {\n      maxX = x + width;\n    }\n    if ((y + height) > maxY || maxY === undefined) {\n      maxY = y + height;\n    }\n  });\n\n  return {\n    x: minX,\n    y: minY,\n    height: maxY - minY,\n    width: maxX - minX\n  };\n}\n\n\n/**\n * Returns all elements that are enclosed from the bounding box.\n *\n *   * If bbox.(width|height) is not specified the method returns\n *     all elements with element.x/y > bbox.x/y\n *   * If only bbox.x or bbox.y is specified, method return all elements with\n *     e.x > bbox.x or e.y > bbox.y\n *\n * @param {Element[]} elements List of Elements to search through\n * @param {Rect} bbox the enclosing bbox.\n *\n * @return {Element[]} enclosed elements\n */\nexport function getEnclosedElements(elements, bbox) {\n\n  var filteredElements = {};\n\n  forEach(elements, function(element) {\n\n    var e = element;\n\n    if (e.waypoints) {\n      e = getBBox(e);\n    }\n\n    if (!isNumber(bbox.y) && (e.x > bbox.x)) {\n      filteredElements[element.id] = element;\n    }\n    if (!isNumber(bbox.x) && (e.y > bbox.y)) {\n      filteredElements[element.id] = element;\n    }\n    if (e.x > bbox.x && e.y > bbox.y) {\n      if (isNumber(bbox.width) && isNumber(bbox.height) &&\n          e.width + e.x < bbox.width + bbox.x &&\n          e.height + e.y < bbox.height + bbox.y) {\n\n        filteredElements[element.id] = element;\n      } else if (!isNumber(bbox.width) || !isNumber(bbox.height)) {\n        filteredElements[element.id] = element;\n      }\n    }\n  });\n\n  return filteredElements;\n}\n\n/**\n * Get the element's type\n *\n * @param {Element} element\n *\n * @return {'connection' | 'shape' | 'root'}\n */\nexport function getType(element) {\n\n  if ('waypoints' in element) {\n    return 'connection';\n  }\n\n  if ('x' in element) {\n    return 'shape';\n  }\n\n  return 'root';\n}\n\n/**\n * @param {Element} element\n *\n * @return {boolean}\n */\nexport function isFrameElement(element) {\n  return !!(element && element.isFrame);\n}\n\n// helpers ///////////////////////////////\n\nfunction copyObject(src1, src2) {\n  return assign({}, src1 || {}, src2 || {});\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAkBO,SAAS,cAAc,GAAG,GAAG;AAClC,MAAI,CAAC,KAAK,CAAC,GAAG;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,KAAK;AAAA,IACV,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IACrB,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,EACvB;AACF;AAaO,SAAS,aAAa,GAAG,GAAG,GAAG,UAAU;AAE9C,MAAI,OAAO,aAAa,aAAa;AACnC,eAAW;AAAA,EACb;AAEA,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;AAClB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IACzD,OAAO,cAAc,GAAG,CAAC;AAG7B,SAAO,KAAK,IAAI,MAAM,IAAI,KAAK;AACjC;AAGA,IAAI,oBAAoB;AAUjB,SAAS,cAAc,GAAG,GAAG;AAClC,MAAI,SAAS,MAAM,KAAK,SAAS,EAAE,KAAK;AAExC,QAAM,UAAU;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,aAAW,CAAE,MAAM,WAAY,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC3D,QAAI,oBAAoB,MAAM,MAAM,GAAG;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAQO,SAAS,oBAAoB,MAAM,QAAQ;AAChD,QAAM,iBAAiB,OAAO,CAAC;AAE/B,SAAO,MAAM,QAAQ,SAAS,OAAO;AACnC,WAAO,KAAK,IAAI,eAAe,IAAI,IAAI,MAAM,IAAI,CAAC,KAAK;AAAA,EACzD,CAAC;AACH;AAWO,SAAS,YAAY,GAAG,MAAM,WAAW;AAC9C,cAAY,aAAa;AAEzB,SAAO,EAAE,IAAI,KAAK,IAAI,aACf,EAAE,IAAI,KAAK,IAAI,aACf,EAAE,IAAI,KAAK,IAAI,KAAK,QAAQ,aAC5B,EAAE,IAAI,KAAK,IAAI,KAAK,SAAS;AACtC;AAUO,SAAS,YAAY,GAAG,GAAG;AAChC,SAAO;AAAA,IACL,GAAG,KAAK,MAAM,EAAE,KAAM,EAAE,IAAI,EAAE,KAAK,CAAI;AAAA,IACvC,GAAG,KAAK,MAAM,EAAE,KAAM,EAAE,IAAI,EAAE,KAAK,CAAI;AAAA,EACzC;AACF;;;AC1HA,IAAI,MAAM;AAAV,IACI,UAAU;AADd,IAEI,OAAO;AAFX,IAGI,KAAK,KAAK;AAHd,IAII,OAAO,KAAK;AAJhB,IAKI,OAAO,KAAK;AALhB,IAMI,MAAM,KAAK;AANf,IAOI,MAAM,KAAK;AAPf,IAQI,cAAc;AARlB,IASI,aAAa;AAEjB,IAAIA,WAAU,MAAM,WAAW,SAAS,GAAG;AAAE,SAAO,aAAa;AAAO;AAExE,SAAS,YAAY,KAAK,UAAU;AAClC,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,QAAQ;AAC3D;AAEA,SAAS,MAAM,KAAK;AAElB,MAAI,OAAO,OAAO,cAAc,OAAO,GAAG,MAAM,KAAK;AACnD,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,IAAI,IAAI;AAElB,WAAS,OAAO,KAAK;AACnB,QAAI,YAAY,KAAK,GAAG,GAAG;AACzB,UAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,OAAO,OAAO,MAAM;AAC3B,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI;AAAK,QAAI,MAAM,CAAC,MAAM,MAAM;AACrE,aAAO,MAAM,KAAK,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,IACzC;AACF;AAEA,SAAS,OAAO,GAAG;AAEjB,WAAS,OAAO;AAEd,QAAI,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC,GAC7C,OAAO,IAAI,KAAK,GAAQ,GACxB,QAAQ,KAAK,QAAQ,KAAK,SAAS,CAAC,GACpC,QAAQ,KAAK,QAAQ,KAAK,SAAS,CAAC;AAExC,QAAI,YAAY,OAAO,IAAI,GAAG;AAC5B,aAAO,OAAO,IAAI;AAClB,aAAO,MAAM,IAAI;AAAA,IACnB;AAEA,UAAM,UAAU,OAAO,OAAO,MAAM,MAAM,MAAM,CAAC;AACjD,UAAM,KAAK,IAAI;AACf,UAAM,IAAI,IAAI,EAAE,GAAG,SAAS;AAE5B,WAAO,MAAM,IAAI;AAAA,EACnB;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,YAAY;AAEnC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,MAAM,UAAU;AAE1B,MAAI,IAAI,KAAK;AACX,WAAO,MAAM,IAAI,GAAG;AAAA,EACtB;AAEA,MAAI,cAAc,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAC3E,OAAO,CAAC;AAEZ,MAAIA,SAAQ,UAAU,KAAKA,SAAQ,WAAW,CAAC,CAAC,GAAG;AACjD,WAAO,MAAM,UAAU;AAAA,EACzB;AAEA,MAAI,CAAC,KAAK,QAAQ;AAEhB,WAAO,UAAU,EAAE,QAAQ,aAAa,SAAS,GAAG,GAAG,GAAG;AACxD,UAAI,SAAS,CAAC,GACV,OAAO,EAAE,YAAY;AAEzB,QAAE,QAAQ,YAAY,SAASC,IAAGC,IAAG;AACnC,QAAAA,MAAK,OAAO,KAAK,CAACA,EAAC;AAAA,MACrB,CAAC;AAED,UAAI,QAAQ,OAAO,OAAO,SAAS,GAAG;AACpC,aAAK,KAAK,CAAE,GAAG,GAAG,OAAO,OAAO,GAAG,CAAC,CAAE,CAAC;AACvC,eAAO;AACP,YAAI,KAAK,MAAM,MAAM;AAAA,MACvB;AAEA,aAAO,OAAO,UAAU,YAAY,IAAI,GAAG;AACzC,aAAK,KAAK,CAAE,GAAG,GAAG,OAAO,OAAO,GAAG,YAAY,IAAI,CAAC,CAAE,CAAC;AACvD,YAAI,CAAC,YAAY,IAAI,GAAG;AACtB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,OAAK,WAAW,MAAM;AACtB,MAAI,MAAM,MAAM,IAAI;AAEpB,SAAO;AACT;AAEA,SAAS,MAAM,IAAI;AACjB,MAAI,IAAI,MAAM,KAAK,MAAM,MAAM,CAAC;AAEhC,MAAI,EAAE,EAAE,GAAG;AACT,MAAE,EAAE,EAAE,QAAQ;AAAA,EAChB,OAAO;AACL,MAAE,EAAE,IAAI;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAEA,aAAW,WAAW;AACpB,aAAS,OAAO,GAAG;AACjB,UAAI,YAAY,GAAG,GAAG,KAAK,OAAO,IAAI;AACpC,UAAE,GAAG,EAAE;AACP,SAAC,EAAE,GAAG,EAAE,SAAS,OAAO,EAAE,GAAG;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO,EAAE,EAAE;AACb;AAEA,SAAS,SAAS,GAAG,GAAG,OAAO,QAAQ;AAErC,MAAI,UAAU,WAAW,GAAG;AAC1B,QAAI,EAAE;AACN,YAAQ,EAAE;AACV,aAAS,EAAE;AACX,QAAI,EAAE;AAAA,EACR;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI,IAAI;AAAA,IACR,IAAI,IAAI;AAAA,EACV;AACF;AAEA,SAAS,eAAe;AACtB,SAAO,KAAK,KAAK,GAAG,EAAE,QAAQ,KAAK,IAAI;AACzC;AAEA,SAAS,UAAU,WAAW;AAC5B,MAAI,MAAM,MAAM,SAAS;AACzB,MAAI,WAAW;AACf,SAAO;AACT;AAEA,SAAS,kBAAkB,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACpE,MAAI,KAAK,IAAI,GACT,MAAM,IAAI,IAAI,CAAC,GACf,MAAM,IAAI,IAAI,CAAC,GACf,KAAK,IAAI,GACT,KAAK,KAAK,GACV,IAAI,MAAM,MAAM,MAAM,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,MAAM,KAAK,KAChE,IAAI,MAAM,MAAM,MAAM,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,MAAM,KAAK;AAEpE,SAAO;AAAA,IACL,GAAG,SAAS,CAAC;AAAA,IACb,GAAG,SAAS,CAAC;AAAA,EACf;AACF;AAEA,SAAS,WAAW,QAAQ;AAE1B,MAAI,OAAO,UAAU,GAAG,MAAM;AAE9B,SAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,KAAK,KAAK;AAAA,IACf,KAAK,KAAK,KAAK;AAAA,EACjB;AACF;AAEA,SAAS,kBAAkB,MAAM,GAAG,GAAG;AACrC,SAAO,KAAK,KAAK,KACf,KAAK,KAAK,IAAI,KAAK,SACnB,KAAK,KAAK,KACV,KAAK,KAAK,IAAI,KAAK;AACvB;AAEA,SAAS,gBAAgB,OAAO,OAAO;AACrC,UAAQ,SAAS,KAAK;AACtB,UAAQ,SAAS,KAAK;AACtB,SAAO,kBAAkB,OAAO,MAAM,GAAG,MAAM,CAAC,KAC3C,kBAAkB,OAAO,MAAM,IAAI,MAAM,CAAC,KAC1C,kBAAkB,OAAO,MAAM,GAAG,MAAM,EAAE,KAC1C,kBAAkB,OAAO,MAAM,IAAI,MAAM,EAAE,KAC3C,kBAAkB,OAAO,MAAM,GAAG,MAAM,CAAC,KACzC,kBAAkB,OAAO,MAAM,IAAI,MAAM,CAAC,KAC1C,kBAAkB,OAAO,MAAM,GAAG,MAAM,EAAE,KAC1C,kBAAkB,OAAO,MAAM,IAAI,MAAM,EAAE,MAC1C,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,KACnC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,OACzC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,KACnC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM;AACjD;AAEA,SAAS,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI;AAChC,MAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IACrC,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;AACzC,SAAO,IAAI,KAAK,IAAI,KAAK,IAAI;AAC/B;AAEA,SAAS,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAEjD,MAAI,KAAK,MAAM;AACb,QAAI;AAAA,EACN;AAEA,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAE5B,MAAI,KAAK,IAAI,GACT,IAAI,IACJ,UAAU,CAAE,SAAO,QAAM,SAAO,QAAM,SAAO,QAAM,SAAO,QAAM,SAAO,QAAM,SAAO,MAAM,GAC1F,UAAU,CAAE,QAAO,QAAO,QAAO,QAAO,QAAO,QAAO,QAAO,QAAO,QAAO,QAAO,QAAO,MAAO,GAChG,MAAM;AAEV,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,KAAK,KAAK,QAAQ,CAAC,IAAI,IACvB,QAAQ,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,GAChC,QAAQ,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,GAChC,OAAO,QAAQ,QAAQ,QAAQ;AAEnC,WAAO,QAAQ,CAAC,IAAI,KAAK,KAAK,IAAI;AAAA,EACpC;AAEA,SAAO,KAAK;AACd;AAGA,SAAS,eAAe,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAEtD,MACE,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,KACxB,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,KAC1B,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,KAC1B,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,GAC5B;AACA;AAAA,EACF;AAEA,MAAI,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK,KACnE,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK,KACnE,eAAe,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK;AAE5D,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AAEA,MAAI,KAAK,SAAS,KAAK,WAAW,GAC9B,KAAK,SAAS,KAAK,WAAW,GAC9B,MAAM,CAAC,GAAG,QAAQ,CAAC,GACnB,MAAM,CAAC,GAAG,QAAQ,CAAC;AAEvB,MACE,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAC3B,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAC7B,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAC7B,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAC7B,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAC7B,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAC7B,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAC7B,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,GAC/B;AACA;AAAA,EACF;AAEA,SAAO,EAAE,GAAG,IAAI,GAAG,GAAG;AACxB;AAEA,SAAS,SAAS,QAAQ;AACxB,SAAO,KAAK,MAAM,SAAS,IAAY,IAAI;AAC7C;AAEA,SAAS,wBAAwB,MAAM,MAAM,WAAW;AACtD,MAAI,QAAQ,WAAW,IAAI,GACvB,QAAQ,WAAW,IAAI;AAE3B,MAAI,CAAC,gBAAgB,OAAO,KAAK,GAAG;AAClC,WAAO,YAAY,IAAI,CAAC;AAAA,EAC1B;AAIA,MAAI,KAAK,OAAO,GAAG,IAAI,GACnB,KAAK,OAAO,GAAG,IAAI,GACnB,KAAK,OAAO,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,GACtC,KAAK,OAAO,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,GACtC,QAAQ,CAAC,GACT,QAAQ,CAAC,GACT,KAAK,CAAC,GACN,MAAM,YAAY,IAAI,CAAC;AAE3B,WAAS,IAAI,GAAG,IAAI,KAAK,GAAG,KAAK;AAC/B,QAAI,IAAI,kBAAkB,GAAG,MAAM,IAAI,EAAE;AACzC,UAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,EAC1C;AAEA,OAAK,IAAI,GAAG,IAAI,KAAK,GAAG,KAAK;AAC3B,QAAI,kBAAkB,GAAG,MAAM,IAAI,EAAE;AACrC,UAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,EAC1C;AAEA,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AAEvB,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,UAAI,KAAK,MAAM,CAAC,GACZ,MAAM,MAAM,IAAI,CAAC,GACjB,KAAK,MAAM,CAAC,GACZ,MAAM,MAAM,IAAI,CAAC,GACjB,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,OAAM,MAAM,KACrC,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,OAAM,MAAM,KACrC,KAAK,eAAe,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GACtE;AAEJ,UAAI,IAAI;AACN,cAAM,GAAG,EAAE,QAAQ,CAAC,IAAI,MAAM,GAAG,EAAE,QAAQ,CAAC;AAE5C,YAAI,GAAG,GAAG,GAAG;AACX;AAAA,QACF;AAEA,WAAG,GAAG,IAAI;AAEV,YAAI,KAAK,GAAG,IAAI,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,KAAK,IAAI,IAAI,GAAG,IACtE,KAAK,GAAG,IAAI,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,KAAK,IAAI,IAAI,GAAG;AAE1E,YAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AAE5C,cAAI,WAAW;AACb;AAAA,UACF,OAAO;AACL,gBAAI,KAAK;AAAA,cACP,GAAG,GAAG;AAAA,cACN,GAAG,GAAG;AAAA,cACN;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAiCe,SAAR,sBAAuC,OAAO,OAAO,WAAW;AACrE,UAAQ,YAAY,KAAK;AACzB,UAAQ,YAAY,KAAK;AAEzB,MAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,MAC1C,MAAM,YAAY,IAAI,CAAC;AAE3B,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,QAAI,KAAK,MAAM,CAAC;AAEhB,QAAI,GAAG,CAAC,KAAK,KAAK;AAChB,WAAK,MAAM,GAAG,CAAC;AACf,WAAK,MAAM,GAAG,CAAC;AAAA,IACjB,OAAO;AAEL,UAAI,GAAG,CAAC,KAAK,KAAK;AAChB,eAAO,CAAE,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAE;AAChC,aAAK,KAAK,CAAC;AACX,aAAK,KAAK,CAAC;AAAA,MACb,OAAO;AACL,eAAO,CAAE,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,GAAI;AAC5C,aAAK;AACL,aAAK;AAAA,MACP;AAEA,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,YAAI,KAAK,MAAM,CAAC;AAEhB,YAAI,GAAG,CAAC,KAAK,KAAK;AAChB,eAAK,MAAM,GAAG,CAAC;AACf,eAAK,MAAM,GAAG,CAAC;AAAA,QACjB,OAAO;AAEL,cAAI,GAAG,CAAC,KAAK,KAAK;AAChB,mBAAO,CAAE,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAE;AAChC,iBAAK,KAAK,CAAC;AACX,iBAAK,KAAK,CAAC;AAAA,UACb,OAAO;AACL,mBAAO,CAAE,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,GAAI;AAC5C,iBAAK;AACL,iBAAK;AAAA,UACP;AAEA,cAAI,OAAO,wBAAwB,MAAM,MAAM,SAAS;AAExD,cAAI,WAAW;AACb,mBAAO;AAAA,UACT,OAAO;AAEL,qBAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,mBAAK,CAAC,EAAE,WAAW;AACnB,mBAAK,CAAC,EAAE,WAAW;AACnB,mBAAK,CAAC,EAAE,OAAO;AACf,mBAAK,CAAC,EAAE,OAAO;AAAA,YACjB;AAEA,kBAAM,IAAI,OAAO,IAAI;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAGA,SAAS,eAAe,WAAW;AACjC,MAAI,MAAM,MAAM,SAAS;AAEzB,MAAI,IAAI,KAAK;AACX,WAAO,UAAU,IAAI,GAAG;AAAA,EAC1B;AAEA,MAAI,CAACF,SAAQ,SAAS,KAAK,CAACA,SAAQ,aAAa,UAAU,CAAC,CAAC,GAAG;AAC9D,gBAAY,gBAAgB,SAAS;AAAA,EACvC;AAEA,MAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACnC,WAAO,CAAE,CAAE,KAAK,GAAG,CAAE,CAAE;AAAA,EACzB;AAEA,MAAI,MAAM,CAAC,GACP,IAAI,GACJ,IAAI,GACJ,KAAK,GACL,KAAK,GACL,QAAQ,GACR;AAEJ,MAAI,UAAU,CAAC,EAAE,CAAC,KAAK,KAAK;AAC1B,QAAI,CAAC,UAAU,CAAC,EAAE,CAAC;AACnB,QAAI,CAAC,UAAU,CAAC,EAAE,CAAC;AACnB,SAAK;AACL,SAAK;AACL;AACA,QAAI,CAAC,IAAI,CAAE,KAAK,GAAG,CAAE;AAAA,EACvB;AAEA,WAAS,GAAG,IAAI,IAAI,OAAO,KAAK,UAAU,QAAQ,IAAI,IAAI,KAAK;AAC7D,QAAI,KAAK,IAAI,CAAC,CAAC;AACf,SAAK,UAAU,CAAC;AAChB,UAAM,GAAG,CAAC;AAEV,QAAI,OAAO,IAAI,YAAY,GAAG;AAC5B,QAAE,CAAC,IAAI,IAAI,YAAY;AAEvB,cAAQ,EAAE,CAAC,GAAG;AAAA,QACd,KAAK;AACH,YAAE,CAAC,IAAI,GAAG,CAAC;AACX,YAAE,CAAC,IAAI,GAAG,CAAC;AACX,YAAE,CAAC,IAAI,GAAG,CAAC;AACX,YAAE,CAAC,IAAI,GAAG,CAAC;AACX,YAAE,CAAC,IAAI,GAAG,CAAC;AACX,YAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI;AAChB,YAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI;AAChB;AAAA,QACF,KAAK;AACH,YAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI;AAChB;AAAA,QACF,KAAK;AACH,YAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI;AAChB;AAAA,QACF,KAAK;AACH,eAAK,CAAC,GAAG,CAAC,IAAI;AACd,eAAK,CAAC,GAAG,CAAC,IAAI;AAAA,QAChB;AACE,mBAAS,IAAI,GAAG,KAAK,GAAG,QAAQ,IAAI,IAAI,KAAK;AAC3C,cAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAM,IAAI,IAAK,IAAI;AAAA,UACjC;AAAA,MACF;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,KAAK,GAAG,QAAQ,IAAI,IAAI,KAAK;AAC3C,UAAE,CAAC,IAAI,GAAG,CAAC;AAAA,MACb;AAAA,IACF;AACA,UAAM,IAAI,YAAY;AAEtB,YAAQ,EAAE,CAAC,GAAG;AAAA,MACd,KAAK;AACH,YAAI,CAAC;AACL,YAAI,CAAC;AACL;AAAA,MACF,KAAK;AACH,YAAI,EAAE,CAAC;AACP;AAAA,MACF,KAAK;AACH,YAAI,EAAE,CAAC;AACP;AAAA,MACF,KAAK;AACH,aAAK,EAAE,EAAE,SAAS,CAAC;AACnB,aAAK,EAAE,EAAE,SAAS,CAAC;AAAA,MACrB;AACE,YAAI,EAAE,EAAE,SAAS,CAAC;AAClB,YAAI,EAAE,EAAE,SAAS,CAAC;AAAA,IACpB;AAAA,EACF;AAEA,MAAI,WAAW;AACf,MAAI,MAAM,UAAU,GAAG;AAEvB,SAAO;AACT;AAEA,SAAS,OAAO,KAAK;AACnB,SACE,IAAI,CAAC,MAAM,IAAI,CAAC,KAChB,IAAI,CAAC,MAAM,IAAI,CAAC,KAChB,IAAI,CAAC,MAAM,IAAI,CAAC,KAChB,IAAI,CAAC,MAAM,IAAI,CAAC;AAEpB;AAEA,SAAS,YAAY,IAAI,IAAI,IAAI,IAAI;AACnC,SAAO;AAAA,IACL;AAAA,IAAI;AAAA,IAAI;AAAA,IACR;AAAA,IAAI;AAAA,IAAI;AAAA,EACV;AACF;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC5C,MAAI,MAAM,IAAI,GACV,MAAM,IAAI;AAEd,SAAO;AAAA,IACL,MAAM,KAAK,MAAM;AAAA,IACjB,MAAM,KAAK,MAAM;AAAA,IACjB,MAAM,KAAK,MAAM;AAAA,IACjB,MAAM,KAAK,MAAM;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,IAAI,IAAI,IAAI,IAAI,OAAO,gBAAgB,YAAY,IAAI,IAAI,WAAW;AAIxF,MAAI,OAAO,KAAK,MAAM,KAClB,MAAM,KAAK,OAAO,CAAC,SAAS,IAC5B,MAAM,CAAC,GACP,IACA,SAAS,OAAO,SAASG,IAAGC,IAAGC,MAAK;AAClC,QAAI,IAAIF,KAAI,KAAK,IAAIE,IAAG,IAAID,KAAI,KAAK,IAAIC,IAAG,GACxC,IAAIF,KAAI,KAAK,IAAIE,IAAG,IAAID,KAAI,KAAK,IAAIC,IAAG;AAE5C,WAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,EACtB,CAAC;AAEL,MAAI,CAAC,WAAW;AACd,SAAK,OAAO,IAAI,IAAI,CAAC,GAAG;AACxB,SAAK,GAAG;AACR,SAAK,GAAG;AACR,SAAK,OAAO,IAAI,IAAI,CAAC,GAAG;AACxB,SAAK,GAAG;AACR,SAAK,GAAG;AAER,QAAI,KAAK,KAAK,MAAM,GAChB,KAAK,KAAK,MAAM;AAEpB,QAAI,IAAK,IAAI,KAAM,KAAK,MAAO,IAAI,KAAM,KAAK;AAE9C,QAAI,IAAI,GAAG;AACT,UAAI,KAAK,KAAK,CAAC;AACf,WAAK,IAAI;AACT,WAAK,IAAI;AAAA,IACX;AAEA,QAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,KAAK,kBAAkB,aAAa,KAAK,KACrC,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,MAAM,IAAI,EAAE,CAAC,GACxF,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,MAAM,GACnC,KAAK,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK,MAAM,GACpC,KAAK,KAAK,OAAO,KAAK,MAAM,IAAI,QAAQ,CAAC,CAAC,GAC1C,KAAK,KAAK,OAAO,KAAK,MAAM,IAAI,QAAQ,CAAC,CAAC;AAE9C,SAAK,KAAK,KAAK,KAAK,KAAK;AACzB,SAAK,KAAK,KAAK,KAAK,KAAK;AACzB,SAAK,MAAM,KAAK,KAAK,IAAI;AACzB,SAAK,MAAM,KAAK,KAAK,IAAI;AAEzB,QAAI,cAAc,KAAK,IAAI;AACzB,WAAK,KAAK,KAAK;AAAA,IACjB;AACA,QAAI,CAAC,cAAc,KAAK,IAAI;AAC1B,WAAK,KAAK,KAAK;AAAA,IACjB;AAAA,EACF,OAAO;AACL,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAAA,EAClB;AAEA,MAAI,KAAK,KAAK;AAEd,MAAI,IAAI,EAAE,IAAI,MAAM;AAClB,QAAI,QAAQ,IACR,QAAQ,IACR,QAAQ;AAEZ,SAAK,KAAK,QAAQ,cAAc,KAAK,KAAK,IAAI;AAC9C,SAAK,KAAK,KAAK,KAAK,IAAI,EAAE;AAC1B,SAAK,KAAK,KAAK,KAAK,IAAI,EAAE;AAC1B,UAAM,WAAW,IAAI,IAAI,IAAI,IAAI,OAAO,GAAG,YAAY,OAAO,OAAO,CAAE,IAAI,OAAO,IAAI,EAAG,CAAC;AAAA,EAC5F;AAEA,OAAK,KAAK;AAEV,MAAI,KAAK,KAAK,IAAI,EAAE,GAChB,KAAK,KAAK,IAAI,EAAE,GAChB,KAAK,KAAK,IAAI,EAAE,GAChB,KAAK,KAAK,IAAI,EAAE,GAChB,IAAI,KAAK,IAAI,KAAK,CAAC,GACnB,KAAK,IAAI,IAAI,KAAK,GAClB,KAAK,IAAI,IAAI,KAAK,GAClB,KAAK,CAAE,IAAI,EAAG,GACd,KAAK,CAAE,KAAK,KAAK,IAAI,KAAK,KAAK,EAAG,GAClC,KAAK,CAAE,KAAK,KAAK,IAAI,KAAK,KAAK,EAAG,GAClC,KAAK,CAAE,IAAI,EAAG;AAElB,KAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACxB,KAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAExB,MAAI,WAAW;AACb,WAAO,CAAE,IAAI,IAAI,EAAG,EAAE,OAAO,GAAG;AAAA,EAClC,OAAO;AACL,UAAM,CAAE,IAAI,IAAI,EAAG,EAAE,OAAO,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG;AACjD,QAAI,SAAS,CAAC;AAEd,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,aAAO,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE;AAAA,IAC1F;AAEA,WAAO;AAAA,EACT;AACF;AAMA,SAAS,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACjD,MAAI,UAAU,CAAC,GACX,SAAS,CAAE,CAAC,GAAG,CAAC,CAAE,GAClB,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,MAAM;AAE9B,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAE1B,QAAI,KAAK,GAAG;AACV,UAAI,IAAI,KAAK,KAAK,KAAK,IAAI;AAC3B,UAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACpC,UAAI,IAAI,KAAK,IAAI;AAAA,IACnB,OAAO;AACL,UAAI,IAAI,KAAK,KAAK,KAAK,IAAI;AAC3B,UAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACpC,UAAI,IAAI,KAAK,IAAI;AAAA,IACnB;AAEA,QAAI,IAAI,CAAC,IAAI,OAAO;AAElB,UAAI,IAAI,CAAC,IAAI,OAAO;AAClB;AAAA,MACF;AAEA,UAAI,CAAC,IAAI;AAET,UAAI,IAAI,KAAK,IAAI,GAAG;AAClB,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAEA;AAAA,IACF;AAEA,WAAO,IAAI,IAAI,IAAI,IAAI;AACvB,eAAW,KAAK,KAAK,IAAI;AAEzB,QAAI,OAAO,GAAG;AACZ;AAAA,IACF;AAEA,UAAM,CAAC,IAAI,aAAa,IAAI;AAE5B,QAAI,IAAI,MAAM,KAAK,GAAG;AACpB,cAAQ,KAAK,EAAE;AAAA,IACjB;AAEA,UAAM,CAAC,IAAI,aAAa,IAAI;AAE5B,QAAI,IAAI,MAAM,KAAK,GAAG;AACpB,cAAQ,KAAK,EAAE;AAAA,IACjB;AAAA,EACF;AAEA,MAAI,IAAI,QAAQ,QACZ,OAAO,GACP;AAEJ,SAAO,KAAK;AACV,QAAI,QAAQ,CAAC;AACb,SAAK,IAAI;AACT,WAAO,CAAC,EAAE,CAAC,IAAK,KAAK,KAAK,KAAK,KAAO,IAAI,KAAK,KAAK,IAAI,KAAO,IAAI,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,IAAI;AACnG,WAAO,CAAC,EAAE,CAAC,IAAK,KAAK,KAAK,KAAK,KAAO,IAAI,KAAK,KAAK,IAAI,KAAO,IAAI,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,IAAI;AAAA,EACrG;AAEA,SAAO,CAAC,EAAE,IAAI,IAAI;AAClB,SAAO,CAAC,EAAE,IAAI,IAAI;AAClB,SAAO,CAAC,EAAE,OAAO,CAAC,IAAI;AACtB,SAAO,CAAC,EAAE,OAAO,CAAC,IAAI;AACtB,SAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,SAAS,OAAO;AAE7C,SAAO;AAAA,IACL,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC;AAAA,IACrB,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC;AAAA,IACrB,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC;AAAA,IACrB,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC;AAAA,EACvB;AACF;AAEA,SAAS,YAAY,MAAM;AAEzB,MAAI,MAAM,MAAM,IAAI;AAGpB,MAAI,IAAI,OAAO;AACb,WAAO,UAAU,IAAI,KAAK;AAAA,EAC5B;AAEA,MAAI,aAAa,eAAe,IAAI,GAChC,QAAQ,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,MAAM,IAAI,KAAK,GACnE,cAAc,SAASC,OAAM,GAAGC,cAAa;AAC3C,QAAI,IAAI;AAER,QAAI,CAACD,OAAM;AACT,aAAO,CAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAE;AAAA,IAC7C;AAEA,MAAEA,MAAK,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK;AAE/C,YAAQA,MAAK,CAAC,GAAG;AAAA,MACjB,KAAK;AACH,UAAE,IAAIA,MAAK,CAAC;AACZ,UAAE,IAAIA,MAAK,CAAC;AACZ;AAAA,MACF,KAAK;AACH,QAAAA,QAAO,CAAE,KAAK,GAAG,WAAW,EAAE,GAAG,EAAE,GAAG,GAAGA,MAAK,MAAM,CAAC,CAAC,CAAE;AACxD;AAAA,MACF,KAAK;AACH,YAAIC,gBAAe,OAAOA,gBAAe,KAAK;AAG5C,eAAK,EAAE,IAAI,IAAI,EAAE;AAGjB,eAAK,EAAE,IAAI,IAAI,EAAE;AAAA,QAGnB,OACK;AAGH,eAAK,EAAE;AACP,eAAK,EAAE;AAAA,QACT;AACA,QAAAD,QAAO,CAAE,KAAK,IAAI,IAAI,GAAGA,MAAK,MAAM,CAAC,CAAE;AACvC;AAAA,MACF,KAAK;AACH,YAAIC,gBAAe,OAAOA,gBAAe,KAAK;AAG5C,YAAE,KAAK,EAAE,IAAI,IAAI,EAAE;AAGnB,YAAE,KAAK,EAAE,IAAI,IAAI,EAAE;AAAA,QAGrB,OACK;AAGH,YAAE,KAAK,EAAE;AACT,YAAE,KAAK,EAAE;AAAA,QACX;AACA,QAAAD,QAAO,CAAE,KAAK,GAAG,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAIA,MAAK,CAAC,GAAGA,MAAK,CAAC,CAAC,CAAE;AACtE;AAAA,MACF,KAAK;AACH,UAAE,KAAKA,MAAK,CAAC;AACb,UAAE,KAAKA,MAAK,CAAC;AACb,QAAAA,QAAO,CAAE,KAAK,GAAG,aAAa,EAAE,GAAG,EAAE,GAAGA,MAAK,CAAC,GAAGA,MAAK,CAAC,GAAGA,MAAK,CAAC,GAAGA,MAAK,CAAC,CAAC,CAAE;AAC5E;AAAA,MACF,KAAK;AACH,QAAAA,QAAO,CAAE,KAAK,GAAG,YAAY,EAAE,GAAG,EAAE,GAAGA,MAAK,CAAC,GAAGA,MAAK,CAAC,CAAC,CAAE;AACzD;AAAA,MACF,KAAK;AACH,QAAAA,QAAO,CAAE,KAAK,GAAG,YAAY,EAAE,GAAG,EAAE,GAAGA,MAAK,CAAC,GAAG,EAAE,CAAC,CAAE;AACrD;AAAA,MACF,KAAK;AACH,QAAAA,QAAO,CAAE,KAAK,GAAG,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,GAAGA,MAAK,CAAC,CAAC,CAAE;AACrD;AAAA,MACF,KAAK;AACH,QAAAA,QAAO,CAAE,KAAK,GAAG,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAE;AACjD;AAAA,IACF;AAEA,WAAOA;AAAA,EACT,GAEA,SAAS,SAAS,IAAIE,IAAG;AAEvB,QAAI,GAAGA,EAAC,EAAE,SAAS,GAAG;AACpB,SAAGA,EAAC,EAAE,MAAM;AACZ,UAAI,KAAK,GAAGA,EAAC;AAEb,aAAO,GAAG,QAAQ;AAChB,qBAAaA,EAAC,IAAI;AAClB,WAAG,OAAOA,MAAK,GAAG,CAAE,KAAK,GAAG,GAAG,OAAO,GAAG,CAAC,CAAE,CAAC;AAAA,MAC/C;AAEA,SAAG,OAAOA,IAAG,CAAC;AACd,WAAK,WAAW;AAAA,IAClB;AAAA,EACF,GAEA,eAAe,CAAC,GAChB,SAAS,IACTD,eAAc;AAElB,WAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,eAAW,CAAC,MAAM,SAAS,WAAW,CAAC,EAAE,CAAC;AAE1C,QAAI,UAAU,KACd;AACE,mBAAa,CAAC,IAAI;AAClB,YAAMA,eAAc,aAAa,IAAI,CAAC;AAAA,IACxC;AACA,eAAW,CAAC,IAAI,YAAY,WAAW,CAAC,GAAG,OAAOA,YAAW;AAE7D,QAAI,aAAa,CAAC,KAAK,OAAO,UAAU;AAAK,mBAAa,CAAC,IAAI;AAI/D,WAAO,YAAY,CAAC;AAEpB,QAAI,MAAM,WAAW,CAAC,GAClB,SAAS,IAAI;AAEjB,UAAM,IAAI,IAAI,SAAS,CAAC;AACxB,UAAM,IAAI,IAAI,SAAS,CAAC;AACxB,UAAM,KAAK,QAAQ,IAAI,SAAS,CAAC,CAAC,KAAK,MAAM;AAC7C,UAAM,KAAK,QAAQ,IAAI,SAAS,CAAC,CAAC,KAAK,MAAM;AAAA,EAC/C;AAGA,MAAI,QAAQ,UAAU,UAAU;AAEhC,SAAO;AACT;;;AC74BO,SAAS,aAAa,OAAO;AAClC,SAAO,SAAS,KAAK,KAAK,IAAI,OAAO,WAAW;AAClD;AASO,SAAS,QAAQ,OAAO;AAC7B,SAAO,SAAS,KAAK,KAAK,IAAI,OAAO,aAAa;AACpD;;;ACIO,SAAS,YAAY,QAAQ;AAClC,SAAO;AAAA,IACL,GAAG,KAAK,MAAM,OAAO,CAAC;AAAA,IACtB,GAAG,KAAK,MAAM,OAAO,CAAC;AAAA,IACtB,OAAO,KAAK,MAAM,OAAO,KAAK;AAAA,IAC9B,QAAQ,KAAK,MAAM,OAAO,MAAM;AAAA,EAClC;AACF;AAOO,SAAS,WAAW,OAAO;AAEhC,SAAO;AAAA,IACL,GAAG,KAAK,MAAM,MAAM,CAAC;AAAA,IACrB,GAAG,KAAK,MAAM,MAAM,CAAC;AAAA,EACvB;AACF;AAUO,SAAS,OAAO,QAAQ;AAC7B,SAAO;AAAA,IACL,KAAK,OAAO;AAAA,IACZ,OAAO,OAAO,KAAK,OAAO,SAAS;AAAA,IACnC,QAAQ,OAAO,KAAK,OAAO,UAAU;AAAA,IACrC,MAAM,OAAO;AAAA,EACf;AACF;AAUO,SAAS,SAAS,MAAM;AAC7B,SAAO;AAAA,IACL,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,IACR,OAAO,KAAK,QAAQ,KAAK;AAAA,IACzB,QAAQ,KAAK,SAAS,KAAK;AAAA,EAC7B;AACF;AAUO,SAAS,aAAa,QAAQ;AACnC,SAAO,WAAW;AAAA,IAChB,GAAG,OAAO,KAAK,OAAO,SAAS,KAAK;AAAA,IACpC,GAAG,OAAO,KAAK,OAAO,UAAU,KAAK;AAAA,EACvC,CAAC;AACH;AAUO,SAAS,iBAAiB,YAAY;AAC3C,MAAI,YAAY,WAAW;AAG3B,MAAI,QAAQ,UAAU,OAAO,SAASE,QAAO,OAAO,OAAO;AAEzD,QAAI,YAAY,UAAU,QAAQ,CAAC;AAEnC,QAAI,WAAW;AACb,UAAI,WAAWA,OAAMA,OAAM,SAAS,CAAC;AAErC,UAAI,cAAc,YAAY,SAAS,aAAa;AACpD,UAAI,SAAS,SAAS,WAAW,KAAK;AAEtC,MAAAA,OAAM,KAAK;AAAA,QACT,OAAO;AAAA,QACP,KAAK;AAAA,QACL;AAAA,QACA,WAAW,cAAc;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,MAAI,cAAc,MAAM,OAAO,SAAS,QAAQ,MAAM;AACpD,WAAO,SAAS,KAAK;AAAA,EACvB,GAAG,CAAC;AAGJ,MAAI,YAAY,cAAc;AAE9B,MAAI,IAAI;AACR,MAAI,aAAa,MAAM,CAAC;AAExB,SAAO,WAAW,YAAY,WAAW;AACvC,iBAAa,MAAM,EAAE,CAAC;AAAA,EACxB;AAGA,MAAI,mBAAmB,YAAY,WAAW,eAAe,WAAW;AAExE,MAAI,WAAW;AAAA,IACb,GAAG,WAAW,MAAM,KAAK,WAAW,IAAI,IAAI,WAAW,MAAM,KAAK;AAAA,IAClE,GAAG,WAAW,MAAM,KAAK,WAAW,IAAI,IAAI,WAAW,MAAM,KAAK;AAAA,EACpE;AAEA,SAAO;AACT;AAUO,SAAS,OAAO,SAAS;AAC9B,MAAI,aAAa,OAAO,GAAG;AACzB,WAAO,iBAAiB,OAAO;AAAA,EACjC;AAEA,SAAO,aAAa,OAAO;AAC7B;AAiBO,SAAS,eAAe,MAAM,WAAW,SAAS;AAEvD,YAAU,WAAW;AAIrB,MAAI,CAAC,SAAS,OAAO,GAAG;AACtB,cAAU,EAAE,GAAG,SAAS,GAAG,QAAQ;AAAA,EACrC;AAGA,MAAI,kBAAkB,OAAO,IAAI,GAC7B,uBAAuB,OAAO,SAAS;AAE3C,MAAI,MAAM,gBAAgB,SAAS,QAAQ,KAAK,qBAAqB,KACjE,QAAQ,gBAAgB,OAAO,QAAQ,KAAK,qBAAqB,OACjE,SAAS,gBAAgB,MAAM,QAAQ,KAAK,qBAAqB,QACjE,OAAO,gBAAgB,QAAQ,QAAQ,KAAK,qBAAqB;AAErE,MAAI,WAAW,MAAM,QAAS,SAAS,WAAW,MAC9C,aAAa,OAAO,SAAU,QAAQ,UAAU;AAEpD,MAAI,cAAc,UAAU;AAC1B,WAAO,WAAW,MAAM;AAAA,EAC1B,OAAO;AACL,WAAO,cAAc,YAAY;AAAA,EACnC;AACF;AAcO,SAAS,2BAA2B,aAAa,UAAU,WAAW;AAE3E,MAAI,gBAAgB,iBAAiB,aAAa,QAAQ;AAO1D,MAAI,cAAc,WAAW,GAAG;AAC9B,WAAO,WAAW,cAAc,CAAC,CAAC;AAAA,EACpC,WAAW,cAAc,WAAW,KAAK,cAAc,cAAc,CAAC,GAAG,cAAc,CAAC,CAAC,IAAI,GAAG;AAC9F,WAAO,WAAW,cAAc,CAAC,CAAC;AAAA,EACpC,WAAW,cAAc,SAAS,GAAG;AAInC,oBAAgB,OAAO,eAAe,SAAS,GAAG;AAChD,UAAIC,YAAW,KAAK,MAAM,EAAE,KAAK,GAAG,KAAK;AAEzC,MAAAA,YAAW,MAAMA;AAEjB,MAAAA,aAAYA,YAAW,KAAK,MAAM,MAAMA;AAKxC,aAAO,EAAE,WAAW,MAAMA;AAAA,IAC5B,CAAC;AAED,WAAO,WAAW,cAAc,YAAY,IAAI,cAAc,SAAS,CAAC,CAAC;AAAA,EAC3E;AAEA,SAAO;AACT;AAGO,SAAS,iBAAiB,GAAG,GAAG;AACrC,SAAO,sBAAe,GAAG,CAAC;AAC5B;AAGO,SAAS,yBAAyB,WAAW;AAGlD,cAAY,UAAU,MAAM;AAE5B,MAAI,MAAM,GACN,OACA,eACA;AAEJ,SAAO,UAAU,GAAG,GAAG;AACrB,YAAQ,UAAU,GAAG;AACrB,oBAAgB,UAAU,MAAM,CAAC;AACjC,gBAAY,UAAU,MAAM,CAAC;AAE7B,QAAI,cAAc,OAAO,SAAS,MAAM,KACpC,aAAa,eAAe,WAAW,KAAK,GAAG;AAIjD,gBAAU,OAAO,KAAK,CAAC;AAAA,IACzB,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAIA,SAAS,SAAS,GAAG,GAAG;AACtB,SAAO,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAClE;;;AChRO,SAAS,WAAW,UAAU;AAGnC,SAAO,OAAO,UAAU,SAAS,SAAS;AACxC,WAAO,CAAC,KAAK,UAAU,SAAS,GAAG;AACjC,aAAO,MAAM,WAAW,UAAU,SAAS,CAAC;AAAA,IAC9C,CAAC;AAAA,EACH,CAAC;AACH;AAGA,SAAS,UAAU,SAAS,QAAQ;AAClC,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AAEA,MAAI,YAAY,QAAQ;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,QAAQ,QAAQ;AACnB;AAAA,EACF;AAEA,SAAO,UAAU,QAAQ,QAAQ,MAAM;AACzC;AAWO,SAAS,IAAI,UAAU,SAAS,QAAQ;AAC7C,MAAI,SAAS,CAAC,UAAU,SAAS,QAAQ,OAAO,MAAM;AAEtD,MAAI,QAAQ;AACV,aAAS,KAAK,OAAO;AAAA,EACvB;AAEA,SAAO;AACT;AAaO,SAAS,YAAY,UAAU,IAAI,OAAO;AAE/C,UAAQ,SAAS;AAEjB,MAAI,CAAC,QAAQ,QAAQ,GAAG;AACtB,eAAW,CAAE,QAAS;AAAA,EACxB;AAEA,UAAQ,UAAU,SAAS,GAAG,GAAG;AAC/B,QAAIC,UAAS,GAAG,GAAG,GAAG,KAAK;AAE3B,QAAI,QAAQA,OAAM,KAAKA,QAAO,QAAQ;AACpC,kBAAYA,SAAQ,IAAI,QAAQ,CAAC;AAAA,IACnC;AAAA,EACF,CAAC;AACH;AAYO,SAAS,gBAAgB,UAAU,QAAQ,UAAU;AAC1D,MAAI,SAAS,CAAC,GACV,oBAAoB,CAAC;AAEzB,cAAY,UAAU,SAAS,SAAS,GAAG,OAAO;AAChD,QAAI,QAAQ,SAAS,MAAM;AAE3B,QAAI,WAAW,QAAQ;AAGvB,QAAI,aAAa,MAAM,QAAQ,UAAU;AAGvC,UAAI,YAAY,IAAI,mBAAmB,UAAU,MAAM,GAAG;AACxD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAuBO,SAAS,mBAAmB,UAAU,iBAAiB;AAC5D,SAAO,gBAAgB,UAAU,CAAC,iBAAiB,EAAE;AACvD;AAaO,SAAS,WAAW,UAAU,YAAY,SAAS;AAExD,MAAI,YAAY,UAAU,GAAG;AAC3B,iBAAa;AAAA,EACf;AAEA,MAAI,SAAS,UAAU,GAAG;AACxB,cAAU;AACV,iBAAa;AAAA,EACf;AAGA,YAAU,WAAW,CAAC;AAEtB,MAAI,YAAY,WAAW,QAAQ,SAAS,GACxC,iBAAiB,WAAW,QAAQ,cAAc,GAClD,mBAAmB,WAAW,QAAQ,gBAAgB,GACtD,sBAAsB,WAAW,QAAQ,mBAAmB;AAEhE,MAAI,WAAW;AAAA,IACb,QAAQ;AAAA,IACR,cAAc,QAAQ,UAAU,SAAS,GAAG;AAAE,aAAO,EAAE;AAAA,IAAI,CAAC;AAAA,EAC9D;AAGA,WAAS,iBAAiB,GAAG;AAC3B,QAAI,SAAS,EAAE,OAAO,EAAE,KAAK,SAAS,EAAE,OAAO,EAAE,GAAG;AAClD,eAAS,EAAE,EAAE,IAAI,CAAE,CAAE;AAAA,IACvB;AAIA,QAAI,UAAU,EAAE,OAAO,EAAE,KAAK,UAAU,EAAE,OAAO,EAAE,GAAG;AACpD,0BAAoB,EAAE,EAAE,IAAI,iBAAiB,EAAE,EAAE,IAAI;AAAA,IACvD;AAEA,mBAAe,EAAE,EAAE,IAAI;AAAA,EACzB;AAEA,WAAS,cAAc,SAAS;AAE9B,qBAAiB,QAAQ,EAAE,IAAI;AAE/B,QAAI,QAAQ,WAAW;AAGrB,0BAAoB,QAAQ,EAAE,IAAI,eAAe,QAAQ,EAAE,IAAI;AAAA,IACjE,OAAO;AAGL,gBAAU,QAAQ,EAAE,IAAI;AAGxB,cAAQ,QAAQ,UAAU,gBAAgB;AAE1C,cAAQ,QAAQ,UAAU,gBAAgB;AAG1C,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAEA,cAAY,UAAU,aAAa;AAEnC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAWO,SAAS,QAAQ,UAAU,eAAe;AAE/C,kBAAgB,CAAC,CAAC;AAClB,MAAI,CAAC,QAAQ,QAAQ,GAAG;AACtB,eAAW,CAAE,QAAS;AAAA,EACxB;AAEA,MAAI,MACA,MACA,MACA;AAEJ,UAAQ,UAAU,SAAS,SAAS;AAGlC,QAAI,OAAO;AACX,QAAI,QAAQ,aAAa,CAAC,eAAe;AACvC,aAAO,QAAQ,QAAQ,WAAW,IAAI;AAAA,IACxC;AAEA,QAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,SAAS,KAAK,UAAU,GACxB,QAAQ,KAAK,SAAS;AAE1B,QAAI,IAAI,QAAQ,SAAS,QAAW;AAClC,aAAO;AAAA,IACT;AACA,QAAI,IAAI,QAAQ,SAAS,QAAW;AAClC,aAAO;AAAA,IACT;AAEA,QAAK,IAAI,QAAS,QAAQ,SAAS,QAAW;AAC5C,aAAO,IAAI;AAAA,IACb;AACA,QAAK,IAAI,SAAU,QAAQ,SAAS,QAAW;AAC7C,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,QAAQ,OAAO;AAAA,IACf,OAAO,OAAO;AAAA,EAChB;AACF;AAgBO,SAAS,oBAAoB,UAAU,MAAM;AAElD,MAAI,mBAAmB,CAAC;AAExB,UAAQ,UAAU,SAAS,SAAS;AAElC,QAAI,IAAI;AAER,QAAI,EAAE,WAAW;AACf,UAAI,QAAQ,CAAC;AAAA,IACf;AAEA,QAAI,CAAC,SAAS,KAAK,CAAC,KAAM,EAAE,IAAI,KAAK,GAAI;AACvC,uBAAiB,QAAQ,EAAE,IAAI;AAAA,IACjC;AACA,QAAI,CAAC,SAAS,KAAK,CAAC,KAAM,EAAE,IAAI,KAAK,GAAI;AACvC,uBAAiB,QAAQ,EAAE,IAAI;AAAA,IACjC;AACA,QAAI,EAAE,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG;AAChC,UAAI,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK,MAAM,KAC5C,EAAE,QAAQ,EAAE,IAAI,KAAK,QAAQ,KAAK,KAClC,EAAE,SAAS,EAAE,IAAI,KAAK,SAAS,KAAK,GAAG;AAEzC,yBAAiB,QAAQ,EAAE,IAAI;AAAA,MACjC,WAAW,CAAC,SAAS,KAAK,KAAK,KAAK,CAAC,SAAS,KAAK,MAAM,GAAG;AAC1D,yBAAiB,QAAQ,EAAE,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AASO,SAAS,QAAQ,SAAS;AAE/B,MAAI,eAAe,SAAS;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS;AAClB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAOO,SAAS,eAAe,SAAS;AACtC,SAAO,CAAC,EAAE,WAAW,QAAQ;AAC/B;AAIA,SAAS,WAAW,MAAM,MAAM;AAC9B,SAAO,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC1C;", "names": ["isArray", "a", "b", "x", "y", "rad", "path", "pathCommand", "i", "parts", "distance", "filter"]}