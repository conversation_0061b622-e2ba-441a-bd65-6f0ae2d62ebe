{"version": 3, "sources": ["../../.pnpm/punycode@1.4.1/node_modules/punycode/punycode.js", "../../.pnpm/url@0.11.4/node_modules/url/url.js"], "sourcesContent": ["/*! https://mths.be/punycode v1.4.1 by @mathias */\n;(function(root) {\n\n\t/** Detect free variables */\n\tvar freeExports = typeof exports == 'object' && exports &&\n\t\t!exports.nodeType && exports;\n\tvar freeModule = typeof module == 'object' && module &&\n\t\t!module.nodeType && module;\n\tvar freeGlobal = typeof global == 'object' && global;\n\tif (\n\t\tfreeGlobal.global === freeGlobal ||\n\t\tfreeGlobal.window === freeGlobal ||\n\t\tfreeGlobal.self === freeGlobal\n\t) {\n\t\troot = freeGlobal;\n\t}\n\n\t/**\n\t * The `punycode` object.\n\t * @name punycode\n\t * @type Object\n\t */\n\tvar punycode,\n\n\t/** Highest positive signed 32-bit float value */\n\tmaxInt = 2147483647, // aka. 0x7FFFFFFF or 2^31-1\n\n\t/** Bootstring parameters */\n\tbase = 36,\n\ttMin = 1,\n\ttMax = 26,\n\tskew = 38,\n\tdamp = 700,\n\tinitialBias = 72,\n\tinitialN = 128, // 0x80\n\tdelimiter = '-', // '\\x2D'\n\n\t/** Regular expressions */\n\tregexPunycode = /^xn--/,\n\tregexNonASCII = /[^\\x20-\\x7E]/, // unprintable ASCII chars + non-ASCII chars\n\tregexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g, // RFC 3490 separators\n\n\t/** Error messages */\n\terrors = {\n\t\t'overflow': 'Overflow: input needs wider integers to process',\n\t\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t\t'invalid-input': 'Invalid input'\n\t},\n\n\t/** Convenience shortcuts */\n\tbaseMinusTMin = base - tMin,\n\tfloor = Math.floor,\n\tstringFromCharCode = String.fromCharCode,\n\n\t/** Temporary variable */\n\tkey;\n\n\t/*--------------------------------------------------------------------------*/\n\n\t/**\n\t * A generic error utility function.\n\t * @private\n\t * @param {String} type The error type.\n\t * @returns {Error} Throws a `RangeError` with the applicable error message.\n\t */\n\tfunction error(type) {\n\t\tthrow new RangeError(errors[type]);\n\t}\n\n\t/**\n\t * A generic `Array#map` utility function.\n\t * @private\n\t * @param {Array} array The array to iterate over.\n\t * @param {Function} callback The function that gets called for every array\n\t * item.\n\t * @returns {Array} A new array of values returned by the callback function.\n\t */\n\tfunction map(array, fn) {\n\t\tvar length = array.length;\n\t\tvar result = [];\n\t\twhile (length--) {\n\t\t\tresult[length] = fn(array[length]);\n\t\t}\n\t\treturn result;\n\t}\n\n\t/**\n\t * A simple `Array#map`-like wrapper to work with domain name strings or email\n\t * addresses.\n\t * @private\n\t * @param {String} domain The domain name or email address.\n\t * @param {Function} callback The function that gets called for every\n\t * character.\n\t * @returns {Array} A new string of characters returned by the callback\n\t * function.\n\t */\n\tfunction mapDomain(string, fn) {\n\t\tvar parts = string.split('@');\n\t\tvar result = '';\n\t\tif (parts.length > 1) {\n\t\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t\t// the local part (i.e. everything up to `@`) intact.\n\t\t\tresult = parts[0] + '@';\n\t\t\tstring = parts[1];\n\t\t}\n\t\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\t\tstring = string.replace(regexSeparators, '\\x2E');\n\t\tvar labels = string.split('.');\n\t\tvar encoded = map(labels, fn).join('.');\n\t\treturn result + encoded;\n\t}\n\n\t/**\n\t * Creates an array containing the numeric code points of each Unicode\n\t * character in the string. While JavaScript uses UCS-2 internally,\n\t * this function will convert a pair of surrogate halves (each of which\n\t * UCS-2 exposes as separate characters) into a single code point,\n\t * matching UTF-16.\n\t * @see `punycode.ucs2.encode`\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode.ucs2\n\t * @name decode\n\t * @param {String} string The Unicode input string (UCS-2).\n\t * @returns {Array} The new array of code points.\n\t */\n\tfunction ucs2decode(string) {\n\t\tvar output = [],\n\t\t    counter = 0,\n\t\t    length = string.length,\n\t\t    value,\n\t\t    extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\n\t/**\n\t * Creates a string based on an array of numeric code points.\n\t * @see `punycode.ucs2.decode`\n\t * @memberOf punycode.ucs2\n\t * @name encode\n\t * @param {Array} codePoints The array of numeric code points.\n\t * @returns {String} The new Unicode string (UCS-2).\n\t */\n\tfunction ucs2encode(array) {\n\t\treturn map(array, function(value) {\n\t\t\tvar output = '';\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t\treturn output;\n\t\t}).join('');\n\t}\n\n\t/**\n\t * Converts a basic code point into a digit/integer.\n\t * @see `digitToBasic()`\n\t * @private\n\t * @param {Number} codePoint The basic numeric code point value.\n\t * @returns {Number} The numeric value of a basic code point (for use in\n\t * representing integers) in the range `0` to `base - 1`, or `base` if\n\t * the code point does not represent a value.\n\t */\n\tfunction basicToDigit(codePoint) {\n\t\tif (codePoint - 48 < 10) {\n\t\t\treturn codePoint - 22;\n\t\t}\n\t\tif (codePoint - 65 < 26) {\n\t\t\treturn codePoint - 65;\n\t\t}\n\t\tif (codePoint - 97 < 26) {\n\t\t\treturn codePoint - 97;\n\t\t}\n\t\treturn base;\n\t}\n\n\t/**\n\t * Converts a digit/integer into a basic code point.\n\t * @see `basicToDigit()`\n\t * @private\n\t * @param {Number} digit The numeric value of a basic code point.\n\t * @returns {Number} The basic code point whose value (when used for\n\t * representing integers) is `digit`, which needs to be in the range\n\t * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n\t * used; else, the lowercase form is used. The behavior is undefined\n\t * if `flag` is non-zero and `digit` has no uppercase form.\n\t */\n\tfunction digitToBasic(digit, flag) {\n\t\t//  0..25 map to ASCII a..z or A..Z\n\t\t// 26..35 map to ASCII 0..9\n\t\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n\t}\n\n\t/**\n\t * Bias adaptation function as per section 3.4 of RFC 3492.\n\t * https://tools.ietf.org/html/rfc3492#section-3.4\n\t * @private\n\t */\n\tfunction adapt(delta, numPoints, firstTime) {\n\t\tvar k = 0;\n\t\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\t\tdelta += floor(delta / numPoints);\n\t\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\t\tdelta = floor(delta / baseMinusTMin);\n\t\t}\n\t\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n\t}\n\n\t/**\n\t * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n\t * symbols.\n\t * @memberOf punycode\n\t * @param {String} input The Punycode string of ASCII-only symbols.\n\t * @returns {String} The resulting string of Unicode symbols.\n\t */\n\tfunction decode(input) {\n\t\t// Don't use UCS-2\n\t\tvar output = [],\n\t\t    inputLength = input.length,\n\t\t    out,\n\t\t    i = 0,\n\t\t    n = initialN,\n\t\t    bias = initialBias,\n\t\t    basic,\n\t\t    j,\n\t\t    index,\n\t\t    oldi,\n\t\t    w,\n\t\t    k,\n\t\t    digit,\n\t\t    t,\n\t\t    /** Cached calculation results */\n\t\t    baseMinusT;\n\n\t\t// Handle the basic code points: let `basic` be the number of input code\n\t\t// points before the last delimiter, or `0` if there is none, then copy\n\t\t// the first basic code points to the output.\n\n\t\tbasic = input.lastIndexOf(delimiter);\n\t\tif (basic < 0) {\n\t\t\tbasic = 0;\n\t\t}\n\n\t\tfor (j = 0; j < basic; ++j) {\n\t\t\t// if it's not a basic code point\n\t\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\t\terror('not-basic');\n\t\t\t}\n\t\t\toutput.push(input.charCodeAt(j));\n\t\t}\n\n\t\t// Main decoding loop: start just after the last delimiter if any basic code\n\t\t// points were copied; start at the beginning otherwise.\n\n\t\tfor (index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t\t// `index` is the index of the next character to be consumed.\n\t\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t\t// which gets added to `i`. The overflow checking is easier\n\t\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t\t// value at the end to obtain `delta`.\n\t\t\tfor (oldi = i, w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\t\tif (index >= inputLength) {\n\t\t\t\t\terror('invalid-input');\n\t\t\t\t}\n\n\t\t\t\tdigit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\t\tif (digit >= base || digit > floor((maxInt - i) / w)) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\ti += digit * w;\n\t\t\t\tt = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\t\tif (digit < t) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tbaseMinusT = base - t;\n\t\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\tw *= baseMinusT;\n\n\t\t\t}\n\n\t\t\tout = output.length + 1;\n\t\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t\t// incrementing `n` each time, so we'll fix that now:\n\t\t\tif (floor(i / out) > maxInt - n) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tn += floor(i / out);\n\t\t\ti %= out;\n\n\t\t\t// Insert `n` at position `i` of the output\n\t\t\toutput.splice(i++, 0, n);\n\n\t\t}\n\n\t\treturn ucs2encode(output);\n\t}\n\n\t/**\n\t * Converts a string of Unicode symbols (e.g. a domain name label) to a\n\t * Punycode string of ASCII-only symbols.\n\t * @memberOf punycode\n\t * @param {String} input The string of Unicode symbols.\n\t * @returns {String} The resulting Punycode string of ASCII-only symbols.\n\t */\n\tfunction encode(input) {\n\t\tvar n,\n\t\t    delta,\n\t\t    handledCPCount,\n\t\t    basicLength,\n\t\t    bias,\n\t\t    j,\n\t\t    m,\n\t\t    q,\n\t\t    k,\n\t\t    t,\n\t\t    currentValue,\n\t\t    output = [],\n\t\t    /** `inputLength` will hold the number of code points in `input`. */\n\t\t    inputLength,\n\t\t    /** Cached calculation results */\n\t\t    handledCPCountPlusOne,\n\t\t    baseMinusT,\n\t\t    qMinusT;\n\n\t\t// Convert the input in UCS-2 to Unicode\n\t\tinput = ucs2decode(input);\n\n\t\t// Cache the length\n\t\tinputLength = input.length;\n\n\t\t// Initialize the state\n\t\tn = initialN;\n\t\tdelta = 0;\n\t\tbias = initialBias;\n\n\t\t// Handle the basic code points\n\t\tfor (j = 0; j < inputLength; ++j) {\n\t\t\tcurrentValue = input[j];\n\t\t\tif (currentValue < 0x80) {\n\t\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t\t}\n\t\t}\n\n\t\thandledCPCount = basicLength = output.length;\n\n\t\t// `handledCPCount` is the number of code points that have been handled;\n\t\t// `basicLength` is the number of basic code points.\n\n\t\t// Finish the basic string - if it is not empty - with a delimiter\n\t\tif (basicLength) {\n\t\t\toutput.push(delimiter);\n\t\t}\n\n\t\t// Main encoding loop:\n\t\twhile (handledCPCount < inputLength) {\n\n\t\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t\t// larger one:\n\t\t\tfor (m = maxInt, j = 0; j < inputLength; ++j) {\n\t\t\t\tcurrentValue = input[j];\n\t\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\t\tm = currentValue;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t\t// but guard against overflow\n\t\t\thandledCPCountPlusOne = handledCPCount + 1;\n\t\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\t\tn = m;\n\n\t\t\tfor (j = 0; j < inputLength; ++j) {\n\t\t\t\tcurrentValue = input[j];\n\n\t\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\tif (currentValue == n) {\n\t\t\t\t\t// Represent delta as a generalized variable-length integer\n\t\t\t\t\tfor (q = delta, k = base; /* no condition */; k += base) {\n\t\t\t\t\t\tt = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tqMinusT = q - t;\n\t\t\t\t\t\tbaseMinusT = base - t;\n\t\t\t\t\t\toutput.push(\n\t\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t\t);\n\t\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t\t}\n\n\t\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n\t\t\t\t\tdelta = 0;\n\t\t\t\t\t++handledCPCount;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t++delta;\n\t\t\t++n;\n\n\t\t}\n\t\treturn output.join('');\n\t}\n\n\t/**\n\t * Converts a Punycode string representing a domain name or an email address\n\t * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n\t * it doesn't matter if you call it on a string that has already been\n\t * converted to Unicode.\n\t * @memberOf punycode\n\t * @param {String} input The Punycoded domain name or email address to\n\t * convert to Unicode.\n\t * @returns {String} The Unicode representation of the given Punycode\n\t * string.\n\t */\n\tfunction toUnicode(input) {\n\t\treturn mapDomain(input, function(string) {\n\t\t\treturn regexPunycode.test(string)\n\t\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t\t: string;\n\t\t});\n\t}\n\n\t/**\n\t * Converts a Unicode string representing a domain name or an email address to\n\t * Punycode. Only the non-ASCII parts of the domain name will be converted,\n\t * i.e. it doesn't matter if you call it with a domain that's already in\n\t * ASCII.\n\t * @memberOf punycode\n\t * @param {String} input The domain name or email address to convert, as a\n\t * Unicode string.\n\t * @returns {String} The Punycode representation of the given domain name or\n\t * email address.\n\t */\n\tfunction toASCII(input) {\n\t\treturn mapDomain(input, function(string) {\n\t\t\treturn regexNonASCII.test(string)\n\t\t\t\t? 'xn--' + encode(string)\n\t\t\t\t: string;\n\t\t});\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\t/** Define the public API */\n\tpunycode = {\n\t\t/**\n\t\t * A string representing the current Punycode.js version number.\n\t\t * @memberOf punycode\n\t\t * @type String\n\t\t */\n\t\t'version': '1.4.1',\n\t\t/**\n\t\t * An object of methods to convert from JavaScript's internal character\n\t\t * representation (UCS-2) to Unicode code points, and back.\n\t\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t\t * @memberOf punycode\n\t\t * @type Object\n\t\t */\n\t\t'ucs2': {\n\t\t\t'decode': ucs2decode,\n\t\t\t'encode': ucs2encode\n\t\t},\n\t\t'decode': decode,\n\t\t'encode': encode,\n\t\t'toASCII': toASCII,\n\t\t'toUnicode': toUnicode\n\t};\n\n\t/** Expose `punycode` */\n\t// Some AMD build optimizers, like r.js, check for specific condition patterns\n\t// like the following:\n\tif (\n\t\ttypeof define == 'function' &&\n\t\ttypeof define.amd == 'object' &&\n\t\tdefine.amd\n\t) {\n\t\tdefine('punycode', function() {\n\t\t\treturn punycode;\n\t\t});\n\t} else if (freeExports && freeModule) {\n\t\tif (module.exports == freeExports) {\n\t\t\t// in Node.js, io.js, or RingoJS v0.8.0+\n\t\t\tfreeModule.exports = punycode;\n\t\t} else {\n\t\t\t// in Narwhal or RingoJS v0.7.0-\n\t\t\tfor (key in punycode) {\n\t\t\t\tpunycode.hasOwnProperty(key) && (freeExports[key] = punycode[key]);\n\t\t\t}\n\t\t}\n\t} else {\n\t\t// in Rhino or a web browser\n\t\troot.punycode = punycode;\n\t}\n\n}(this));\n", "/*\n * Copyright Joyent, Inc. and other Node contributors.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a\n * copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to permit\n * persons to whom the Software is furnished to do so, subject to the\n * following conditions:\n *\n * The above copyright notice and this permission notice shall be included\n * in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n * NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n * DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n * USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n'use strict';\n\nvar punycode = require('punycode/');\n\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.host = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.query = null;\n  this.pathname = null;\n  this.path = null;\n  this.href = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n/*\n * define these here so at least they only have to be\n * compiled once on the first module load.\n */\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n  portPattern = /:[0-9]*$/,\n\n  // Special case for a simple path URL\n  simplePathPattern = /^(\\/\\/?(?!\\/)[^?\\s]*)(\\?[^\\s]*)?$/,\n\n  /*\n   * RFC 2396: characters reserved for delimiting URLs.\n   * We actually just auto-escape these.\n   */\n  delims = [\n    '<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t'\n  ],\n\n  // RFC 2396: characters not allowed for various reasons.\n  unwise = [\n    '{', '}', '|', '\\\\', '^', '`'\n  ].concat(delims),\n\n  // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n  autoEscape = ['\\''].concat(unwise),\n  /*\n   * Characters that are never ever allowed in a hostname.\n   * Note that any invalid chars are also handled, but these\n   * are the ones that are *expected* to be seen, so we fast-path\n   * them.\n   */\n  nonHostChars = [\n    '%', '/', '?', ';', '#'\n  ].concat(autoEscape),\n  hostEndingChars = [\n    '/', '?', '#'\n  ],\n  hostnameMaxLen = 255,\n  hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n  hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n  // protocols that can allow \"unsafe\" and \"unwise\" chars.\n  unsafeProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that never have a hostname.\n  hostlessProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that always contain a // bit.\n  slashedProtocol = {\n    http: true,\n    https: true,\n    ftp: true,\n    gopher: true,\n    file: true,\n    'http:': true,\n    'https:': true,\n    'ftp:': true,\n    'gopher:': true,\n    'file:': true\n  },\n  querystring = require('qs');\n\nfunction urlParse(url, parseQueryString, slashesDenoteHost) {\n  if (url && typeof url === 'object' && url instanceof Url) { return url; }\n\n  var u = new Url();\n  u.parse(url, parseQueryString, slashesDenoteHost);\n  return u;\n}\n\nUrl.prototype.parse = function (url, parseQueryString, slashesDenoteHost) {\n  if (typeof url !== 'string') {\n    throw new TypeError(\"Parameter 'url' must be a string, not \" + typeof url);\n  }\n\n  /*\n   * Copy chrome, IE, opera backslash-handling behavior.\n   * Back slashes before the query string get converted to forward slashes\n   * See: https://code.google.com/p/chromium/issues/detail?id=25916\n   */\n  var queryIndex = url.indexOf('?'),\n    splitter = queryIndex !== -1 && queryIndex < url.indexOf('#') ? '?' : '#',\n    uSplit = url.split(splitter),\n    slashRegex = /\\\\/g;\n  uSplit[0] = uSplit[0].replace(slashRegex, '/');\n  url = uSplit.join(splitter);\n\n  var rest = url;\n\n  /*\n   * trim before proceeding.\n   * This is to support parse stuff like \"  http://foo.com  \\n\"\n   */\n  rest = rest.trim();\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.path = rest;\n      this.href = rest;\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n        if (parseQueryString) {\n          this.query = querystring.parse(this.search.substr(1));\n        } else {\n          this.query = this.search.substr(1);\n        }\n      } else if (parseQueryString) {\n        this.search = '';\n        this.query = {};\n      }\n      return this;\n    }\n  }\n\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    var lowerProto = proto.toLowerCase();\n    this.protocol = lowerProto;\n    rest = rest.substr(proto.length);\n  }\n\n  /*\n   * figure out if it's got a host\n   * user@server is *always* interpreted as a hostname, and url\n   * resolution will treat //foo/bar as host=foo,path=bar because that's\n   * how the browser resolves relative URLs.\n   */\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@/]+@[^@/]+/)) {\n    var slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n\n  if (!hostlessProtocol[proto] && (slashes || (proto && !slashedProtocol[proto]))) {\n\n    /*\n     * there's a hostname.\n     * the first instance of /, ?, ;, or # ends the host.\n     *\n     * If there is an @ in the hostname, then non-host chars *are* allowed\n     * to the left of the last @ sign, unless some host-ending character\n     * comes *before* the @-sign.\n     * URLs are obnoxious.\n     *\n     * ex:\n     * http://a@b@c/ => user:a@b host:c\n     * http://a@b?@c => user:a host:c path:/?@c\n     */\n\n    /*\n     * v0.12 TODO(isaacs): This is not quite how Chrome does things.\n     * Review our test case against browsers more comprehensively.\n     */\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (var i = 0; i < hostEndingChars.length; i++) {\n      var hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) { hostEnd = hec; }\n    }\n\n    /*\n     * at this point, either we have an explicit point where the\n     * auth portion cannot go past, or the last @ char is the decider.\n     */\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      /*\n       * atSign must be in auth portion.\n       * http://a@b/c@d => host:b auth:a path:/c@d\n       */\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    /*\n     * Now we have a portion which is definitely the auth.\n     * Pull that off.\n     */\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = decodeURIComponent(auth);\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (var i = 0; i < nonHostChars.length; i++) {\n      var hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) { hostEnd = hec; }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) { hostEnd = rest.length; }\n\n    this.host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost();\n\n    /*\n     * we've indicated that there is a hostname,\n     * so even if it's empty, it has to be present.\n     */\n    this.hostname = this.hostname || '';\n\n    /*\n     * if hostname begins with [ and ends with ]\n     * assume that it's an IPv6 address.\n     */\n    var ipv6Hostname = this.hostname[0] === '[' && this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (var i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) { continue; }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              /*\n               * we replace non-ASCII char with a temporary placeholder\n               * we need this to make sure size of hostname is not\n               * broken by replacing non-ASCII by nothing\n               */\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = '/' + notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    } else {\n      // hostnames are always lower case.\n      this.hostname = this.hostname.toLowerCase();\n    }\n\n    if (!ipv6Hostname) {\n      /*\n       * IDNA Support: Returns a punycoded representation of \"domain\".\n       * It only converts parts of the domain name that\n       * have non-ASCII characters, i.e. it doesn't matter if\n       * you call it with a domain that already is ASCII-only.\n       */\n      this.hostname = punycode.toASCII(this.hostname);\n    }\n\n    var p = this.port ? ':' + this.port : '';\n    var h = this.hostname || '';\n    this.host = h + p;\n    this.href += this.host;\n\n    /*\n     * strip [ and ] from the hostname\n     * the host field still retains them, though\n     */\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n      if (rest[0] !== '/') {\n        rest = '/' + rest;\n      }\n    }\n  }\n\n  /*\n   * now rest is set to the post-host stuff.\n   * chop off any delim chars.\n   */\n  if (!unsafeProtocol[lowerProto]) {\n\n    /*\n     * First, make 100% sure that any \"autoEscape\" chars get\n     * escaped, even if encodeURIComponent doesn't think they\n     * need to be.\n     */\n    for (var i = 0, l = autoEscape.length; i < l; i++) {\n      var ae = autoEscape[i];\n      if (rest.indexOf(ae) === -1) { continue; }\n      var esc = encodeURIComponent(ae);\n      if (esc === ae) {\n        esc = escape(ae);\n      }\n      rest = rest.split(ae).join(esc);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    this.query = rest.substr(qm + 1);\n    if (parseQueryString) {\n      this.query = querystring.parse(this.query);\n    }\n    rest = rest.slice(0, qm);\n  } else if (parseQueryString) {\n    // no query string, but parseQueryString still requested\n    this.search = '';\n    this.query = {};\n  }\n  if (rest) { this.pathname = rest; }\n  if (slashedProtocol[lowerProto] && this.hostname && !this.pathname) {\n    this.pathname = '/';\n  }\n\n  // to support http.request\n  if (this.pathname || this.search) {\n    var p = this.pathname || '';\n    var s = this.search || '';\n    this.path = p + s;\n  }\n\n  // finally, reconstruct the href based on what has been validated.\n  this.href = this.format();\n  return this;\n};\n\n// format a parsed object into a url string\nfunction urlFormat(obj) {\n  /*\n   * ensure it's an object, and not a string url.\n   * If it's an obj, this is a no-op.\n   * this way, you can call url_format() on strings\n   * to clean up potentially wonky urls.\n   */\n  if (typeof obj === 'string') { obj = urlParse(obj); }\n  if (!(obj instanceof Url)) { return Url.prototype.format.call(obj); }\n  return obj.format();\n}\n\nUrl.prototype.format = function () {\n  var auth = this.auth || '';\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, ':');\n    auth += '@';\n  }\n\n  var protocol = this.protocol || '',\n    pathname = this.pathname || '',\n    hash = this.hash || '',\n    host = false,\n    query = '';\n\n  if (this.host) {\n    host = auth + this.host;\n  } else if (this.hostname) {\n    host = auth + (this.hostname.indexOf(':') === -1 ? this.hostname : '[' + this.hostname + ']');\n    if (this.port) {\n      host += ':' + this.port;\n    }\n  }\n\n  if (this.query && typeof this.query === 'object' && Object.keys(this.query).length) {\n    query = querystring.stringify(this.query, {\n      arrayFormat: 'repeat',\n      addQueryPrefix: false\n    });\n  }\n\n  var search = this.search || (query && ('?' + query)) || '';\n\n  if (protocol && protocol.substr(-1) !== ':') { protocol += ':'; }\n\n  /*\n   * only the slashedProtocols get the //.  Not mailto:, xmpp:, etc.\n   * unless they had them to begin with.\n   */\n  if (this.slashes || (!protocol || slashedProtocol[protocol]) && host !== false) {\n    host = '//' + (host || '');\n    if (pathname && pathname.charAt(0) !== '/') { pathname = '/' + pathname; }\n  } else if (!host) {\n    host = '';\n  }\n\n  if (hash && hash.charAt(0) !== '#') { hash = '#' + hash; }\n  if (search && search.charAt(0) !== '?') { search = '?' + search; }\n\n  pathname = pathname.replace(/[?#]/g, function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace('#', '%23');\n\n  return protocol + host + pathname + search + hash;\n};\n\nfunction urlResolve(source, relative) {\n  return urlParse(source, false, true).resolve(relative);\n}\n\nUrl.prototype.resolve = function (relative) {\n  return this.resolveObject(urlParse(relative, false, true)).format();\n};\n\nfunction urlResolveObject(source, relative) {\n  if (!source) { return relative; }\n  return urlParse(source, false, true).resolveObject(relative);\n}\n\nUrl.prototype.resolveObject = function (relative) {\n  if (typeof relative === 'string') {\n    var rel = new Url();\n    rel.parse(relative, false, true);\n    relative = rel;\n  }\n\n  var result = new Url();\n  var tkeys = Object.keys(this);\n  for (var tk = 0; tk < tkeys.length; tk++) {\n    var tkey = tkeys[tk];\n    result[tkey] = this[tkey];\n  }\n\n  /*\n   * hash is always overridden, no matter what.\n   * even href=\"\" will remove it.\n   */\n  result.hash = relative.hash;\n\n  // if the relative url is empty, then there's nothing left to do here.\n  if (relative.href === '') {\n    result.href = result.format();\n    return result;\n  }\n\n  // hrefs like //foo/bar always cut to the protocol.\n  if (relative.slashes && !relative.protocol) {\n    // take everything except the protocol from relative\n    var rkeys = Object.keys(relative);\n    for (var rk = 0; rk < rkeys.length; rk++) {\n      var rkey = rkeys[rk];\n      if (rkey !== 'protocol') { result[rkey] = relative[rkey]; }\n    }\n\n    // urlParse appends trailing / to urls like http://www.example.com\n    if (slashedProtocol[result.protocol] && result.hostname && !result.pathname) {\n      result.pathname = '/';\n      result.path = result.pathname;\n    }\n\n    result.href = result.format();\n    return result;\n  }\n\n  if (relative.protocol && relative.protocol !== result.protocol) {\n    /*\n     * if it's a known url protocol, then changing\n     * the protocol does weird things\n     * first, if it's not file:, then we MUST have a host,\n     * and if there was a path\n     * to begin with, then we MUST have a path.\n     * if it is file:, then the host is dropped,\n     * because that's known to be hostless.\n     * anything else is assumed to be absolute.\n     */\n    if (!slashedProtocol[relative.protocol]) {\n      var keys = Object.keys(relative);\n      for (var v = 0; v < keys.length; v++) {\n        var k = keys[v];\n        result[k] = relative[k];\n      }\n      result.href = result.format();\n      return result;\n    }\n\n    result.protocol = relative.protocol;\n    if (!relative.host && !hostlessProtocol[relative.protocol]) {\n      var relPath = (relative.pathname || '').split('/');\n      while (relPath.length && !(relative.host = relPath.shift())) { }\n      if (!relative.host) { relative.host = ''; }\n      if (!relative.hostname) { relative.hostname = ''; }\n      if (relPath[0] !== '') { relPath.unshift(''); }\n      if (relPath.length < 2) { relPath.unshift(''); }\n      result.pathname = relPath.join('/');\n    } else {\n      result.pathname = relative.pathname;\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    result.host = relative.host || '';\n    result.auth = relative.auth;\n    result.hostname = relative.hostname || relative.host;\n    result.port = relative.port;\n    // to support http.request\n    if (result.pathname || result.search) {\n      var p = result.pathname || '';\n      var s = result.search || '';\n      result.path = p + s;\n    }\n    result.slashes = result.slashes || relative.slashes;\n    result.href = result.format();\n    return result;\n  }\n\n  var isSourceAbs = result.pathname && result.pathname.charAt(0) === '/',\n    isRelAbs = relative.host || relative.pathname && relative.pathname.charAt(0) === '/',\n    mustEndAbs = isRelAbs || isSourceAbs || (result.host && relative.pathname),\n    removeAllDots = mustEndAbs,\n    srcPath = result.pathname && result.pathname.split('/') || [],\n    relPath = relative.pathname && relative.pathname.split('/') || [],\n    psychotic = result.protocol && !slashedProtocol[result.protocol];\n\n  /*\n   * if the url is a non-slashed url, then relative\n   * links like ../.. should be able\n   * to crawl up to the hostname, as well.  This is strange.\n   * result.protocol has already been set by now.\n   * Later on, put the first path part into the host field.\n   */\n  if (psychotic) {\n    result.hostname = '';\n    result.port = null;\n    if (result.host) {\n      if (srcPath[0] === '') { srcPath[0] = result.host; } else { srcPath.unshift(result.host); }\n    }\n    result.host = '';\n    if (relative.protocol) {\n      relative.hostname = null;\n      relative.port = null;\n      if (relative.host) {\n        if (relPath[0] === '') { relPath[0] = relative.host; } else { relPath.unshift(relative.host); }\n      }\n      relative.host = null;\n    }\n    mustEndAbs = mustEndAbs && (relPath[0] === '' || srcPath[0] === '');\n  }\n\n  if (isRelAbs) {\n    // it's absolute.\n    result.host = relative.host || relative.host === '' ? relative.host : result.host;\n    result.hostname = relative.hostname || relative.hostname === '' ? relative.hostname : result.hostname;\n    result.search = relative.search;\n    result.query = relative.query;\n    srcPath = relPath;\n    // fall through to the dot-handling below.\n  } else if (relPath.length) {\n    /*\n     * it's relative\n     * throw away the existing file, and take the new path instead.\n     */\n    if (!srcPath) { srcPath = []; }\n    srcPath.pop();\n    srcPath = srcPath.concat(relPath);\n    result.search = relative.search;\n    result.query = relative.query;\n  } else if (relative.search != null) {\n    /*\n     * just pull out the search.\n     * like href='?foo'.\n     * Put this after the other two cases because it simplifies the booleans\n     */\n    if (psychotic) {\n      result.host = srcPath.shift();\n      result.hostname = result.host;\n      /*\n       * occationaly the auth can get stuck only in host\n       * this especially happens in cases like\n       * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n       */\n      var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n      if (authInHost) {\n        result.auth = authInHost.shift();\n        result.hostname = authInHost.shift();\n        result.host = result.hostname;\n      }\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    // to support http.request\n    if (result.pathname !== null || result.search !== null) {\n      result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  if (!srcPath.length) {\n    /*\n     * no path at all.  easy.\n     * we've already handled the other stuff above.\n     */\n    result.pathname = null;\n    // to support http.request\n    if (result.search) {\n      result.path = '/' + result.search;\n    } else {\n      result.path = null;\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  /*\n   * if a url ENDs in . or .., then it must get a trailing slash.\n   * however, if it ends in anything else non-slashy,\n   * then it must NOT get a trailing slash.\n   */\n  var last = srcPath.slice(-1)[0];\n  var hasTrailingSlash = (result.host || relative.host || srcPath.length > 1) && (last === '.' || last === '..') || last === '';\n\n  /*\n   * strip single dots, resolve double dots to parent dir\n   * if the path tries to go above the root, `up` ends up > 0\n   */\n  var up = 0;\n  for (var i = srcPath.length; i >= 0; i--) {\n    last = srcPath[i];\n    if (last === '.') {\n      srcPath.splice(i, 1);\n    } else if (last === '..') {\n      srcPath.splice(i, 1);\n      up++;\n    } else if (up) {\n      srcPath.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (!mustEndAbs && !removeAllDots) {\n    for (; up--; up) {\n      srcPath.unshift('..');\n    }\n  }\n\n  if (mustEndAbs && srcPath[0] !== '' && (!srcPath[0] || srcPath[0].charAt(0) !== '/')) {\n    srcPath.unshift('');\n  }\n\n  if (hasTrailingSlash && (srcPath.join('/').substr(-1) !== '/')) {\n    srcPath.push('');\n  }\n\n  var isAbsolute = srcPath[0] === '' || (srcPath[0] && srcPath[0].charAt(0) === '/');\n\n  // put the host back\n  if (psychotic) {\n    result.hostname = isAbsolute ? '' : srcPath.length ? srcPath.shift() : '';\n    result.host = result.hostname;\n    /*\n     * occationaly the auth can get stuck only in host\n     * this especially happens in cases like\n     * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n     */\n    var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n    if (authInHost) {\n      result.auth = authInHost.shift();\n      result.hostname = authInHost.shift();\n      result.host = result.hostname;\n    }\n  }\n\n  mustEndAbs = mustEndAbs || (result.host && srcPath.length);\n\n  if (mustEndAbs && !isAbsolute) {\n    srcPath.unshift('');\n  }\n\n  if (srcPath.length > 0) {\n    result.pathname = srcPath.join('/');\n  } else {\n    result.pathname = null;\n    result.path = null;\n  }\n\n  // to support request.http\n  if (result.pathname !== null || result.search !== null) {\n    result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n  }\n  result.auth = relative.auth || result.auth;\n  result.slashes = result.slashes || relative.slashes;\n  result.href = result.format();\n  return result;\n};\n\nUrl.prototype.parseHost = function () {\n  var host = this.host;\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) { this.hostname = host; }\n};\n\nexports.parse = urlParse;\nexports.resolve = urlResolve;\nexports.resolveObject = urlResolveObject;\nexports.format = urlFormat;\n\nexports.Url = Url;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACC,KAAC,SAAS,MAAM;AAGhB,UAAI,cAAc,OAAO,WAAW,YAAY,WAC/C,CAAC,QAAQ,YAAY;AACtB,UAAI,aAAa,OAAO,UAAU,YAAY,UAC7C,CAAC,OAAO,YAAY;AACrB,UAAI,aAAa,OAAO,UAAU,YAAY;AAC9C,UACC,WAAW,WAAW,cACtB,WAAW,WAAW,cACtB,WAAW,SAAS,YACnB;AACD,eAAO;AAAA,MACR;AAOA,UAAI,UAGJ,SAAS,YAGT,OAAO,IACP,OAAO,GACP,OAAO,IACP,OAAO,IACP,OAAO,KACP,cAAc,IACd,WAAW,KACX,YAAY,KAGZ,gBAAgB,SAChB,gBAAgB,gBAChB,kBAAkB,6BAGlB,SAAS;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB,GAGA,gBAAgB,OAAO,MACvB,QAAQ,KAAK,OACb,qBAAqB,OAAO,cAG5B;AAUA,eAAS,MAAM,MAAM;AACpB,cAAM,IAAI,WAAW,OAAO,IAAI,CAAC;AAAA,MAClC;AAUA,eAAS,IAAI,OAAO,IAAI;AACvB,YAAI,SAAS,MAAM;AACnB,YAAI,SAAS,CAAC;AACd,eAAO,UAAU;AAChB,iBAAO,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC;AAAA,QAClC;AACA,eAAO;AAAA,MACR;AAYA,eAAS,UAAU,QAAQ,IAAI;AAC9B,YAAI,QAAQ,OAAO,MAAM,GAAG;AAC5B,YAAI,SAAS;AACb,YAAI,MAAM,SAAS,GAAG;AAGrB,mBAAS,MAAM,CAAC,IAAI;AACpB,mBAAS,MAAM,CAAC;AAAA,QACjB;AAEA,iBAAS,OAAO,QAAQ,iBAAiB,GAAM;AAC/C,YAAI,SAAS,OAAO,MAAM,GAAG;AAC7B,YAAI,UAAU,IAAI,QAAQ,EAAE,EAAE,KAAK,GAAG;AACtC,eAAO,SAAS;AAAA,MACjB;AAeA,eAAS,WAAW,QAAQ;AAC3B,YAAI,SAAS,CAAC,GACV,UAAU,GACV,SAAS,OAAO,QAChB,OACA;AACJ,eAAO,UAAU,QAAQ;AACxB,kBAAQ,OAAO,WAAW,SAAS;AACnC,cAAI,SAAS,SAAU,SAAS,SAAU,UAAU,QAAQ;AAE3D,oBAAQ,OAAO,WAAW,SAAS;AACnC,iBAAK,QAAQ,UAAW,OAAQ;AAC/B,qBAAO,OAAO,QAAQ,SAAU,OAAO,QAAQ,QAAS,KAAO;AAAA,YAChE,OAAO;AAGN,qBAAO,KAAK,KAAK;AACjB;AAAA,YACD;AAAA,UACD,OAAO;AACN,mBAAO,KAAK,KAAK;AAAA,UAClB;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAUA,eAAS,WAAW,OAAO;AAC1B,eAAO,IAAI,OAAO,SAAS,OAAO;AACjC,cAAI,SAAS;AACb,cAAI,QAAQ,OAAQ;AACnB,qBAAS;AACT,sBAAU,mBAAmB,UAAU,KAAK,OAAQ,KAAM;AAC1D,oBAAQ,QAAS,QAAQ;AAAA,UAC1B;AACA,oBAAU,mBAAmB,KAAK;AAClC,iBAAO;AAAA,QACR,CAAC,EAAE,KAAK,EAAE;AAAA,MACX;AAWA,eAAS,aAAa,WAAW;AAChC,YAAI,YAAY,KAAK,IAAI;AACxB,iBAAO,YAAY;AAAA,QACpB;AACA,YAAI,YAAY,KAAK,IAAI;AACxB,iBAAO,YAAY;AAAA,QACpB;AACA,YAAI,YAAY,KAAK,IAAI;AACxB,iBAAO,YAAY;AAAA,QACpB;AACA,eAAO;AAAA,MACR;AAaA,eAAS,aAAa,OAAO,MAAM;AAGlC,eAAO,QAAQ,KAAK,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACzD;AAOA,eAAS,MAAM,OAAO,WAAW,WAAW;AAC3C,YAAI,IAAI;AACR,gBAAQ,YAAY,MAAM,QAAQ,IAAI,IAAI,SAAS;AACnD,iBAAS,MAAM,QAAQ,SAAS;AAChC,eAA8B,QAAQ,gBAAgB,QAAQ,GAAG,KAAK,MAAM;AAC3E,kBAAQ,MAAM,QAAQ,aAAa;AAAA,QACpC;AACA,eAAO,MAAM,KAAK,gBAAgB,KAAK,SAAS,QAAQ,KAAK;AAAA,MAC9D;AASA,eAAS,OAAO,OAAO;AAEtB,YAAI,SAAS,CAAC,GACV,cAAc,MAAM,QACpB,KACA,IAAI,GACJ,IAAI,UACJ,OAAO,aACP,OACA,GACA,OACA,MACA,GACA,GACA,OACA,GAEA;AAMJ,gBAAQ,MAAM,YAAY,SAAS;AACnC,YAAI,QAAQ,GAAG;AACd,kBAAQ;AAAA,QACT;AAEA,aAAK,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAE3B,cAAI,MAAM,WAAW,CAAC,KAAK,KAAM;AAChC,kBAAM,WAAW;AAAA,UAClB;AACA,iBAAO,KAAK,MAAM,WAAW,CAAC,CAAC;AAAA,QAChC;AAKA,aAAK,QAAQ,QAAQ,IAAI,QAAQ,IAAI,GAAG,QAAQ,eAAwC;AAOvF,eAAK,OAAO,GAAG,IAAI,GAAG,IAAI,QAA0B,KAAK,MAAM;AAE9D,gBAAI,SAAS,aAAa;AACzB,oBAAM,eAAe;AAAA,YACtB;AAEA,oBAAQ,aAAa,MAAM,WAAW,OAAO,CAAC;AAE9C,gBAAI,SAAS,QAAQ,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG;AACrD,oBAAM,UAAU;AAAA,YACjB;AAEA,iBAAK,QAAQ;AACb,gBAAI,KAAK,OAAO,OAAQ,KAAK,OAAO,OAAO,OAAO,IAAI;AAEtD,gBAAI,QAAQ,GAAG;AACd;AAAA,YACD;AAEA,yBAAa,OAAO;AACpB,gBAAI,IAAI,MAAM,SAAS,UAAU,GAAG;AACnC,oBAAM,UAAU;AAAA,YACjB;AAEA,iBAAK;AAAA,UAEN;AAEA,gBAAM,OAAO,SAAS;AACtB,iBAAO,MAAM,IAAI,MAAM,KAAK,QAAQ,CAAC;AAIrC,cAAI,MAAM,IAAI,GAAG,IAAI,SAAS,GAAG;AAChC,kBAAM,UAAU;AAAA,UACjB;AAEA,eAAK,MAAM,IAAI,GAAG;AAClB,eAAK;AAGL,iBAAO,OAAO,KAAK,GAAG,CAAC;AAAA,QAExB;AAEA,eAAO,WAAW,MAAM;AAAA,MACzB;AASA,eAAS,OAAO,OAAO;AACtB,YAAI,GACA,OACA,gBACA,aACA,MACA,GACA,GACA,GACA,GACA,GACA,cACA,SAAS,CAAC,GAEV,aAEA,uBACA,YACA;AAGJ,gBAAQ,WAAW,KAAK;AAGxB,sBAAc,MAAM;AAGpB,YAAI;AACJ,gBAAQ;AACR,eAAO;AAGP,aAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACjC,yBAAe,MAAM,CAAC;AACtB,cAAI,eAAe,KAAM;AACxB,mBAAO,KAAK,mBAAmB,YAAY,CAAC;AAAA,UAC7C;AAAA,QACD;AAEA,yBAAiB,cAAc,OAAO;AAMtC,YAAI,aAAa;AAChB,iBAAO,KAAK,SAAS;AAAA,QACtB;AAGA,eAAO,iBAAiB,aAAa;AAIpC,eAAK,IAAI,QAAQ,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAC7C,2BAAe,MAAM,CAAC;AACtB,gBAAI,gBAAgB,KAAK,eAAe,GAAG;AAC1C,kBAAI;AAAA,YACL;AAAA,UACD;AAIA,kCAAwB,iBAAiB;AACzC,cAAI,IAAI,IAAI,OAAO,SAAS,SAAS,qBAAqB,GAAG;AAC5D,kBAAM,UAAU;AAAA,UACjB;AAEA,oBAAU,IAAI,KAAK;AACnB,cAAI;AAEJ,eAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACjC,2BAAe,MAAM,CAAC;AAEtB,gBAAI,eAAe,KAAK,EAAE,QAAQ,QAAQ;AACzC,oBAAM,UAAU;AAAA,YACjB;AAEA,gBAAI,gBAAgB,GAAG;AAEtB,mBAAK,IAAI,OAAO,IAAI,QAA0B,KAAK,MAAM;AACxD,oBAAI,KAAK,OAAO,OAAQ,KAAK,OAAO,OAAO,OAAO,IAAI;AACtD,oBAAI,IAAI,GAAG;AACV;AAAA,gBACD;AACA,0BAAU,IAAI;AACd,6BAAa,OAAO;AACpB,uBAAO;AAAA,kBACN,mBAAmB,aAAa,IAAI,UAAU,YAAY,CAAC,CAAC;AAAA,gBAC7D;AACA,oBAAI,MAAM,UAAU,UAAU;AAAA,cAC/B;AAEA,qBAAO,KAAK,mBAAmB,aAAa,GAAG,CAAC,CAAC,CAAC;AAClD,qBAAO,MAAM,OAAO,uBAAuB,kBAAkB,WAAW;AACxE,sBAAQ;AACR,gBAAE;AAAA,YACH;AAAA,UACD;AAEA,YAAE;AACF,YAAE;AAAA,QAEH;AACA,eAAO,OAAO,KAAK,EAAE;AAAA,MACtB;AAaA,eAAS,UAAU,OAAO;AACzB,eAAO,UAAU,OAAO,SAAS,QAAQ;AACxC,iBAAO,cAAc,KAAK,MAAM,IAC7B,OAAO,OAAO,MAAM,CAAC,EAAE,YAAY,CAAC,IACpC;AAAA,QACJ,CAAC;AAAA,MACF;AAaA,eAAS,QAAQ,OAAO;AACvB,eAAO,UAAU,OAAO,SAAS,QAAQ;AACxC,iBAAO,cAAc,KAAK,MAAM,IAC7B,SAAS,OAAO,MAAM,IACtB;AAAA,QACJ,CAAC;AAAA,MACF;AAKA,iBAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQX,QAAQ;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,QACX;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,MACd;AAKA,UACC,OAAO,UAAU,cACjB,OAAO,OAAO,OAAO,YACrB,OAAO,KACN;AACD,eAAO,YAAY,WAAW;AAC7B,iBAAO;AAAA,QACR,CAAC;AAAA,MACF,WAAW,eAAe,YAAY;AACrC,YAAI,OAAO,WAAW,aAAa;AAElC,qBAAW,UAAU;AAAA,QACtB,OAAO;AAEN,eAAK,OAAO,UAAU;AACrB,qBAAS,eAAe,GAAG,MAAM,YAAY,GAAG,IAAI,SAAS,GAAG;AAAA,UACjE;AAAA,QACD;AAAA,MACD,OAAO;AAEN,aAAK,WAAW;AAAA,MACjB;AAAA,IAED,GAAE,OAAI;AAAA;AAAA;;;ACphBN;AAAA;AAyBA,QAAI,WAAW;AAEf,aAAS,MAAM;AACb,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,QAAQ;AACb,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,WAAK,OAAO;AAAA,IACd;AAQA,QAAI,kBAAkB;AAAtB,QACE,cAAc;AADhB,QAIE,oBAAoB;AAJtB,QAUE,SAAS;AAAA,MACP;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,IACvC;AAZF,QAeE,SAAS;AAAA,MACP;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAK;AAAA,IAC5B,EAAE,OAAO,MAAM;AAjBjB,QAoBE,aAAa,CAAC,GAAI,EAAE,OAAO,MAAM;AApBnC,QA2BE,eAAe;AAAA,MACb;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,IACtB,EAAE,OAAO,UAAU;AA7BrB,QA8BE,kBAAkB;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,IACZ;AAhCF,QAiCE,iBAAiB;AAjCnB,QAkCE,sBAAsB;AAlCxB,QAmCE,oBAAoB;AAnCtB,QAqCE,iBAAiB;AAAA,MACf,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAxCF,QA0CE,mBAAmB;AAAA,MACjB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AA7CF,QA+CE,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AA1DF,QA2DE,cAAc;AAEhB,aAAS,SAAS,KAAK,kBAAkB,mBAAmB;AAC1D,UAAI,OAAO,OAAO,QAAQ,YAAY,eAAe,KAAK;AAAE,eAAO;AAAA,MAAK;AAExE,UAAI,IAAI,IAAI,IAAI;AAChB,QAAE,MAAM,KAAK,kBAAkB,iBAAiB;AAChD,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,QAAQ,SAAU,KAAK,kBAAkB,mBAAmB;AACxE,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,UAAU,2CAA2C,OAAO,GAAG;AAAA,MAC3E;AAOA,UAAI,aAAa,IAAI,QAAQ,GAAG,GAC9B,WAAW,eAAe,MAAM,aAAa,IAAI,QAAQ,GAAG,IAAI,MAAM,KACtE,SAAS,IAAI,MAAM,QAAQ,GAC3B,aAAa;AACf,aAAO,CAAC,IAAI,OAAO,CAAC,EAAE,QAAQ,YAAY,GAAG;AAC7C,YAAM,OAAO,KAAK,QAAQ;AAE1B,UAAI,OAAO;AAMX,aAAO,KAAK,KAAK;AAEjB,UAAI,CAAC,qBAAqB,IAAI,MAAM,GAAG,EAAE,WAAW,GAAG;AAErD,YAAI,aAAa,kBAAkB,KAAK,IAAI;AAC5C,YAAI,YAAY;AACd,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,WAAW,WAAW,CAAC;AAC5B,cAAI,WAAW,CAAC,GAAG;AACjB,iBAAK,SAAS,WAAW,CAAC;AAC1B,gBAAI,kBAAkB;AACpB,mBAAK,QAAQ,YAAY,MAAM,KAAK,OAAO,OAAO,CAAC,CAAC;AAAA,YACtD,OAAO;AACL,mBAAK,QAAQ,KAAK,OAAO,OAAO,CAAC;AAAA,YACnC;AAAA,UACF,WAAW,kBAAkB;AAC3B,iBAAK,SAAS;AACd,iBAAK,QAAQ,CAAC;AAAA,UAChB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,QAAQ,gBAAgB,KAAK,IAAI;AACrC,UAAI,OAAO;AACT,gBAAQ,MAAM,CAAC;AACf,YAAI,aAAa,MAAM,YAAY;AACnC,aAAK,WAAW;AAChB,eAAO,KAAK,OAAO,MAAM,MAAM;AAAA,MACjC;AAQA,UAAI,qBAAqB,SAAS,KAAK,MAAM,oBAAoB,GAAG;AAClE,YAAI,UAAU,KAAK,OAAO,GAAG,CAAC,MAAM;AACpC,YAAI,WAAW,EAAE,SAAS,iBAAiB,KAAK,IAAI;AAClD,iBAAO,KAAK,OAAO,CAAC;AACpB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAEA,UAAI,CAAC,iBAAiB,KAAK,MAAM,WAAY,SAAS,CAAC,gBAAgB,KAAK,IAAK;AAsB/E,YAAI,UAAU;AACd,iBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,cAAI,MAAM,KAAK,QAAQ,gBAAgB,CAAC,CAAC;AACzC,cAAI,QAAQ,OAAO,YAAY,MAAM,MAAM,UAAU;AAAE,sBAAU;AAAA,UAAK;AAAA,QACxE;AAMA,YAAI,MAAM;AACV,YAAI,YAAY,IAAI;AAElB,mBAAS,KAAK,YAAY,GAAG;AAAA,QAC/B,OAAO;AAKL,mBAAS,KAAK,YAAY,KAAK,OAAO;AAAA,QACxC;AAMA,YAAI,WAAW,IAAI;AACjB,iBAAO,KAAK,MAAM,GAAG,MAAM;AAC3B,iBAAO,KAAK,MAAM,SAAS,CAAC;AAC5B,eAAK,OAAO,mBAAmB,IAAI;AAAA,QACrC;AAGA,kBAAU;AACV,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,cAAI,MAAM,KAAK,QAAQ,aAAa,CAAC,CAAC;AACtC,cAAI,QAAQ,OAAO,YAAY,MAAM,MAAM,UAAU;AAAE,sBAAU;AAAA,UAAK;AAAA,QACxE;AAEA,YAAI,YAAY,IAAI;AAAE,oBAAU,KAAK;AAAA,QAAQ;AAE7C,aAAK,OAAO,KAAK,MAAM,GAAG,OAAO;AACjC,eAAO,KAAK,MAAM,OAAO;AAGzB,aAAK,UAAU;AAMf,aAAK,WAAW,KAAK,YAAY;AAMjC,YAAI,eAAe,KAAK,SAAS,CAAC,MAAM,OAAO,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,MAAM;AAG3F,YAAI,CAAC,cAAc;AACjB,cAAI,YAAY,KAAK,SAAS,MAAM,IAAI;AACxC,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,gBAAI,OAAO,UAAU,CAAC;AACtB,gBAAI,CAAC,MAAM;AAAE;AAAA,YAAU;AACvB,gBAAI,CAAC,KAAK,MAAM,mBAAmB,GAAG;AACpC,kBAAI,UAAU;AACd,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,oBAAI,KAAK,WAAW,CAAC,IAAI,KAAK;AAM5B,6BAAW;AAAA,gBACb,OAAO;AACL,6BAAW,KAAK,CAAC;AAAA,gBACnB;AAAA,cACF;AAEA,kBAAI,CAAC,QAAQ,MAAM,mBAAmB,GAAG;AACvC,oBAAI,aAAa,UAAU,MAAM,GAAG,CAAC;AACrC,oBAAI,UAAU,UAAU,MAAM,IAAI,CAAC;AACnC,oBAAI,MAAM,KAAK,MAAM,iBAAiB;AACtC,oBAAI,KAAK;AACP,6BAAW,KAAK,IAAI,CAAC,CAAC;AACtB,0BAAQ,QAAQ,IAAI,CAAC,CAAC;AAAA,gBACxB;AACA,oBAAI,QAAQ,QAAQ;AAClB,yBAAO,MAAM,QAAQ,KAAK,GAAG,IAAI;AAAA,gBACnC;AACA,qBAAK,WAAW,WAAW,KAAK,GAAG;AACnC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,KAAK,SAAS,SAAS,gBAAgB;AACzC,eAAK,WAAW;AAAA,QAClB,OAAO;AAEL,eAAK,WAAW,KAAK,SAAS,YAAY;AAAA,QAC5C;AAEA,YAAI,CAAC,cAAc;AAOjB,eAAK,WAAW,SAAS,QAAQ,KAAK,QAAQ;AAAA,QAChD;AAEA,YAAI,IAAI,KAAK,OAAO,MAAM,KAAK,OAAO;AACtC,YAAI,IAAI,KAAK,YAAY;AACzB,aAAK,OAAO,IAAI;AAChB,aAAK,QAAQ,KAAK;AAMlB,YAAI,cAAc;AAChB,eAAK,WAAW,KAAK,SAAS,OAAO,GAAG,KAAK,SAAS,SAAS,CAAC;AAChE,cAAI,KAAK,CAAC,MAAM,KAAK;AACnB,mBAAO,MAAM;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAMA,UAAI,CAAC,eAAe,UAAU,GAAG;AAO/B,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,KAAK,WAAW,CAAC;AACrB,cAAI,KAAK,QAAQ,EAAE,MAAM,IAAI;AAAE;AAAA,UAAU;AACzC,cAAI,MAAM,mBAAmB,EAAE;AAC/B,cAAI,QAAQ,IAAI;AACd,kBAAM,OAAO,EAAE;AAAA,UACjB;AACA,iBAAO,KAAK,MAAM,EAAE,EAAE,KAAK,GAAG;AAAA,QAChC;AAAA,MACF;AAGA,UAAI,OAAO,KAAK,QAAQ,GAAG;AAC3B,UAAI,SAAS,IAAI;AAEf,aAAK,OAAO,KAAK,OAAO,IAAI;AAC5B,eAAO,KAAK,MAAM,GAAG,IAAI;AAAA,MAC3B;AACA,UAAI,KAAK,KAAK,QAAQ,GAAG;AACzB,UAAI,OAAO,IAAI;AACb,aAAK,SAAS,KAAK,OAAO,EAAE;AAC5B,aAAK,QAAQ,KAAK,OAAO,KAAK,CAAC;AAC/B,YAAI,kBAAkB;AACpB,eAAK,QAAQ,YAAY,MAAM,KAAK,KAAK;AAAA,QAC3C;AACA,eAAO,KAAK,MAAM,GAAG,EAAE;AAAA,MACzB,WAAW,kBAAkB;AAE3B,aAAK,SAAS;AACd,aAAK,QAAQ,CAAC;AAAA,MAChB;AACA,UAAI,MAAM;AAAE,aAAK,WAAW;AAAA,MAAM;AAClC,UAAI,gBAAgB,UAAU,KAAK,KAAK,YAAY,CAAC,KAAK,UAAU;AAClE,aAAK,WAAW;AAAA,MAClB;AAGA,UAAI,KAAK,YAAY,KAAK,QAAQ;AAChC,YAAI,IAAI,KAAK,YAAY;AACzB,YAAI,IAAI,KAAK,UAAU;AACvB,aAAK,OAAO,IAAI;AAAA,MAClB;AAGA,WAAK,OAAO,KAAK,OAAO;AACxB,aAAO;AAAA,IACT;AAGA,aAAS,UAAU,KAAK;AAOtB,UAAI,OAAO,QAAQ,UAAU;AAAE,cAAM,SAAS,GAAG;AAAA,MAAG;AACpD,UAAI,EAAE,eAAe,MAAM;AAAE,eAAO,IAAI,UAAU,OAAO,KAAK,GAAG;AAAA,MAAG;AACpE,aAAO,IAAI,OAAO;AAAA,IACpB;AAEA,QAAI,UAAU,SAAS,WAAY;AACjC,UAAI,OAAO,KAAK,QAAQ;AACxB,UAAI,MAAM;AACR,eAAO,mBAAmB,IAAI;AAC9B,eAAO,KAAK,QAAQ,QAAQ,GAAG;AAC/B,gBAAQ;AAAA,MACV;AAEA,UAAI,WAAW,KAAK,YAAY,IAC9B,WAAW,KAAK,YAAY,IAC5B,OAAO,KAAK,QAAQ,IACpB,OAAO,OACP,QAAQ;AAEV,UAAI,KAAK,MAAM;AACb,eAAO,OAAO,KAAK;AAAA,MACrB,WAAW,KAAK,UAAU;AACxB,eAAO,QAAQ,KAAK,SAAS,QAAQ,GAAG,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,WAAW;AACzF,YAAI,KAAK,MAAM;AACb,kBAAQ,MAAM,KAAK;AAAA,QACrB;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,KAAK,KAAK,EAAE,QAAQ;AAClF,gBAAQ,YAAY,UAAU,KAAK,OAAO;AAAA,UACxC,aAAa;AAAA,UACb,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,KAAK,UAAW,SAAU,MAAM,SAAW;AAExD,UAAI,YAAY,SAAS,OAAO,EAAE,MAAM,KAAK;AAAE,oBAAY;AAAA,MAAK;AAMhE,UAAI,KAAK,YAAY,CAAC,YAAY,gBAAgB,QAAQ,MAAM,SAAS,OAAO;AAC9E,eAAO,QAAQ,QAAQ;AACvB,YAAI,YAAY,SAAS,OAAO,CAAC,MAAM,KAAK;AAAE,qBAAW,MAAM;AAAA,QAAU;AAAA,MAC3E,WAAW,CAAC,MAAM;AAChB,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,KAAK,OAAO,CAAC,MAAM,KAAK;AAAE,eAAO,MAAM;AAAA,MAAM;AACzD,UAAI,UAAU,OAAO,OAAO,CAAC,MAAM,KAAK;AAAE,iBAAS,MAAM;AAAA,MAAQ;AAEjE,iBAAW,SAAS,QAAQ,SAAS,SAAU,OAAO;AACpD,eAAO,mBAAmB,KAAK;AAAA,MACjC,CAAC;AACD,eAAS,OAAO,QAAQ,KAAK,KAAK;AAElC,aAAO,WAAW,OAAO,WAAW,SAAS;AAAA,IAC/C;AAEA,aAAS,WAAW,QAAQ,UAAU;AACpC,aAAO,SAAS,QAAQ,OAAO,IAAI,EAAE,QAAQ,QAAQ;AAAA,IACvD;AAEA,QAAI,UAAU,UAAU,SAAU,UAAU;AAC1C,aAAO,KAAK,cAAc,SAAS,UAAU,OAAO,IAAI,CAAC,EAAE,OAAO;AAAA,IACpE;AAEA,aAAS,iBAAiB,QAAQ,UAAU;AAC1C,UAAI,CAAC,QAAQ;AAAE,eAAO;AAAA,MAAU;AAChC,aAAO,SAAS,QAAQ,OAAO,IAAI,EAAE,cAAc,QAAQ;AAAA,IAC7D;AAEA,QAAI,UAAU,gBAAgB,SAAU,UAAU;AAChD,UAAI,OAAO,aAAa,UAAU;AAChC,YAAI,MAAM,IAAI,IAAI;AAClB,YAAI,MAAM,UAAU,OAAO,IAAI;AAC/B,mBAAW;AAAA,MACb;AAEA,UAAI,SAAS,IAAI,IAAI;AACrB,UAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,eAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,YAAI,OAAO,MAAM,EAAE;AACnB,eAAO,IAAI,IAAI,KAAK,IAAI;AAAA,MAC1B;AAMA,aAAO,OAAO,SAAS;AAGvB,UAAI,SAAS,SAAS,IAAI;AACxB,eAAO,OAAO,OAAO,OAAO;AAC5B,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,WAAW,CAAC,SAAS,UAAU;AAE1C,YAAI,QAAQ,OAAO,KAAK,QAAQ;AAChC,iBAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,cAAI,OAAO,MAAM,EAAE;AACnB,cAAI,SAAS,YAAY;AAAE,mBAAO,IAAI,IAAI,SAAS,IAAI;AAAA,UAAG;AAAA,QAC5D;AAGA,YAAI,gBAAgB,OAAO,QAAQ,KAAK,OAAO,YAAY,CAAC,OAAO,UAAU;AAC3E,iBAAO,WAAW;AAClB,iBAAO,OAAO,OAAO;AAAA,QACvB;AAEA,eAAO,OAAO,OAAO,OAAO;AAC5B,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,YAAY,SAAS,aAAa,OAAO,UAAU;AAW9D,YAAI,CAAC,gBAAgB,SAAS,QAAQ,GAAG;AACvC,cAAI,OAAO,OAAO,KAAK,QAAQ;AAC/B,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAI,IAAI,KAAK,CAAC;AACd,mBAAO,CAAC,IAAI,SAAS,CAAC;AAAA,UACxB;AACA,iBAAO,OAAO,OAAO,OAAO;AAC5B,iBAAO;AAAA,QACT;AAEA,eAAO,WAAW,SAAS;AAC3B,YAAI,CAAC,SAAS,QAAQ,CAAC,iBAAiB,SAAS,QAAQ,GAAG;AAC1D,cAAI,WAAW,SAAS,YAAY,IAAI,MAAM,GAAG;AACjD,iBAAO,QAAQ,UAAU,EAAE,SAAS,OAAO,QAAQ,MAAM,IAAI;AAAA,UAAE;AAC/D,cAAI,CAAC,SAAS,MAAM;AAAE,qBAAS,OAAO;AAAA,UAAI;AAC1C,cAAI,CAAC,SAAS,UAAU;AAAE,qBAAS,WAAW;AAAA,UAAI;AAClD,cAAI,QAAQ,CAAC,MAAM,IAAI;AAAE,oBAAQ,QAAQ,EAAE;AAAA,UAAG;AAC9C,cAAI,QAAQ,SAAS,GAAG;AAAE,oBAAQ,QAAQ,EAAE;AAAA,UAAG;AAC/C,iBAAO,WAAW,QAAQ,KAAK,GAAG;AAAA,QACpC,OAAO;AACL,iBAAO,WAAW,SAAS;AAAA,QAC7B;AACA,eAAO,SAAS,SAAS;AACzB,eAAO,QAAQ,SAAS;AACxB,eAAO,OAAO,SAAS,QAAQ;AAC/B,eAAO,OAAO,SAAS;AACvB,eAAO,WAAW,SAAS,YAAY,SAAS;AAChD,eAAO,OAAO,SAAS;AAEvB,YAAI,OAAO,YAAY,OAAO,QAAQ;AACpC,cAAI,IAAI,OAAO,YAAY;AAC3B,cAAI,IAAI,OAAO,UAAU;AACzB,iBAAO,OAAO,IAAI;AAAA,QACpB;AACA,eAAO,UAAU,OAAO,WAAW,SAAS;AAC5C,eAAO,OAAO,OAAO,OAAO;AAC5B,eAAO;AAAA,MACT;AAEA,UAAI,cAAc,OAAO,YAAY,OAAO,SAAS,OAAO,CAAC,MAAM,KACjE,WAAW,SAAS,QAAQ,SAAS,YAAY,SAAS,SAAS,OAAO,CAAC,MAAM,KACjF,aAAa,YAAY,eAAgB,OAAO,QAAQ,SAAS,UACjE,gBAAgB,YAChB,UAAU,OAAO,YAAY,OAAO,SAAS,MAAM,GAAG,KAAK,CAAC,GAC5D,UAAU,SAAS,YAAY,SAAS,SAAS,MAAM,GAAG,KAAK,CAAC,GAChE,YAAY,OAAO,YAAY,CAAC,gBAAgB,OAAO,QAAQ;AASjE,UAAI,WAAW;AACb,eAAO,WAAW;AAClB,eAAO,OAAO;AACd,YAAI,OAAO,MAAM;AACf,cAAI,QAAQ,CAAC,MAAM,IAAI;AAAE,oBAAQ,CAAC,IAAI,OAAO;AAAA,UAAM,OAAO;AAAE,oBAAQ,QAAQ,OAAO,IAAI;AAAA,UAAG;AAAA,QAC5F;AACA,eAAO,OAAO;AACd,YAAI,SAAS,UAAU;AACrB,mBAAS,WAAW;AACpB,mBAAS,OAAO;AAChB,cAAI,SAAS,MAAM;AACjB,gBAAI,QAAQ,CAAC,MAAM,IAAI;AAAE,sBAAQ,CAAC,IAAI,SAAS;AAAA,YAAM,OAAO;AAAE,sBAAQ,QAAQ,SAAS,IAAI;AAAA,YAAG;AAAA,UAChG;AACA,mBAAS,OAAO;AAAA,QAClB;AACA,qBAAa,eAAe,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,MAAM;AAAA,MAClE;AAEA,UAAI,UAAU;AAEZ,eAAO,OAAO,SAAS,QAAQ,SAAS,SAAS,KAAK,SAAS,OAAO,OAAO;AAC7E,eAAO,WAAW,SAAS,YAAY,SAAS,aAAa,KAAK,SAAS,WAAW,OAAO;AAC7F,eAAO,SAAS,SAAS;AACzB,eAAO,QAAQ,SAAS;AACxB,kBAAU;AAAA,MAEZ,WAAW,QAAQ,QAAQ;AAKzB,YAAI,CAAC,SAAS;AAAE,oBAAU,CAAC;AAAA,QAAG;AAC9B,gBAAQ,IAAI;AACZ,kBAAU,QAAQ,OAAO,OAAO;AAChC,eAAO,SAAS,SAAS;AACzB,eAAO,QAAQ,SAAS;AAAA,MAC1B,WAAW,SAAS,UAAU,MAAM;AAMlC,YAAI,WAAW;AACb,iBAAO,OAAO,QAAQ,MAAM;AAC5B,iBAAO,WAAW,OAAO;AAMzB,cAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,QAAQ,GAAG,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,IAAI;AACxF,cAAI,YAAY;AACd,mBAAO,OAAO,WAAW,MAAM;AAC/B,mBAAO,WAAW,WAAW,MAAM;AACnC,mBAAO,OAAO,OAAO;AAAA,UACvB;AAAA,QACF;AACA,eAAO,SAAS,SAAS;AACzB,eAAO,QAAQ,SAAS;AAExB,YAAI,OAAO,aAAa,QAAQ,OAAO,WAAW,MAAM;AACtD,iBAAO,QAAQ,OAAO,WAAW,OAAO,WAAW,OAAO,OAAO,SAAS,OAAO,SAAS;AAAA,QAC5F;AACA,eAAO,OAAO,OAAO,OAAO;AAC5B,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,QAAQ,QAAQ;AAKnB,eAAO,WAAW;AAElB,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,MAAM,OAAO;AAAA,QAC7B,OAAO;AACL,iBAAO,OAAO;AAAA,QAChB;AACA,eAAO,OAAO,OAAO,OAAO;AAC5B,eAAO;AAAA,MACT;AAOA,UAAI,OAAO,QAAQ,MAAM,EAAE,EAAE,CAAC;AAC9B,UAAI,oBAAoB,OAAO,QAAQ,SAAS,QAAQ,QAAQ,SAAS,OAAO,SAAS,OAAO,SAAS,SAAS,SAAS;AAM3H,UAAI,KAAK;AACT,eAAS,IAAI,QAAQ,QAAQ,KAAK,GAAG,KAAK;AACxC,eAAO,QAAQ,CAAC;AAChB,YAAI,SAAS,KAAK;AAChB,kBAAQ,OAAO,GAAG,CAAC;AAAA,QACrB,WAAW,SAAS,MAAM;AACxB,kBAAQ,OAAO,GAAG,CAAC;AACnB;AAAA,QACF,WAAW,IAAI;AACb,kBAAQ,OAAO,GAAG,CAAC;AACnB;AAAA,QACF;AAAA,MACF;AAGA,UAAI,CAAC,cAAc,CAAC,eAAe;AACjC,eAAO,MAAM,IAAI;AACf,kBAAQ,QAAQ,IAAI;AAAA,QACtB;AAAA,MACF;AAEA,UAAI,cAAc,QAAQ,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM;AACpF,gBAAQ,QAAQ,EAAE;AAAA,MACpB;AAEA,UAAI,oBAAqB,QAAQ,KAAK,GAAG,EAAE,OAAO,EAAE,MAAM,KAAM;AAC9D,gBAAQ,KAAK,EAAE;AAAA,MACjB;AAEA,UAAI,aAAa,QAAQ,CAAC,MAAM,MAAO,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,OAAO,CAAC,MAAM;AAG9E,UAAI,WAAW;AACb,eAAO,WAAW,aAAa,KAAK,QAAQ,SAAS,QAAQ,MAAM,IAAI;AACvE,eAAO,OAAO,OAAO;AAMrB,YAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,QAAQ,GAAG,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,IAAI;AACxF,YAAI,YAAY;AACd,iBAAO,OAAO,WAAW,MAAM;AAC/B,iBAAO,WAAW,WAAW,MAAM;AACnC,iBAAO,OAAO,OAAO;AAAA,QACvB;AAAA,MACF;AAEA,mBAAa,cAAe,OAAO,QAAQ,QAAQ;AAEnD,UAAI,cAAc,CAAC,YAAY;AAC7B,gBAAQ,QAAQ,EAAE;AAAA,MACpB;AAEA,UAAI,QAAQ,SAAS,GAAG;AACtB,eAAO,WAAW,QAAQ,KAAK,GAAG;AAAA,MACpC,OAAO;AACL,eAAO,WAAW;AAClB,eAAO,OAAO;AAAA,MAChB;AAGA,UAAI,OAAO,aAAa,QAAQ,OAAO,WAAW,MAAM;AACtD,eAAO,QAAQ,OAAO,WAAW,OAAO,WAAW,OAAO,OAAO,SAAS,OAAO,SAAS;AAAA,MAC5F;AACA,aAAO,OAAO,SAAS,QAAQ,OAAO;AACtC,aAAO,UAAU,OAAO,WAAW,SAAS;AAC5C,aAAO,OAAO,OAAO,OAAO;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,YAAY,WAAY;AACpC,UAAI,OAAO,KAAK;AAChB,UAAI,OAAO,YAAY,KAAK,IAAI;AAChC,UAAI,MAAM;AACR,eAAO,KAAK,CAAC;AACb,YAAI,SAAS,KAAK;AAChB,eAAK,OAAO,KAAK,OAAO,CAAC;AAAA,QAC3B;AACA,eAAO,KAAK,OAAO,GAAG,KAAK,SAAS,KAAK,MAAM;AAAA,MACjD;AACA,UAAI,MAAM;AAAE,aAAK,WAAW;AAAA,MAAM;AAAA,IACpC;AAEA,YAAQ,QAAQ;AAChB,YAAQ,UAAU;AAClB,YAAQ,gBAAgB;AACxB,YAAQ,SAAS;AAEjB,YAAQ,MAAM;AAAA;AAAA;", "names": []}