import {
  getParent
} from "./chunk-KKQ6WPIB.js";
import {
  asBounds,
  asTRBL,
  getBBox
} from "./chunk-T4R4535C.js";
import {
  isHorizontal
} from "./chunk-7C6J56BH.js";
import {
  is
} from "./chunk-FNF472WR.js";
import {
  filter,
  isNumber
} from "./chunk-YTJ5ESGD.js";

// node_modules/.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/features/resize/ResizeUtil.js
var max = Math.max;
var min = Math.min;
var DEFAULT_CHILD_BOX_PADDING = 20;
function substractTRBL(trblA, trblB) {
  return {
    top: trblA.top - trblB.top,
    right: trblA.right - trblB.right,
    bottom: trblA.bottom - trblB.bottom,
    left: trblA.left - trblB.left
  };
}
function resizeBounds(bounds, direction, delta) {
  var dx = delta.x, dy = delta.y;
  var newBounds = {
    x: bounds.x,
    y: bounds.y,
    width: bounds.width,
    height: bounds.height
  };
  if (direction.indexOf("n") !== -1) {
    newBounds.y = bounds.y + dy;
    newBounds.height = bounds.height - dy;
  } else if (direction.indexOf("s") !== -1) {
    newBounds.height = bounds.height + dy;
  }
  if (direction.indexOf("e") !== -1) {
    newBounds.width = bounds.width + dx;
  } else if (direction.indexOf("w") !== -1) {
    newBounds.x = bounds.x + dx;
    newBounds.width = bounds.width - dx;
  }
  return newBounds;
}
function resizeTRBL(bounds, resize) {
  return {
    x: bounds.x + (resize.left || 0),
    y: bounds.y + (resize.top || 0),
    width: bounds.width - (resize.left || 0) + (resize.right || 0),
    height: bounds.height - (resize.top || 0) + (resize.bottom || 0)
  };
}
function applyConstraints(attr, trbl, resizeConstraints) {
  var value = trbl[attr], minValue = resizeConstraints.min && resizeConstraints.min[attr], maxValue = resizeConstraints.max && resizeConstraints.max[attr];
  if (isNumber(minValue)) {
    value = (/top|left/.test(attr) ? min : max)(value, minValue);
  }
  if (isNumber(maxValue)) {
    value = (/top|left/.test(attr) ? max : min)(value, maxValue);
  }
  return value;
}
function ensureConstraints(currentBounds, resizeConstraints) {
  if (!resizeConstraints) {
    return currentBounds;
  }
  var currentTrbl = asTRBL(currentBounds);
  return asBounds({
    top: applyConstraints("top", currentTrbl, resizeConstraints),
    right: applyConstraints("right", currentTrbl, resizeConstraints),
    bottom: applyConstraints("bottom", currentTrbl, resizeConstraints),
    left: applyConstraints("left", currentTrbl, resizeConstraints)
  });
}
function getMinResizeBounds(direction, currentBounds, minDimensions, childrenBounds) {
  var currentBox = asTRBL(currentBounds);
  var minBox = {
    top: /n/.test(direction) ? currentBox.bottom - minDimensions.height : currentBox.top,
    left: /w/.test(direction) ? currentBox.right - minDimensions.width : currentBox.left,
    bottom: /s/.test(direction) ? currentBox.top + minDimensions.height : currentBox.bottom,
    right: /e/.test(direction) ? currentBox.left + minDimensions.width : currentBox.right
  };
  var childrenBox = childrenBounds ? asTRBL(childrenBounds) : minBox;
  var combinedBox = {
    top: min(minBox.top, childrenBox.top),
    left: min(minBox.left, childrenBox.left),
    bottom: max(minBox.bottom, childrenBox.bottom),
    right: max(minBox.right, childrenBox.right)
  };
  return asBounds(combinedBox);
}
function asPadding(mayBePadding, defaultValue) {
  if (typeof mayBePadding !== "undefined") {
    return mayBePadding;
  } else {
    return DEFAULT_CHILD_BOX_PADDING;
  }
}
function addPadding(bbox, padding) {
  var left, right, top, bottom;
  if (typeof padding === "object") {
    left = asPadding(padding.left);
    right = asPadding(padding.right);
    top = asPadding(padding.top);
    bottom = asPadding(padding.bottom);
  } else {
    left = right = top = bottom = asPadding(padding);
  }
  return {
    x: bbox.x - left,
    y: bbox.y - top,
    width: bbox.width + left + right,
    height: bbox.height + top + bottom
  };
}
function isBBoxChild(element) {
  if (element.waypoints) {
    return false;
  }
  if (element.type === "label") {
    return false;
  }
  return true;
}
function computeChildrenBBox(shapeOrChildren, padding) {
  var elements;
  if (shapeOrChildren.length === void 0) {
    elements = filter(shapeOrChildren.children, isBBoxChild);
  } else {
    elements = shapeOrChildren;
  }
  if (elements.length) {
    return addPadding(getBBox(elements), padding);
  }
}

// node_modules/.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/features/modeling/util/LaneUtil.js
var abs = Math.abs;
function getTRBLResize(oldBounds, newBounds) {
  return substractTRBL(asTRBL(newBounds), asTRBL(oldBounds));
}
var LANE_PARENTS = [
  "bpmn:Participant",
  "bpmn:Process",
  "bpmn:SubProcess"
];
var LANE_INDENTATION = 30;
function collectLanes(shape, collectedShapes) {
  collectedShapes = collectedShapes || [];
  shape.children.filter(function(s) {
    if (is(s, "bpmn:Lane")) {
      collectLanes(s, collectedShapes);
      collectedShapes.push(s);
    }
  });
  return collectedShapes;
}
function getChildLanes(shape) {
  return shape.children.filter(function(c) {
    return is(c, "bpmn:Lane");
  });
}
function getLanesRoot(shape) {
  return getParent(shape, LANE_PARENTS) || shape;
}
function computeLanesResize(shape, newBounds) {
  var rootElement = getLanesRoot(shape);
  var initialShapes = is(rootElement, "bpmn:Process") ? [] : [rootElement];
  var allLanes = collectLanes(rootElement, initialShapes), shapeTrbl = asTRBL(shape), shapeNewTrbl = asTRBL(newBounds), trblResize = getTRBLResize(shape, newBounds), resizeNeeded = [];
  var isHorizontalLane = isHorizontal(shape);
  allLanes.forEach(function(other) {
    if (other === shape) {
      return;
    }
    var topResize = isHorizontalLane ? 0 : trblResize.top, rightResize = isHorizontalLane ? trblResize.right : 0, bottomResize = isHorizontalLane ? 0 : trblResize.bottom, leftResize = isHorizontalLane ? trblResize.left : 0;
    var otherTrbl = asTRBL(other);
    if (trblResize.top) {
      if (abs(otherTrbl.bottom - shapeTrbl.top) < 10) {
        bottomResize = shapeNewTrbl.top - otherTrbl.bottom;
      }
      if (abs(otherTrbl.top - shapeTrbl.top) < 5) {
        topResize = shapeNewTrbl.top - otherTrbl.top;
      }
    }
    if (trblResize.left) {
      if (abs(otherTrbl.right - shapeTrbl.left) < 10) {
        rightResize = shapeNewTrbl.left - otherTrbl.right;
      }
      if (abs(otherTrbl.left - shapeTrbl.left) < 5) {
        leftResize = shapeNewTrbl.left - otherTrbl.left;
      }
    }
    if (trblResize.bottom) {
      if (abs(otherTrbl.top - shapeTrbl.bottom) < 10) {
        topResize = shapeNewTrbl.bottom - otherTrbl.top;
      }
      if (abs(otherTrbl.bottom - shapeTrbl.bottom) < 5) {
        bottomResize = shapeNewTrbl.bottom - otherTrbl.bottom;
      }
    }
    if (trblResize.right) {
      if (abs(otherTrbl.left - shapeTrbl.right) < 10) {
        leftResize = shapeNewTrbl.right - otherTrbl.left;
      }
      if (abs(otherTrbl.right - shapeTrbl.right) < 5) {
        rightResize = shapeNewTrbl.right - otherTrbl.right;
      }
    }
    if (topResize || rightResize || bottomResize || leftResize) {
      resizeNeeded.push({
        shape: other,
        newBounds: resizeTRBL(other, {
          top: topResize,
          right: rightResize,
          bottom: bottomResize,
          left: leftResize
        })
      });
    }
  });
  return resizeNeeded;
}

export {
  substractTRBL,
  resizeBounds,
  ensureConstraints,
  getMinResizeBounds,
  computeChildrenBBox,
  LANE_INDENTATION,
  collectLanes,
  getChildLanes,
  getLanesRoot,
  computeLanesResize
};
//# sourceMappingURL=chunk-VUQJMARV.js.map
