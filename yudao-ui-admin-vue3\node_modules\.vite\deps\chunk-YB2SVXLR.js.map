{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/label/labelLayoutHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { BoundingRect, OrientedBoundingRect } from '../util/graphic.js';\nexport function prepareLayoutList(input) {\n  var list = [];\n  for (var i = 0; i < input.length; i++) {\n    var rawItem = input[i];\n    if (rawItem.defaultAttr.ignore) {\n      continue;\n    }\n    var label = rawItem.label;\n    var transform = label.getComputedTransform();\n    // NOTE: Get bounding rect after getComputedTransform, or label may not been updated by the host el.\n    var localRect = label.getBoundingRect();\n    var isAxisAligned = !transform || transform[1] < 1e-5 && transform[2] < 1e-5;\n    var minMargin = label.style.margin || 0;\n    var globalRect = localRect.clone();\n    globalRect.applyTransform(transform);\n    globalRect.x -= minMargin / 2;\n    globalRect.y -= minMargin / 2;\n    globalRect.width += minMargin;\n    globalRect.height += minMargin;\n    var obb = isAxisAligned ? new OrientedBoundingRect(localRect, transform) : null;\n    list.push({\n      label: label,\n      labelLine: rawItem.labelLine,\n      rect: globalRect,\n      localRect: localRect,\n      obb: obb,\n      priority: rawItem.priority,\n      defaultAttr: rawItem.defaultAttr,\n      layoutOption: rawItem.computedLayoutOption,\n      axisAligned: isAxisAligned,\n      transform: transform\n    });\n  }\n  return list;\n}\nfunction shiftLayout(list, xyDim, sizeDim, minBound, maxBound, balanceShift) {\n  var len = list.length;\n  if (len < 2) {\n    return;\n  }\n  list.sort(function (a, b) {\n    return a.rect[xyDim] - b.rect[xyDim];\n  });\n  var lastPos = 0;\n  var delta;\n  var adjusted = false;\n  var shifts = [];\n  var totalShifts = 0;\n  for (var i = 0; i < len; i++) {\n    var item = list[i];\n    var rect = item.rect;\n    delta = rect[xyDim] - lastPos;\n    if (delta < 0) {\n      // shiftForward(i, len, -delta);\n      rect[xyDim] -= delta;\n      item.label[xyDim] -= delta;\n      adjusted = true;\n    }\n    var shift = Math.max(-delta, 0);\n    shifts.push(shift);\n    totalShifts += shift;\n    lastPos = rect[xyDim] + rect[sizeDim];\n  }\n  if (totalShifts > 0 && balanceShift) {\n    // Shift back to make the distribution more equally.\n    shiftList(-totalShifts / len, 0, len);\n  }\n  // TODO bleedMargin?\n  var first = list[0];\n  var last = list[len - 1];\n  var minGap;\n  var maxGap;\n  updateMinMaxGap();\n  // If ends exceed two bounds, squeeze at most 80%, then take the gap of two bounds.\n  minGap < 0 && squeezeGaps(-minGap, 0.8);\n  maxGap < 0 && squeezeGaps(maxGap, 0.8);\n  updateMinMaxGap();\n  takeBoundsGap(minGap, maxGap, 1);\n  takeBoundsGap(maxGap, minGap, -1);\n  // Handle bailout when there is not enough space.\n  updateMinMaxGap();\n  if (minGap < 0) {\n    squeezeWhenBailout(-minGap);\n  }\n  if (maxGap < 0) {\n    squeezeWhenBailout(maxGap);\n  }\n  function updateMinMaxGap() {\n    minGap = first.rect[xyDim] - minBound;\n    maxGap = maxBound - last.rect[xyDim] - last.rect[sizeDim];\n  }\n  function takeBoundsGap(gapThisBound, gapOtherBound, moveDir) {\n    if (gapThisBound < 0) {\n      // Move from other gap if can.\n      var moveFromMaxGap = Math.min(gapOtherBound, -gapThisBound);\n      if (moveFromMaxGap > 0) {\n        shiftList(moveFromMaxGap * moveDir, 0, len);\n        var remained = moveFromMaxGap + gapThisBound;\n        if (remained < 0) {\n          squeezeGaps(-remained * moveDir, 1);\n        }\n      } else {\n        squeezeGaps(-gapThisBound * moveDir, 1);\n      }\n    }\n  }\n  function shiftList(delta, start, end) {\n    if (delta !== 0) {\n      adjusted = true;\n    }\n    for (var i = start; i < end; i++) {\n      var item = list[i];\n      var rect = item.rect;\n      rect[xyDim] += delta;\n      item.label[xyDim] += delta;\n    }\n  }\n  // Squeeze gaps if the labels exceed margin.\n  function squeezeGaps(delta, maxSqeezePercent) {\n    var gaps = [];\n    var totalGaps = 0;\n    for (var i = 1; i < len; i++) {\n      var prevItemRect = list[i - 1].rect;\n      var gap = Math.max(list[i].rect[xyDim] - prevItemRect[xyDim] - prevItemRect[sizeDim], 0);\n      gaps.push(gap);\n      totalGaps += gap;\n    }\n    if (!totalGaps) {\n      return;\n    }\n    var squeezePercent = Math.min(Math.abs(delta) / totalGaps, maxSqeezePercent);\n    if (delta > 0) {\n      for (var i = 0; i < len - 1; i++) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i] * squeezePercent;\n        // Forward\n        shiftList(movement, 0, i + 1);\n      }\n    } else {\n      // Backward\n      for (var i = len - 1; i > 0; i--) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i - 1] * squeezePercent;\n        shiftList(-movement, i, len);\n      }\n    }\n  }\n  /**\n   * Squeeze to allow overlap if there is no more space available.\n   * Let other overlapping strategy like hideOverlap do the job instead of keep exceeding the bounds.\n   */\n  function squeezeWhenBailout(delta) {\n    var dir = delta < 0 ? -1 : 1;\n    delta = Math.abs(delta);\n    var moveForEachLabel = Math.ceil(delta / (len - 1));\n    for (var i = 0; i < len - 1; i++) {\n      if (dir > 0) {\n        // Forward\n        shiftList(moveForEachLabel, 0, i + 1);\n      } else {\n        // Backward\n        shiftList(-moveForEachLabel, len - i - 1, len);\n      }\n      delta -= moveForEachLabel;\n      if (delta <= 0) {\n        return;\n      }\n    }\n  }\n  return adjusted;\n}\n/**\n * Adjust labels on x direction to avoid overlap.\n */\nexport function shiftLayoutOnX(list, leftBound, rightBound,\n// If average the shifts on all labels and add them to 0\n// TODO: Not sure if should enable it.\n// Pros: The angle of lines will distribute more equally\n// Cons: In some layout. It may not what user wanted. like in pie. the label of last sector is usually changed unexpectedly.\nbalanceShift) {\n  return shiftLayout(list, 'x', 'width', leftBound, rightBound, balanceShift);\n}\n/**\n * Adjust labels on y direction to avoid overlap.\n */\nexport function shiftLayoutOnY(list, topBound, bottomBound,\n// If average the shifts on all labels and add them to 0\nbalanceShift) {\n  return shiftLayout(list, 'y', 'height', topBound, bottomBound, balanceShift);\n}\nexport function hideOverlap(labelList) {\n  var displayedLabels = [];\n  // TODO, render overflow visible first, put in the displayedLabels.\n  labelList.sort(function (a, b) {\n    return b.priority - a.priority;\n  });\n  var globalRect = new BoundingRect(0, 0, 0, 0);\n  function hideEl(el) {\n    if (!el.ignore) {\n      // Show on emphasis.\n      var emphasisState = el.ensureState('emphasis');\n      if (emphasisState.ignore == null) {\n        emphasisState.ignore = false;\n      }\n    }\n    el.ignore = true;\n  }\n  for (var i = 0; i < labelList.length; i++) {\n    var labelItem = labelList[i];\n    var isAxisAligned = labelItem.axisAligned;\n    var localRect = labelItem.localRect;\n    var transform = labelItem.transform;\n    var label = labelItem.label;\n    var labelLine = labelItem.labelLine;\n    globalRect.copy(labelItem.rect);\n    // Add a threshold because layout may be aligned precisely.\n    globalRect.width -= 0.1;\n    globalRect.height -= 0.1;\n    globalRect.x += 0.05;\n    globalRect.y += 0.05;\n    var obb = labelItem.obb;\n    var overlapped = false;\n    for (var j = 0; j < displayedLabels.length; j++) {\n      var existsTextCfg = displayedLabels[j];\n      // Fast rejection.\n      if (!globalRect.intersect(existsTextCfg.rect)) {\n        continue;\n      }\n      if (isAxisAligned && existsTextCfg.axisAligned) {\n        // Is overlapped\n        overlapped = true;\n        break;\n      }\n      if (!existsTextCfg.obb) {\n        // If self is not axis aligned. But other is.\n        existsTextCfg.obb = new OrientedBoundingRect(existsTextCfg.localRect, existsTextCfg.transform);\n      }\n      if (!obb) {\n        // If self is axis aligned. But other is not.\n        obb = new OrientedBoundingRect(localRect, transform);\n      }\n      if (obb.intersect(existsTextCfg.obb)) {\n        overlapped = true;\n        break;\n      }\n    }\n    // TODO Callback to determine if this overlap should be handled?\n    if (overlapped) {\n      hideEl(label);\n      labelLine && hideEl(labelLine);\n    } else {\n      label.attr('ignore', labelItem.defaultAttr.ignore);\n      labelLine && labelLine.attr('ignore', labelItem.defaultAttr.labelGuideIgnore);\n      displayedLabels.push(labelItem);\n    }\n  }\n}"], "mappings": ";;;;;;;;AA4CO,SAAS,kBAAkB,OAAO;AACvC,MAAI,OAAO,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,UAAU,MAAM,CAAC;AACrB,QAAI,QAAQ,YAAY,QAAQ;AAC9B;AAAA,IACF;AACA,QAAI,QAAQ,QAAQ;AACpB,QAAI,YAAY,MAAM,qBAAqB;AAE3C,QAAI,YAAY,MAAM,gBAAgB;AACtC,QAAI,gBAAgB,CAAC,aAAa,UAAU,CAAC,IAAI,QAAQ,UAAU,CAAC,IAAI;AACxE,QAAI,YAAY,MAAM,MAAM,UAAU;AACtC,QAAI,aAAa,UAAU,MAAM;AACjC,eAAW,eAAe,SAAS;AACnC,eAAW,KAAK,YAAY;AAC5B,eAAW,KAAK,YAAY;AAC5B,eAAW,SAAS;AACpB,eAAW,UAAU;AACrB,QAAI,MAAM,gBAAgB,IAAI,6BAAqB,WAAW,SAAS,IAAI;AAC3E,SAAK,KAAK;AAAA,MACR;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,UAAU,QAAQ;AAAA,MAClB,aAAa,QAAQ;AAAA,MACrB,cAAc,QAAQ;AAAA,MACtB,aAAa;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,YAAY,MAAM,OAAO,SAAS,UAAU,UAAU,cAAc;AAC3E,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,GAAG;AACX;AAAA,EACF;AACA,OAAK,KAAK,SAAU,GAAG,GAAG;AACxB,WAAO,EAAE,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK;AAAA,EACrC,CAAC;AACD,MAAI,UAAU;AACd,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,SAAS,CAAC;AACd,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,OAAO,KAAK,CAAC;AACjB,QAAI,OAAO,KAAK;AAChB,YAAQ,KAAK,KAAK,IAAI;AACtB,QAAI,QAAQ,GAAG;AAEb,WAAK,KAAK,KAAK;AACf,WAAK,MAAM,KAAK,KAAK;AACrB,iBAAW;AAAA,IACb;AACA,QAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC;AAC9B,WAAO,KAAK,KAAK;AACjB,mBAAe;AACf,cAAU,KAAK,KAAK,IAAI,KAAK,OAAO;AAAA,EACtC;AACA,MAAI,cAAc,KAAK,cAAc;AAEnC,cAAU,CAAC,cAAc,KAAK,GAAG,GAAG;AAAA,EACtC;AAEA,MAAI,QAAQ,KAAK,CAAC;AAClB,MAAI,OAAO,KAAK,MAAM,CAAC;AACvB,MAAI;AACJ,MAAI;AACJ,kBAAgB;AAEhB,WAAS,KAAK,YAAY,CAAC,QAAQ,GAAG;AACtC,WAAS,KAAK,YAAY,QAAQ,GAAG;AACrC,kBAAgB;AAChB,gBAAc,QAAQ,QAAQ,CAAC;AAC/B,gBAAc,QAAQ,QAAQ,EAAE;AAEhC,kBAAgB;AAChB,MAAI,SAAS,GAAG;AACd,uBAAmB,CAAC,MAAM;AAAA,EAC5B;AACA,MAAI,SAAS,GAAG;AACd,uBAAmB,MAAM;AAAA,EAC3B;AACA,WAAS,kBAAkB;AACzB,aAAS,MAAM,KAAK,KAAK,IAAI;AAC7B,aAAS,WAAW,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,OAAO;AAAA,EAC1D;AACA,WAAS,cAAc,cAAc,eAAe,SAAS;AAC3D,QAAI,eAAe,GAAG;AAEpB,UAAI,iBAAiB,KAAK,IAAI,eAAe,CAAC,YAAY;AAC1D,UAAI,iBAAiB,GAAG;AACtB,kBAAU,iBAAiB,SAAS,GAAG,GAAG;AAC1C,YAAI,WAAW,iBAAiB;AAChC,YAAI,WAAW,GAAG;AAChB,sBAAY,CAAC,WAAW,SAAS,CAAC;AAAA,QACpC;AAAA,MACF,OAAO;AACL,oBAAY,CAAC,eAAe,SAAS,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,WAAS,UAAUA,QAAO,OAAO,KAAK;AACpC,QAAIA,WAAU,GAAG;AACf,iBAAW;AAAA,IACb;AACA,aAASC,KAAI,OAAOA,KAAI,KAAKA,MAAK;AAChC,UAAIC,QAAO,KAAKD,EAAC;AACjB,UAAIE,QAAOD,MAAK;AAChB,MAAAC,MAAK,KAAK,KAAKH;AACf,MAAAE,MAAK,MAAM,KAAK,KAAKF;AAAA,IACvB;AAAA,EACF;AAEA,WAAS,YAAYA,QAAO,kBAAkB;AAC5C,QAAI,OAAO,CAAC;AACZ,QAAI,YAAY;AAChB,aAASC,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC5B,UAAI,eAAe,KAAKA,KAAI,CAAC,EAAE;AAC/B,UAAI,MAAM,KAAK,IAAI,KAAKA,EAAC,EAAE,KAAK,KAAK,IAAI,aAAa,KAAK,IAAI,aAAa,OAAO,GAAG,CAAC;AACvF,WAAK,KAAK,GAAG;AACb,mBAAa;AAAA,IACf;AACA,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,QAAI,iBAAiB,KAAK,IAAI,KAAK,IAAID,MAAK,IAAI,WAAW,gBAAgB;AAC3E,QAAIA,SAAQ,GAAG;AACb,eAASC,KAAI,GAAGA,KAAI,MAAM,GAAGA,MAAK;AAEhC,YAAI,WAAW,KAAKA,EAAC,IAAI;AAEzB,kBAAU,UAAU,GAAGA,KAAI,CAAC;AAAA,MAC9B;AAAA,IACF,OAAO;AAEL,eAASA,KAAI,MAAM,GAAGA,KAAI,GAAGA,MAAK;AAEhC,YAAI,WAAW,KAAKA,KAAI,CAAC,IAAI;AAC7B,kBAAU,CAAC,UAAUA,IAAG,GAAG;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAKA,WAAS,mBAAmBD,QAAO;AACjC,QAAI,MAAMA,SAAQ,IAAI,KAAK;AAC3B,IAAAA,SAAQ,KAAK,IAAIA,MAAK;AACtB,QAAI,mBAAmB,KAAK,KAAKA,UAAS,MAAM,EAAE;AAClD,aAASC,KAAI,GAAGA,KAAI,MAAM,GAAGA,MAAK;AAChC,UAAI,MAAM,GAAG;AAEX,kBAAU,kBAAkB,GAAGA,KAAI,CAAC;AAAA,MACtC,OAAO;AAEL,kBAAU,CAAC,kBAAkB,MAAMA,KAAI,GAAG,GAAG;AAAA,MAC/C;AACA,MAAAD,UAAS;AACT,UAAIA,UAAS,GAAG;AACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAIO,SAAS,eAAe,MAAM,WAAW,YAKhD,cAAc;AACZ,SAAO,YAAY,MAAM,KAAK,SAAS,WAAW,YAAY,YAAY;AAC5E;AAIO,SAAS,eAAe,MAAM,UAAU,aAE/C,cAAc;AACZ,SAAO,YAAY,MAAM,KAAK,UAAU,UAAU,aAAa,YAAY;AAC7E;AACO,SAAS,YAAY,WAAW;AACrC,MAAI,kBAAkB,CAAC;AAEvB,YAAU,KAAK,SAAU,GAAG,GAAG;AAC7B,WAAO,EAAE,WAAW,EAAE;AAAA,EACxB,CAAC;AACD,MAAI,aAAa,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC5C,WAAS,OAAO,IAAI;AAClB,QAAI,CAAC,GAAG,QAAQ;AAEd,UAAI,gBAAgB,GAAG,YAAY,UAAU;AAC7C,UAAI,cAAc,UAAU,MAAM;AAChC,sBAAc,SAAS;AAAA,MACzB;AAAA,IACF;AACA,OAAG,SAAS;AAAA,EACd;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,YAAY,UAAU,CAAC;AAC3B,QAAI,gBAAgB,UAAU;AAC9B,QAAI,YAAY,UAAU;AAC1B,QAAI,YAAY,UAAU;AAC1B,QAAI,QAAQ,UAAU;AACtB,QAAI,YAAY,UAAU;AAC1B,eAAW,KAAK,UAAU,IAAI;AAE9B,eAAW,SAAS;AACpB,eAAW,UAAU;AACrB,eAAW,KAAK;AAChB,eAAW,KAAK;AAChB,QAAI,MAAM,UAAU;AACpB,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAI,gBAAgB,gBAAgB,CAAC;AAErC,UAAI,CAAC,WAAW,UAAU,cAAc,IAAI,GAAG;AAC7C;AAAA,MACF;AACA,UAAI,iBAAiB,cAAc,aAAa;AAE9C,qBAAa;AACb;AAAA,MACF;AACA,UAAI,CAAC,cAAc,KAAK;AAEtB,sBAAc,MAAM,IAAI,6BAAqB,cAAc,WAAW,cAAc,SAAS;AAAA,MAC/F;AACA,UAAI,CAAC,KAAK;AAER,cAAM,IAAI,6BAAqB,WAAW,SAAS;AAAA,MACrD;AACA,UAAI,IAAI,UAAU,cAAc,GAAG,GAAG;AACpC,qBAAa;AACb;AAAA,MACF;AAAA,IACF;AAEA,QAAI,YAAY;AACd,aAAO,KAAK;AACZ,mBAAa,OAAO,SAAS;AAAA,IAC/B,OAAO;AACL,YAAM,KAAK,UAAU,UAAU,YAAY,MAAM;AACjD,mBAAa,UAAU,KAAK,UAAU,UAAU,YAAY,gBAAgB;AAC5E,sBAAgB,KAAK,SAAS;AAAA,IAChC;AAAA,EACF;AACF;", "names": ["delta", "i", "item", "rect"]}