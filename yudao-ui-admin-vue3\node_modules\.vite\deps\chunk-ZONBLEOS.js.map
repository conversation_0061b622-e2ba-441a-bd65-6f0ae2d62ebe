{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/LinePath.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/Line.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/LineDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/**\n * Line path for bezier and straight line draw\n */\nimport * as graphic from '../../util/graphic.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar straightLineProto = graphic.Line.prototype;\nvar bezierCurveProto = graphic.BezierCurve.prototype;\nvar StraightLineShape = /** @class */function () {\n  function StraightLineShape() {\n    // Start point\n    this.x1 = 0;\n    this.y1 = 0;\n    // End point\n    this.x2 = 0;\n    this.y2 = 0;\n    this.percent = 1;\n  }\n  return StraightLineShape;\n}();\nvar CurveShape = /** @class */function (_super) {\n  __extends(CurveShape, _super);\n  function CurveShape() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return CurveShape;\n}(StraightLineShape);\nfunction isStraightLine(shape) {\n  return isNaN(+shape.cpx1) || isNaN(+shape.cpy1);\n}\nvar ECLinePath = /** @class */function (_super) {\n  __extends(ECLinePath, _super);\n  function ECLinePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-line';\n    return _this;\n  }\n  ECLinePath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  ECLinePath.prototype.getDefaultShape = function () {\n    return new StraightLineShape();\n  };\n  ECLinePath.prototype.buildPath = function (ctx, shape) {\n    if (isStraightLine(shape)) {\n      straightLineProto.buildPath.call(this, ctx, shape);\n    } else {\n      bezierCurveProto.buildPath.call(this, ctx, shape);\n    }\n  };\n  ECLinePath.prototype.pointAt = function (t) {\n    if (isStraightLine(this.shape)) {\n      return straightLineProto.pointAt.call(this, t);\n    } else {\n      return bezierCurveProto.pointAt.call(this, t);\n    }\n  };\n  ECLinePath.prototype.tangentAt = function (t) {\n    var shape = this.shape;\n    var p = isStraightLine(shape) ? [shape.x2 - shape.x1, shape.y2 - shape.y1] : bezierCurveProto.tangentAt.call(this, t);\n    return vec2.normalize(p, p);\n  };\n  return ECLinePath;\n}(graphic.Path);\nexport default ECLinePath;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { isArray, each } from 'zrender/lib/core/util.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport * as symbolUtil from '../../util/symbol.js';\nimport ECLinePath from './LinePath.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enterEmphasis, leaveEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { getLabelStatesModels, setLabelStyle } from '../../label/labelStyle.js';\nimport { round } from '../../util/number.js';\nvar SYMBOL_CATEGORIES = ['fromSymbol', 'toSymbol'];\nfunction makeSymbolTypeKey(symbolCategory) {\n  return '_' + symbolCategory + 'Type';\n}\nfunction makeSymbolTypeValue(name, lineData, idx) {\n  var symbolType = lineData.getItemVisual(idx, name);\n  if (!symbolType || symbolType === 'none') {\n    return symbolType;\n  }\n  var symbolSize = lineData.getItemVisual(idx, name + 'Size');\n  var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');\n  var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');\n  var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');\n  var symbolSizeArr = symbolUtil.normalizeSymbolSize(symbolSize);\n  var symbolOffsetArr = symbolUtil.normalizeSymbolOffset(symbolOffset || 0, symbolSizeArr);\n  return symbolType + symbolSizeArr + symbolOffsetArr + (symbolRotate || '') + (symbolKeepAspect || '');\n}\n/**\n * @inner\n */\nfunction createSymbol(name, lineData, idx) {\n  var symbolType = lineData.getItemVisual(idx, name);\n  if (!symbolType || symbolType === 'none') {\n    return;\n  }\n  var symbolSize = lineData.getItemVisual(idx, name + 'Size');\n  var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');\n  var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');\n  var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');\n  var symbolSizeArr = symbolUtil.normalizeSymbolSize(symbolSize);\n  var symbolOffsetArr = symbolUtil.normalizeSymbolOffset(symbolOffset || 0, symbolSizeArr);\n  var symbolPath = symbolUtil.createSymbol(symbolType, -symbolSizeArr[0] / 2 + symbolOffsetArr[0], -symbolSizeArr[1] / 2 + symbolOffsetArr[1], symbolSizeArr[0], symbolSizeArr[1], null, symbolKeepAspect);\n  symbolPath.__specifiedRotation = symbolRotate == null || isNaN(symbolRotate) ? void 0 : +symbolRotate * Math.PI / 180 || 0;\n  symbolPath.name = name;\n  return symbolPath;\n}\nfunction createLine(points) {\n  var line = new ECLinePath({\n    name: 'line',\n    subPixelOptimize: true\n  });\n  setLinePoints(line.shape, points);\n  return line;\n}\nfunction setLinePoints(targetShape, points) {\n  targetShape.x1 = points[0][0];\n  targetShape.y1 = points[0][1];\n  targetShape.x2 = points[1][0];\n  targetShape.y2 = points[1][1];\n  targetShape.percent = 1;\n  var cp1 = points[2];\n  if (cp1) {\n    targetShape.cpx1 = cp1[0];\n    targetShape.cpy1 = cp1[1];\n  } else {\n    targetShape.cpx1 = NaN;\n    targetShape.cpy1 = NaN;\n  }\n}\nvar Line = /** @class */function (_super) {\n  __extends(Line, _super);\n  function Line(lineData, idx, seriesScope) {\n    var _this = _super.call(this) || this;\n    _this._createLine(lineData, idx, seriesScope);\n    return _this;\n  }\n  Line.prototype._createLine = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var linePoints = lineData.getItemLayout(idx);\n    var line = createLine(linePoints);\n    line.shape.percent = 0;\n    graphic.initProps(line, {\n      shape: {\n        percent: 1\n      }\n    }, seriesModel, idx);\n    this.add(line);\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbol = createSymbol(symbolCategory, lineData, idx);\n      // symbols must added after line to make sure\n      // it will be updated after line#update.\n      // Or symbol position and rotation update in line#beforeUpdate will be one frame slow\n      this.add(symbol);\n      this[makeSymbolTypeKey(symbolCategory)] = makeSymbolTypeValue(symbolCategory, lineData, idx);\n    }, this);\n    this._updateCommonStl(lineData, idx, seriesScope);\n  };\n  // TODO More strict on the List type in parameters?\n  Line.prototype.updateData = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var line = this.childOfName('line');\n    var linePoints = lineData.getItemLayout(idx);\n    var target = {\n      shape: {}\n    };\n    setLinePoints(target.shape, linePoints);\n    graphic.updateProps(line, target, seriesModel, idx);\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbolType = makeSymbolTypeValue(symbolCategory, lineData, idx);\n      var key = makeSymbolTypeKey(symbolCategory);\n      // Symbol changed\n      if (this[key] !== symbolType) {\n        this.remove(this.childOfName(symbolCategory));\n        var symbol = createSymbol(symbolCategory, lineData, idx);\n        this.add(symbol);\n      }\n      this[key] = symbolType;\n    }, this);\n    this._updateCommonStl(lineData, idx, seriesScope);\n  };\n  ;\n  Line.prototype.getLinePath = function () {\n    return this.childAt(0);\n  };\n  Line.prototype._updateCommonStl = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var line = this.childOfName('line');\n    var emphasisLineStyle = seriesScope && seriesScope.emphasisLineStyle;\n    var blurLineStyle = seriesScope && seriesScope.blurLineStyle;\n    var selectLineStyle = seriesScope && seriesScope.selectLineStyle;\n    var labelStatesModels = seriesScope && seriesScope.labelStatesModels;\n    var emphasisDisabled = seriesScope && seriesScope.emphasisDisabled;\n    var focus = seriesScope && seriesScope.focus;\n    var blurScope = seriesScope && seriesScope.blurScope;\n    // Optimization for large dataset\n    if (!seriesScope || lineData.hasItemOption) {\n      var itemModel = lineData.getItemModel(idx);\n      var emphasisModel = itemModel.getModel('emphasis');\n      emphasisLineStyle = emphasisModel.getModel('lineStyle').getLineStyle();\n      blurLineStyle = itemModel.getModel(['blur', 'lineStyle']).getLineStyle();\n      selectLineStyle = itemModel.getModel(['select', 'lineStyle']).getLineStyle();\n      emphasisDisabled = emphasisModel.get('disabled');\n      focus = emphasisModel.get('focus');\n      blurScope = emphasisModel.get('blurScope');\n      labelStatesModels = getLabelStatesModels(itemModel);\n    }\n    var lineStyle = lineData.getItemVisual(idx, 'style');\n    var visualColor = lineStyle.stroke;\n    line.useStyle(lineStyle);\n    line.style.fill = null;\n    line.style.strokeNoScale = true;\n    line.ensureState('emphasis').style = emphasisLineStyle;\n    line.ensureState('blur').style = blurLineStyle;\n    line.ensureState('select').style = selectLineStyle;\n    // Update symbol\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbol = this.childOfName(symbolCategory);\n      if (symbol) {\n        // Share opacity and color with line.\n        symbol.setColor(visualColor);\n        symbol.style.opacity = lineStyle.opacity;\n        for (var i = 0; i < SPECIAL_STATES.length; i++) {\n          var stateName = SPECIAL_STATES[i];\n          var lineState = line.getState(stateName);\n          if (lineState) {\n            var lineStateStyle = lineState.style || {};\n            var state = symbol.ensureState(stateName);\n            var stateStyle = state.style || (state.style = {});\n            if (lineStateStyle.stroke != null) {\n              stateStyle[symbol.__isEmptyBrush ? 'stroke' : 'fill'] = lineStateStyle.stroke;\n            }\n            if (lineStateStyle.opacity != null) {\n              stateStyle.opacity = lineStateStyle.opacity;\n            }\n          }\n        }\n        symbol.markRedraw();\n      }\n    }, this);\n    var rawVal = seriesModel.getRawValue(idx);\n    setLabelStyle(this, labelStatesModels, {\n      labelDataIndex: idx,\n      labelFetcher: {\n        getFormattedLabel: function (dataIndex, stateName) {\n          return seriesModel.getFormattedLabel(dataIndex, stateName, lineData.dataType);\n        }\n      },\n      inheritColor: visualColor || '#000',\n      defaultOpacity: lineStyle.opacity,\n      defaultText: (rawVal == null ? lineData.getName(idx) : isFinite(rawVal) ? round(rawVal) : rawVal) + ''\n    });\n    var label = this.getTextContent();\n    // Always set `textStyle` even if `normalStyle.text` is null, because default\n    // values have to be set on `normalStyle`.\n    if (label) {\n      var labelNormalModel = labelStatesModels.normal;\n      label.__align = label.style.align;\n      label.__verticalAlign = label.style.verticalAlign;\n      // 'start', 'middle', 'end'\n      label.__position = labelNormalModel.get('position') || 'middle';\n      var distance = labelNormalModel.get('distance');\n      if (!isArray(distance)) {\n        distance = [distance, distance];\n      }\n      label.__labelDistance = distance;\n    }\n    this.setTextConfig({\n      position: null,\n      local: true,\n      inside: false // Can't be inside for stroke element.\n    });\n\n    toggleHoverEmphasis(this, focus, blurScope, emphasisDisabled);\n  };\n  Line.prototype.highlight = function () {\n    enterEmphasis(this);\n  };\n  Line.prototype.downplay = function () {\n    leaveEmphasis(this);\n  };\n  Line.prototype.updateLayout = function (lineData, idx) {\n    this.setLinePoints(lineData.getItemLayout(idx));\n  };\n  Line.prototype.setLinePoints = function (points) {\n    var linePath = this.childOfName('line');\n    setLinePoints(linePath.shape, points);\n    linePath.dirty();\n  };\n  Line.prototype.beforeUpdate = function () {\n    var lineGroup = this;\n    var symbolFrom = lineGroup.childOfName('fromSymbol');\n    var symbolTo = lineGroup.childOfName('toSymbol');\n    var label = lineGroup.getTextContent();\n    // Quick reject\n    if (!symbolFrom && !symbolTo && (!label || label.ignore)) {\n      return;\n    }\n    var invScale = 1;\n    var parentNode = this.parent;\n    while (parentNode) {\n      if (parentNode.scaleX) {\n        invScale /= parentNode.scaleX;\n      }\n      parentNode = parentNode.parent;\n    }\n    var line = lineGroup.childOfName('line');\n    // If line not changed\n    // FIXME Parent scale changed\n    if (!this.__dirty && !line.__dirty) {\n      return;\n    }\n    var percent = line.shape.percent;\n    var fromPos = line.pointAt(0);\n    var toPos = line.pointAt(percent);\n    var d = vector.sub([], toPos, fromPos);\n    vector.normalize(d, d);\n    function setSymbolRotation(symbol, percent) {\n      // Fix #12388\n      // when symbol is set to be 'arrow' in markLine,\n      // symbolRotate value will be ignored, and compulsively use tangent angle.\n      // rotate by default if symbol rotation is not specified\n      var specifiedRotation = symbol.__specifiedRotation;\n      if (specifiedRotation == null) {\n        var tangent = line.tangentAt(percent);\n        symbol.attr('rotation', (percent === 1 ? -1 : 1) * Math.PI / 2 - Math.atan2(tangent[1], tangent[0]));\n      } else {\n        symbol.attr('rotation', specifiedRotation);\n      }\n    }\n    if (symbolFrom) {\n      symbolFrom.setPosition(fromPos);\n      setSymbolRotation(symbolFrom, 0);\n      symbolFrom.scaleX = symbolFrom.scaleY = invScale * percent;\n      symbolFrom.markRedraw();\n    }\n    if (symbolTo) {\n      symbolTo.setPosition(toPos);\n      setSymbolRotation(symbolTo, 1);\n      symbolTo.scaleX = symbolTo.scaleY = invScale * percent;\n      symbolTo.markRedraw();\n    }\n    if (label && !label.ignore) {\n      label.x = label.y = 0;\n      label.originX = label.originY = 0;\n      var textAlign = void 0;\n      var textVerticalAlign = void 0;\n      var distance = label.__labelDistance;\n      var distanceX = distance[0] * invScale;\n      var distanceY = distance[1] * invScale;\n      var halfPercent = percent / 2;\n      var tangent = line.tangentAt(halfPercent);\n      var n = [tangent[1], -tangent[0]];\n      var cp = line.pointAt(halfPercent);\n      if (n[1] > 0) {\n        n[0] = -n[0];\n        n[1] = -n[1];\n      }\n      var dir = tangent[0] < 0 ? -1 : 1;\n      if (label.__position !== 'start' && label.__position !== 'end') {\n        var rotation = -Math.atan2(tangent[1], tangent[0]);\n        if (toPos[0] < fromPos[0]) {\n          rotation = Math.PI + rotation;\n        }\n        label.rotation = rotation;\n      }\n      var dy = void 0;\n      switch (label.__position) {\n        case 'insideStartTop':\n        case 'insideMiddleTop':\n        case 'insideEndTop':\n        case 'middle':\n          dy = -distanceY;\n          textVerticalAlign = 'bottom';\n          break;\n        case 'insideStartBottom':\n        case 'insideMiddleBottom':\n        case 'insideEndBottom':\n          dy = distanceY;\n          textVerticalAlign = 'top';\n          break;\n        default:\n          dy = 0;\n          textVerticalAlign = 'middle';\n      }\n      switch (label.__position) {\n        case 'end':\n          label.x = d[0] * distanceX + toPos[0];\n          label.y = d[1] * distanceY + toPos[1];\n          textAlign = d[0] > 0.8 ? 'left' : d[0] < -0.8 ? 'right' : 'center';\n          textVerticalAlign = d[1] > 0.8 ? 'top' : d[1] < -0.8 ? 'bottom' : 'middle';\n          break;\n        case 'start':\n          label.x = -d[0] * distanceX + fromPos[0];\n          label.y = -d[1] * distanceY + fromPos[1];\n          textAlign = d[0] > 0.8 ? 'right' : d[0] < -0.8 ? 'left' : 'center';\n          textVerticalAlign = d[1] > 0.8 ? 'bottom' : d[1] < -0.8 ? 'top' : 'middle';\n          break;\n        case 'insideStartTop':\n        case 'insideStart':\n        case 'insideStartBottom':\n          label.x = distanceX * dir + fromPos[0];\n          label.y = fromPos[1] + dy;\n          textAlign = tangent[0] < 0 ? 'right' : 'left';\n          label.originX = -distanceX * dir;\n          label.originY = -dy;\n          break;\n        case 'insideMiddleTop':\n        case 'insideMiddle':\n        case 'insideMiddleBottom':\n        case 'middle':\n          label.x = cp[0];\n          label.y = cp[1] + dy;\n          textAlign = 'center';\n          label.originY = -dy;\n          break;\n        case 'insideEndTop':\n        case 'insideEnd':\n        case 'insideEndBottom':\n          label.x = -distanceX * dir + toPos[0];\n          label.y = toPos[1] + dy;\n          textAlign = tangent[0] >= 0 ? 'right' : 'left';\n          label.originX = distanceX * dir;\n          label.originY = -dy;\n          break;\n      }\n      label.scaleX = label.scaleY = invScale;\n      label.setStyle({\n        // Use the user specified text align and baseline first\n        verticalAlign: label.__verticalAlign || textVerticalAlign,\n        align: label.__align || textAlign\n      });\n    }\n  };\n  return Line;\n}(graphic.Group);\nexport default Line;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as graphic from '../../util/graphic.js';\nimport LineGroup from './Line.js';\nimport { getLabelStatesModels } from '../../label/labelStyle.js';\nvar LineDraw = /** @class */function () {\n  function LineDraw(LineCtor) {\n    this.group = new graphic.Group();\n    this._LineCtor = LineCtor || LineGroup;\n  }\n  LineDraw.prototype.updateData = function (lineData) {\n    var _this = this;\n    // Remove progressive els.\n    this._progressiveEls = null;\n    var lineDraw = this;\n    var group = lineDraw.group;\n    var oldLineData = lineDraw._lineData;\n    lineDraw._lineData = lineData;\n    // There is no oldLineData only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!oldLineData) {\n      group.removeAll();\n    }\n    var seriesScope = makeSeriesScope(lineData);\n    lineData.diff(oldLineData).add(function (idx) {\n      _this._doAdd(lineData, idx, seriesScope);\n    }).update(function (newIdx, oldIdx) {\n      _this._doUpdate(oldLineData, lineData, oldIdx, newIdx, seriesScope);\n    }).remove(function (idx) {\n      group.remove(oldLineData.getItemGraphicEl(idx));\n    }).execute();\n  };\n  ;\n  LineDraw.prototype.updateLayout = function () {\n    var lineData = this._lineData;\n    // Do not support update layout in incremental mode.\n    if (!lineData) {\n      return;\n    }\n    lineData.eachItemGraphicEl(function (el, idx) {\n      el.updateLayout(lineData, idx);\n    }, this);\n  };\n  ;\n  LineDraw.prototype.incrementalPrepareUpdate = function (lineData) {\n    this._seriesScope = makeSeriesScope(lineData);\n    this._lineData = null;\n    this.group.removeAll();\n  };\n  ;\n  LineDraw.prototype.incrementalUpdate = function (taskParams, lineData) {\n    this._progressiveEls = [];\n    function updateIncrementalAndHover(el) {\n      if (!el.isGroup && !isEffectObject(el)) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = taskParams.start; idx < taskParams.end; idx++) {\n      var itemLayout = lineData.getItemLayout(idx);\n      if (lineNeedsDraw(itemLayout)) {\n        var el = new this._LineCtor(lineData, idx, this._seriesScope);\n        el.traverse(updateIncrementalAndHover);\n        this.group.add(el);\n        lineData.setItemGraphicEl(idx, el);\n        this._progressiveEls.push(el);\n      }\n    }\n  };\n  ;\n  LineDraw.prototype.remove = function () {\n    this.group.removeAll();\n  };\n  ;\n  LineDraw.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  LineDraw.prototype._doAdd = function (lineData, idx, seriesScope) {\n    var itemLayout = lineData.getItemLayout(idx);\n    if (!lineNeedsDraw(itemLayout)) {\n      return;\n    }\n    var el = new this._LineCtor(lineData, idx, seriesScope);\n    lineData.setItemGraphicEl(idx, el);\n    this.group.add(el);\n  };\n  LineDraw.prototype._doUpdate = function (oldLineData, newLineData, oldIdx, newIdx, seriesScope) {\n    var itemEl = oldLineData.getItemGraphicEl(oldIdx);\n    if (!lineNeedsDraw(newLineData.getItemLayout(newIdx))) {\n      this.group.remove(itemEl);\n      return;\n    }\n    if (!itemEl) {\n      itemEl = new this._LineCtor(newLineData, newIdx, seriesScope);\n    } else {\n      itemEl.updateData(newLineData, newIdx, seriesScope);\n    }\n    newLineData.setItemGraphicEl(newIdx, itemEl);\n    this.group.add(itemEl);\n  };\n  return LineDraw;\n}();\nfunction isEffectObject(el) {\n  return el.animators && el.animators.length > 0;\n}\nfunction makeSeriesScope(lineData) {\n  var hostModel = lineData.hostModel;\n  var emphasisModel = hostModel.getModel('emphasis');\n  return {\n    lineStyle: hostModel.getModel('lineStyle').getLineStyle(),\n    emphasisLineStyle: emphasisModel.getModel(['lineStyle']).getLineStyle(),\n    blurLineStyle: hostModel.getModel(['blur', 'lineStyle']).getLineStyle(),\n    selectLineStyle: hostModel.getModel(['select', 'lineStyle']).getLineStyle(),\n    emphasisDisabled: emphasisModel.get('disabled'),\n    blurScope: emphasisModel.get('blurScope'),\n    focus: emphasisModel.get('focus'),\n    labelStatesModels: getLabelStatesModels(hostModel)\n  };\n}\nfunction isPointNaN(pt) {\n  return isNaN(pt[0]) || isNaN(pt[1]);\n}\nfunction lineNeedsDraw(pts) {\n  return pts && !isPointNaN(pts[0]) && !isPointNaN(pts[1]);\n}\nexport default LineDraw;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,IAAI,oBAA4B,aAAK;AACrC,IAAI,mBAA2B,oBAAY;AAC3C,IAAI;AAAA;AAAA,EAAiC,2BAAY;AAC/C,aAASA,qBAAoB;AAE3B,WAAK,KAAK;AACV,WAAK,KAAK;AAEV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,UAAU;AAAA,IACjB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC9C,cAAUC,aAAY,MAAM;AAC5B,aAASA,cAAa;AACpB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,WAAOA;AAAA,EACT,EAAE,iBAAiB;AAAA;AACnB,SAAS,eAAe,OAAO;AAC7B,SAAO,MAAM,CAAC,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,IAAI;AAChD;AACA,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC9C,cAAUC,aAAY,MAAM;AAC5B,aAASA,YAAW,MAAM;AACxB,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,kBAAkB,WAAY;AACjD,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,kBAAkB,WAAY;AACjD,aAAO,IAAI,kBAAkB;AAAA,IAC/B;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,KAAK,OAAO;AACrD,UAAI,eAAe,KAAK,GAAG;AACzB,0BAAkB,UAAU,KAAK,MAAM,KAAK,KAAK;AAAA,MACnD,OAAO;AACL,yBAAiB,UAAU,KAAK,MAAM,KAAK,KAAK;AAAA,MAClD;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,UAAU,SAAU,GAAG;AAC1C,UAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,eAAO,kBAAkB,QAAQ,KAAK,MAAM,CAAC;AAAA,MAC/C,OAAO;AACL,eAAO,iBAAiB,QAAQ,KAAK,MAAM,CAAC;AAAA,MAC9C;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,GAAG;AAC5C,UAAI,QAAQ,KAAK;AACjB,UAAI,IAAI,eAAe,KAAK,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,EAAE,IAAI,iBAAiB,UAAU,KAAK,MAAM,CAAC;AACpH,aAAY,UAAU,GAAG,CAAC;AAAA,IAC5B;AACA,WAAOA;AAAA,EACT,EAAU,YAAI;AAAA;AACd,IAAO,mBAAQ;;;AC1Df,IAAI,oBAAoB,CAAC,cAAc,UAAU;AACjD,SAAS,kBAAkB,gBAAgB;AACzC,SAAO,MAAM,iBAAiB;AAChC;AACA,SAAS,oBAAoB,MAAM,UAAU,KAAK;AAChD,MAAI,aAAa,SAAS,cAAc,KAAK,IAAI;AACjD,MAAI,CAAC,cAAc,eAAe,QAAQ;AACxC,WAAO;AAAA,EACT;AACA,MAAI,aAAa,SAAS,cAAc,KAAK,OAAO,MAAM;AAC1D,MAAI,eAAe,SAAS,cAAc,KAAK,OAAO,QAAQ;AAC9D,MAAI,eAAe,SAAS,cAAc,KAAK,OAAO,QAAQ;AAC9D,MAAI,mBAAmB,SAAS,cAAc,KAAK,OAAO,YAAY;AACtE,MAAI,gBAA2B,oBAAoB,UAAU;AAC7D,MAAI,kBAA6B,sBAAsB,gBAAgB,GAAG,aAAa;AACvF,SAAO,aAAa,gBAAgB,mBAAmB,gBAAgB,OAAO,oBAAoB;AACpG;AAIA,SAASC,cAAa,MAAM,UAAU,KAAK;AACzC,MAAI,aAAa,SAAS,cAAc,KAAK,IAAI;AACjD,MAAI,CAAC,cAAc,eAAe,QAAQ;AACxC;AAAA,EACF;AACA,MAAI,aAAa,SAAS,cAAc,KAAK,OAAO,MAAM;AAC1D,MAAI,eAAe,SAAS,cAAc,KAAK,OAAO,QAAQ;AAC9D,MAAI,eAAe,SAAS,cAAc,KAAK,OAAO,QAAQ;AAC9D,MAAI,mBAAmB,SAAS,cAAc,KAAK,OAAO,YAAY;AACtE,MAAI,gBAA2B,oBAAoB,UAAU;AAC7D,MAAI,kBAA6B,sBAAsB,gBAAgB,GAAG,aAAa;AACvF,MAAI,aAAwB,aAAa,YAAY,CAAC,cAAc,CAAC,IAAI,IAAI,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,IAAI,gBAAgB,CAAC,GAAG,cAAc,CAAC,GAAG,cAAc,CAAC,GAAG,MAAM,gBAAgB;AACvM,aAAW,sBAAsB,gBAAgB,QAAQ,MAAM,YAAY,IAAI,SAAS,CAAC,eAAe,KAAK,KAAK,OAAO;AACzH,aAAW,OAAO;AAClB,SAAO;AACT;AACA,SAAS,WAAW,QAAQ;AAC1B,MAAI,OAAO,IAAI,iBAAW;AAAA,IACxB,MAAM;AAAA,IACN,kBAAkB;AAAA,EACpB,CAAC;AACD,gBAAc,KAAK,OAAO,MAAM;AAChC,SAAO;AACT;AACA,SAAS,cAAc,aAAa,QAAQ;AAC1C,cAAY,KAAK,OAAO,CAAC,EAAE,CAAC;AAC5B,cAAY,KAAK,OAAO,CAAC,EAAE,CAAC;AAC5B,cAAY,KAAK,OAAO,CAAC,EAAE,CAAC;AAC5B,cAAY,KAAK,OAAO,CAAC,EAAE,CAAC;AAC5B,cAAY,UAAU;AACtB,MAAI,MAAM,OAAO,CAAC;AAClB,MAAI,KAAK;AACP,gBAAY,OAAO,IAAI,CAAC;AACxB,gBAAY,OAAO,IAAI,CAAC;AAAA,EAC1B,OAAO;AACL,gBAAY,OAAO;AACnB,gBAAY,OAAO;AAAA,EACrB;AACF;AACA,IAAI;AAAA;AAAA,EAAoB,SAAU,QAAQ;AACxC,cAAUC,OAAM,MAAM;AACtB,aAASA,MAAK,UAAU,KAAK,aAAa;AACxC,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,YAAY,UAAU,KAAK,WAAW;AAC5C,aAAO;AAAA,IACT;AACA,IAAAA,MAAK,UAAU,cAAc,SAAU,UAAU,KAAK,aAAa;AACjE,UAAI,cAAc,SAAS;AAC3B,UAAI,aAAa,SAAS,cAAc,GAAG;AAC3C,UAAI,OAAO,WAAW,UAAU;AAChC,WAAK,MAAM,UAAU;AACrB,MAAQ,UAAU,MAAM;AAAA,QACtB,OAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACF,GAAG,aAAa,GAAG;AACnB,WAAK,IAAI,IAAI;AACb,WAAK,mBAAmB,SAAU,gBAAgB;AAChD,YAAI,SAASD,cAAa,gBAAgB,UAAU,GAAG;AAIvD,aAAK,IAAI,MAAM;AACf,aAAK,kBAAkB,cAAc,CAAC,IAAI,oBAAoB,gBAAgB,UAAU,GAAG;AAAA,MAC7F,GAAG,IAAI;AACP,WAAK,iBAAiB,UAAU,KAAK,WAAW;AAAA,IAClD;AAEA,IAAAC,MAAK,UAAU,aAAa,SAAU,UAAU,KAAK,aAAa;AAChE,UAAI,cAAc,SAAS;AAC3B,UAAI,OAAO,KAAK,YAAY,MAAM;AAClC,UAAI,aAAa,SAAS,cAAc,GAAG;AAC3C,UAAI,SAAS;AAAA,QACX,OAAO,CAAC;AAAA,MACV;AACA,oBAAc,OAAO,OAAO,UAAU;AACtC,MAAQ,YAAY,MAAM,QAAQ,aAAa,GAAG;AAClD,WAAK,mBAAmB,SAAU,gBAAgB;AAChD,YAAI,aAAa,oBAAoB,gBAAgB,UAAU,GAAG;AAClE,YAAI,MAAM,kBAAkB,cAAc;AAE1C,YAAI,KAAK,GAAG,MAAM,YAAY;AAC5B,eAAK,OAAO,KAAK,YAAY,cAAc,CAAC;AAC5C,cAAI,SAASD,cAAa,gBAAgB,UAAU,GAAG;AACvD,eAAK,IAAI,MAAM;AAAA,QACjB;AACA,aAAK,GAAG,IAAI;AAAA,MACd,GAAG,IAAI;AACP,WAAK,iBAAiB,UAAU,KAAK,WAAW;AAAA,IAClD;AACA;AACA,IAAAC,MAAK,UAAU,cAAc,WAAY;AACvC,aAAO,KAAK,QAAQ,CAAC;AAAA,IACvB;AACA,IAAAA,MAAK,UAAU,mBAAmB,SAAU,UAAU,KAAK,aAAa;AACtE,UAAI,cAAc,SAAS;AAC3B,UAAI,OAAO,KAAK,YAAY,MAAM;AAClC,UAAI,oBAAoB,eAAe,YAAY;AACnD,UAAI,gBAAgB,eAAe,YAAY;AAC/C,UAAI,kBAAkB,eAAe,YAAY;AACjD,UAAI,oBAAoB,eAAe,YAAY;AACnD,UAAI,mBAAmB,eAAe,YAAY;AAClD,UAAI,QAAQ,eAAe,YAAY;AACvC,UAAI,YAAY,eAAe,YAAY;AAE3C,UAAI,CAAC,eAAe,SAAS,eAAe;AAC1C,YAAI,YAAY,SAAS,aAAa,GAAG;AACzC,YAAI,gBAAgB,UAAU,SAAS,UAAU;AACjD,4BAAoB,cAAc,SAAS,WAAW,EAAE,aAAa;AACrE,wBAAgB,UAAU,SAAS,CAAC,QAAQ,WAAW,CAAC,EAAE,aAAa;AACvE,0BAAkB,UAAU,SAAS,CAAC,UAAU,WAAW,CAAC,EAAE,aAAa;AAC3E,2BAAmB,cAAc,IAAI,UAAU;AAC/C,gBAAQ,cAAc,IAAI,OAAO;AACjC,oBAAY,cAAc,IAAI,WAAW;AACzC,4BAAoB,qBAAqB,SAAS;AAAA,MACpD;AACA,UAAI,YAAY,SAAS,cAAc,KAAK,OAAO;AACnD,UAAI,cAAc,UAAU;AAC5B,WAAK,SAAS,SAAS;AACvB,WAAK,MAAM,OAAO;AAClB,WAAK,MAAM,gBAAgB;AAC3B,WAAK,YAAY,UAAU,EAAE,QAAQ;AACrC,WAAK,YAAY,MAAM,EAAE,QAAQ;AACjC,WAAK,YAAY,QAAQ,EAAE,QAAQ;AAEnC,WAAK,mBAAmB,SAAU,gBAAgB;AAChD,YAAI,SAAS,KAAK,YAAY,cAAc;AAC5C,YAAI,QAAQ;AAEV,iBAAO,SAAS,WAAW;AAC3B,iBAAO,MAAM,UAAU,UAAU;AACjC,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,YAAY,eAAe,CAAC;AAChC,gBAAI,YAAY,KAAK,SAAS,SAAS;AACvC,gBAAI,WAAW;AACb,kBAAI,iBAAiB,UAAU,SAAS,CAAC;AACzC,kBAAI,QAAQ,OAAO,YAAY,SAAS;AACxC,kBAAI,aAAa,MAAM,UAAU,MAAM,QAAQ,CAAC;AAChD,kBAAI,eAAe,UAAU,MAAM;AACjC,2BAAW,OAAO,iBAAiB,WAAW,MAAM,IAAI,eAAe;AAAA,cACzE;AACA,kBAAI,eAAe,WAAW,MAAM;AAClC,2BAAW,UAAU,eAAe;AAAA,cACtC;AAAA,YACF;AAAA,UACF;AACA,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF,GAAG,IAAI;AACP,UAAI,SAAS,YAAY,YAAY,GAAG;AACxC,oBAAc,MAAM,mBAAmB;AAAA,QACrC,gBAAgB;AAAA,QAChB,cAAc;AAAA,UACZ,mBAAmB,SAAU,WAAW,WAAW;AACjD,mBAAO,YAAY,kBAAkB,WAAW,WAAW,SAAS,QAAQ;AAAA,UAC9E;AAAA,QACF;AAAA,QACA,cAAc,eAAe;AAAA,QAC7B,gBAAgB,UAAU;AAAA,QAC1B,cAAc,UAAU,OAAO,SAAS,QAAQ,GAAG,IAAI,SAAS,MAAM,IAAI,MAAM,MAAM,IAAI,UAAU;AAAA,MACtG,CAAC;AACD,UAAI,QAAQ,KAAK,eAAe;AAGhC,UAAI,OAAO;AACT,YAAI,mBAAmB,kBAAkB;AACzC,cAAM,UAAU,MAAM,MAAM;AAC5B,cAAM,kBAAkB,MAAM,MAAM;AAEpC,cAAM,aAAa,iBAAiB,IAAI,UAAU,KAAK;AACvD,YAAI,WAAW,iBAAiB,IAAI,UAAU;AAC9C,YAAI,CAAC,QAAQ,QAAQ,GAAG;AACtB,qBAAW,CAAC,UAAU,QAAQ;AAAA,QAChC;AACA,cAAM,kBAAkB;AAAA,MAC1B;AACA,WAAK,cAAc;AAAA,QACjB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACV,CAAC;AAED,0BAAoB,MAAM,OAAO,WAAW,gBAAgB;AAAA,IAC9D;AACA,IAAAA,MAAK,UAAU,YAAY,WAAY;AACrC,oBAAc,IAAI;AAAA,IACpB;AACA,IAAAA,MAAK,UAAU,WAAW,WAAY;AACpC,oBAAc,IAAI;AAAA,IACpB;AACA,IAAAA,MAAK,UAAU,eAAe,SAAU,UAAU,KAAK;AACrD,WAAK,cAAc,SAAS,cAAc,GAAG,CAAC;AAAA,IAChD;AACA,IAAAA,MAAK,UAAU,gBAAgB,SAAU,QAAQ;AAC/C,UAAI,WAAW,KAAK,YAAY,MAAM;AACtC,oBAAc,SAAS,OAAO,MAAM;AACpC,eAAS,MAAM;AAAA,IACjB;AACA,IAAAA,MAAK,UAAU,eAAe,WAAY;AACxC,UAAI,YAAY;AAChB,UAAI,aAAa,UAAU,YAAY,YAAY;AACnD,UAAI,WAAW,UAAU,YAAY,UAAU;AAC/C,UAAI,QAAQ,UAAU,eAAe;AAErC,UAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,MAAM,SAAS;AACxD;AAAA,MACF;AACA,UAAI,WAAW;AACf,UAAI,aAAa,KAAK;AACtB,aAAO,YAAY;AACjB,YAAI,WAAW,QAAQ;AACrB,sBAAY,WAAW;AAAA,QACzB;AACA,qBAAa,WAAW;AAAA,MAC1B;AACA,UAAI,OAAO,UAAU,YAAY,MAAM;AAGvC,UAAI,CAAC,KAAK,WAAW,CAAC,KAAK,SAAS;AAClC;AAAA,MACF;AACA,UAAI,UAAU,KAAK,MAAM;AACzB,UAAI,UAAU,KAAK,QAAQ,CAAC;AAC5B,UAAI,QAAQ,KAAK,QAAQ,OAAO;AAChC,UAAI,IAAW,IAAI,CAAC,GAAG,OAAO,OAAO;AACrC,MAAO,UAAU,GAAG,CAAC;AACrB,eAAS,kBAAkB,QAAQC,UAAS;AAK1C,YAAI,oBAAoB,OAAO;AAC/B,YAAI,qBAAqB,MAAM;AAC7B,cAAIC,WAAU,KAAK,UAAUD,QAAO;AACpC,iBAAO,KAAK,aAAaA,aAAY,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,MAAMC,SAAQ,CAAC,GAAGA,SAAQ,CAAC,CAAC,CAAC;AAAA,QACrG,OAAO;AACL,iBAAO,KAAK,YAAY,iBAAiB;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,YAAY;AACd,mBAAW,YAAY,OAAO;AAC9B,0BAAkB,YAAY,CAAC;AAC/B,mBAAW,SAAS,WAAW,SAAS,WAAW;AACnD,mBAAW,WAAW;AAAA,MACxB;AACA,UAAI,UAAU;AACZ,iBAAS,YAAY,KAAK;AAC1B,0BAAkB,UAAU,CAAC;AAC7B,iBAAS,SAAS,SAAS,SAAS,WAAW;AAC/C,iBAAS,WAAW;AAAA,MACtB;AACA,UAAI,SAAS,CAAC,MAAM,QAAQ;AAC1B,cAAM,IAAI,MAAM,IAAI;AACpB,cAAM,UAAU,MAAM,UAAU;AAChC,YAAI,YAAY;AAChB,YAAI,oBAAoB;AACxB,YAAI,WAAW,MAAM;AACrB,YAAI,YAAY,SAAS,CAAC,IAAI;AAC9B,YAAI,YAAY,SAAS,CAAC,IAAI;AAC9B,YAAI,cAAc,UAAU;AAC5B,YAAI,UAAU,KAAK,UAAU,WAAW;AACxC,YAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAChC,YAAI,KAAK,KAAK,QAAQ,WAAW;AACjC,YAAI,EAAE,CAAC,IAAI,GAAG;AACZ,YAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACX,YAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AAAA,QACb;AACA,YAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,KAAK;AAChC,YAAI,MAAM,eAAe,WAAW,MAAM,eAAe,OAAO;AAC9D,cAAI,WAAW,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AACjD,cAAI,MAAM,CAAC,IAAI,QAAQ,CAAC,GAAG;AACzB,uBAAW,KAAK,KAAK;AAAA,UACvB;AACA,gBAAM,WAAW;AAAA,QACnB;AACA,YAAI,KAAK;AACT,gBAAQ,MAAM,YAAY;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,CAAC;AACN,gCAAoB;AACpB;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK;AACL,gCAAoB;AACpB;AAAA,UACF;AACE,iBAAK;AACL,gCAAoB;AAAA,QACxB;AACA,gBAAQ,MAAM,YAAY;AAAA,UACxB,KAAK;AACH,kBAAM,IAAI,EAAE,CAAC,IAAI,YAAY,MAAM,CAAC;AACpC,kBAAM,IAAI,EAAE,CAAC,IAAI,YAAY,MAAM,CAAC;AACpC,wBAAY,EAAE,CAAC,IAAI,MAAM,SAAS,EAAE,CAAC,IAAI,OAAO,UAAU;AAC1D,gCAAoB,EAAE,CAAC,IAAI,MAAM,QAAQ,EAAE,CAAC,IAAI,OAAO,WAAW;AAClE;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,CAAC,EAAE,CAAC,IAAI,YAAY,QAAQ,CAAC;AACvC,kBAAM,IAAI,CAAC,EAAE,CAAC,IAAI,YAAY,QAAQ,CAAC;AACvC,wBAAY,EAAE,CAAC,IAAI,MAAM,UAAU,EAAE,CAAC,IAAI,OAAO,SAAS;AAC1D,gCAAoB,EAAE,CAAC,IAAI,MAAM,WAAW,EAAE,CAAC,IAAI,OAAO,QAAQ;AAClE;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,kBAAM,IAAI,YAAY,MAAM,QAAQ,CAAC;AACrC,kBAAM,IAAI,QAAQ,CAAC,IAAI;AACvB,wBAAY,QAAQ,CAAC,IAAI,IAAI,UAAU;AACvC,kBAAM,UAAU,CAAC,YAAY;AAC7B,kBAAM,UAAU,CAAC;AACjB;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,kBAAM,IAAI,GAAG,CAAC;AACd,kBAAM,IAAI,GAAG,CAAC,IAAI;AAClB,wBAAY;AACZ,kBAAM,UAAU,CAAC;AACjB;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,kBAAM,IAAI,CAAC,YAAY,MAAM,MAAM,CAAC;AACpC,kBAAM,IAAI,MAAM,CAAC,IAAI;AACrB,wBAAY,QAAQ,CAAC,KAAK,IAAI,UAAU;AACxC,kBAAM,UAAU,YAAY;AAC5B,kBAAM,UAAU,CAAC;AACjB;AAAA,QACJ;AACA,cAAM,SAAS,MAAM,SAAS;AAC9B,cAAM,SAAS;AAAA;AAAA,UAEb,eAAe,MAAM,mBAAmB;AAAA,UACxC,OAAO,MAAM,WAAW;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAOF;AAAA,EACT,EAAU,aAAK;AAAA;AACf,IAAOG,gBAAQ;;;ACnXf,IAAI;AAAA;AAAA,EAAwB,WAAY;AACtC,aAASC,UAAS,UAAU;AAC1B,WAAK,QAAQ,IAAY,cAAM;AAC/B,WAAK,YAAY,YAAYC;AAAA,IAC/B;AACA,IAAAD,UAAS,UAAU,aAAa,SAAU,UAAU;AAClD,UAAI,QAAQ;AAEZ,WAAK,kBAAkB;AACvB,UAAI,WAAW;AACf,UAAI,QAAQ,SAAS;AACrB,UAAI,cAAc,SAAS;AAC3B,eAAS,YAAY;AAGrB,UAAI,CAAC,aAAa;AAChB,cAAM,UAAU;AAAA,MAClB;AACA,UAAI,cAAc,gBAAgB,QAAQ;AAC1C,eAAS,KAAK,WAAW,EAAE,IAAI,SAAU,KAAK;AAC5C,cAAM,OAAO,UAAU,KAAK,WAAW;AAAA,MACzC,CAAC,EAAE,OAAO,SAAU,QAAQ,QAAQ;AAClC,cAAM,UAAU,aAAa,UAAU,QAAQ,QAAQ,WAAW;AAAA,MACpE,CAAC,EAAE,OAAO,SAAU,KAAK;AACvB,cAAM,OAAO,YAAY,iBAAiB,GAAG,CAAC;AAAA,MAChD,CAAC,EAAE,QAAQ;AAAA,IACb;AACA;AACA,IAAAA,UAAS,UAAU,eAAe,WAAY;AAC5C,UAAI,WAAW,KAAK;AAEpB,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AACA,eAAS,kBAAkB,SAAU,IAAI,KAAK;AAC5C,WAAG,aAAa,UAAU,GAAG;AAAA,MAC/B,GAAG,IAAI;AAAA,IACT;AACA;AACA,IAAAA,UAAS,UAAU,2BAA2B,SAAU,UAAU;AAChE,WAAK,eAAe,gBAAgB,QAAQ;AAC5C,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AAAA,IACvB;AACA;AACA,IAAAA,UAAS,UAAU,oBAAoB,SAAU,YAAY,UAAU;AACrE,WAAK,kBAAkB,CAAC;AACxB,eAAS,0BAA0BE,KAAI;AACrC,YAAI,CAACA,IAAG,WAAW,CAAC,eAAeA,GAAE,GAAG;AACtC,UAAAA,IAAG,cAAc;AACjB,UAAAA,IAAG,YAAY,UAAU,EAAE,aAAa;AAAA,QAC1C;AAAA,MACF;AACA,eAAS,MAAM,WAAW,OAAO,MAAM,WAAW,KAAK,OAAO;AAC5D,YAAI,aAAa,SAAS,cAAc,GAAG;AAC3C,YAAI,cAAc,UAAU,GAAG;AAC7B,cAAI,KAAK,IAAI,KAAK,UAAU,UAAU,KAAK,KAAK,YAAY;AAC5D,aAAG,SAAS,yBAAyB;AACrC,eAAK,MAAM,IAAI,EAAE;AACjB,mBAAS,iBAAiB,KAAK,EAAE;AACjC,eAAK,gBAAgB,KAAK,EAAE;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AACA;AACA,IAAAF,UAAS,UAAU,SAAS,WAAY;AACtC,WAAK,MAAM,UAAU;AAAA,IACvB;AACA;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,IAAI;AAC9C,MAAQ,iBAAiB,KAAK,mBAAmB,KAAK,OAAO,EAAE;AAAA,IACjE;AACA,IAAAA,UAAS,UAAU,SAAS,SAAU,UAAU,KAAK,aAAa;AAChE,UAAI,aAAa,SAAS,cAAc,GAAG;AAC3C,UAAI,CAAC,cAAc,UAAU,GAAG;AAC9B;AAAA,MACF;AACA,UAAI,KAAK,IAAI,KAAK,UAAU,UAAU,KAAK,WAAW;AACtD,eAAS,iBAAiB,KAAK,EAAE;AACjC,WAAK,MAAM,IAAI,EAAE;AAAA,IACnB;AACA,IAAAA,UAAS,UAAU,YAAY,SAAU,aAAa,aAAa,QAAQ,QAAQ,aAAa;AAC9F,UAAI,SAAS,YAAY,iBAAiB,MAAM;AAChD,UAAI,CAAC,cAAc,YAAY,cAAc,MAAM,CAAC,GAAG;AACrD,aAAK,MAAM,OAAO,MAAM;AACxB;AAAA,MACF;AACA,UAAI,CAAC,QAAQ;AACX,iBAAS,IAAI,KAAK,UAAU,aAAa,QAAQ,WAAW;AAAA,MAC9D,OAAO;AACL,eAAO,WAAW,aAAa,QAAQ,WAAW;AAAA,MACpD;AACA,kBAAY,iBAAiB,QAAQ,MAAM;AAC3C,WAAK,MAAM,IAAI,MAAM;AAAA,IACvB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,SAAS,eAAe,IAAI;AAC1B,SAAO,GAAG,aAAa,GAAG,UAAU,SAAS;AAC/C;AACA,SAAS,gBAAgB,UAAU;AACjC,MAAI,YAAY,SAAS;AACzB,MAAI,gBAAgB,UAAU,SAAS,UAAU;AACjD,SAAO;AAAA,IACL,WAAW,UAAU,SAAS,WAAW,EAAE,aAAa;AAAA,IACxD,mBAAmB,cAAc,SAAS,CAAC,WAAW,CAAC,EAAE,aAAa;AAAA,IACtE,eAAe,UAAU,SAAS,CAAC,QAAQ,WAAW,CAAC,EAAE,aAAa;AAAA,IACtE,iBAAiB,UAAU,SAAS,CAAC,UAAU,WAAW,CAAC,EAAE,aAAa;AAAA,IAC1E,kBAAkB,cAAc,IAAI,UAAU;AAAA,IAC9C,WAAW,cAAc,IAAI,WAAW;AAAA,IACxC,OAAO,cAAc,IAAI,OAAO;AAAA,IAChC,mBAAmB,qBAAqB,SAAS;AAAA,EACnD;AACF;AACA,SAAS,WAAW,IAAI;AACtB,SAAO,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC;AACpC;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;AACzD;AACA,IAAO,mBAAQ;", "names": ["StraightLineShape", "CurveShape", "ECLinePath", "createSymbol", "Line", "percent", "tangent", "Line_default", "LineDraw", "Line_default", "el"]}