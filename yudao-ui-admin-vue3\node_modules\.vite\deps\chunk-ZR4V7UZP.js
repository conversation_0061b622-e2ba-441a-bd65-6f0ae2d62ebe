import {
  Component_default as Component_default2,
  isDimensionStacked
} from "./chunk-H732WCN4.js";
import {
  Component_default,
  DataFormatMixin,
  createTooltipMarkup,
  defaultEmphasis,
  enterBlur,
  getPrecision,
  leaveBlur,
  makeInner,
  parseDataValue
} from "./chunk-Q47K3BNQ.js";
import {
  __extends,
  clone,
  createHashMap,
  curry,
  each,
  env_default,
  extend,
  indexOf,
  isArray,
  mixin
} from "./chunk-MUBQFVAI.js";

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/checkMarkerInSeries.js
function checkMarkerInSeries(seriesOpts, markerType) {
  if (!seriesOpts) {
    return false;
  }
  var seriesOptArr = isArray(seriesOpts) ? seriesOpts : [seriesOpts];
  for (var idx = 0; idx < seriesOptArr.length; idx++) {
    if (seriesOptArr[idx] && seriesOptArr[idx][markerType]) {
      return true;
    }
  }
  return false;
}

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkerModel.js
function fillLabel(opt) {
  defaultEmphasis(opt, "label", ["show"]);
}
var inner = makeInner();
var MarkerModel = (
  /** @class */
  function(_super) {
    __extends(MarkerModel2, _super);
    function MarkerModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkerModel2.type;
      _this.createdBySelf = false;
      return _this;
    }
    MarkerModel2.prototype.init = function(option, parentModel, ecModel) {
      if (true) {
        if (this.type === "marker") {
          throw new Error("Marker component is abstract component. Use markLine, markPoint, markArea instead.");
        }
      }
      this.mergeDefaultAndTheme(option, ecModel);
      this._mergeOption(option, ecModel, false, true);
    };
    MarkerModel2.prototype.isAnimationEnabled = function() {
      if (env_default.node) {
        return false;
      }
      var hostSeries = this.__hostSeries;
      return this.getShallow("animation") && hostSeries && hostSeries.isAnimationEnabled();
    };
    MarkerModel2.prototype.mergeOption = function(newOpt, ecModel) {
      this._mergeOption(newOpt, ecModel, false, false);
    };
    MarkerModel2.prototype._mergeOption = function(newOpt, ecModel, createdBySelf, isInit) {
      var componentType = this.mainType;
      if (!createdBySelf) {
        ecModel.eachSeries(function(seriesModel) {
          var markerOpt = seriesModel.get(this.mainType, true);
          var markerModel = inner(seriesModel)[componentType];
          if (!markerOpt || !markerOpt.data) {
            inner(seriesModel)[componentType] = null;
            return;
          }
          if (!markerModel) {
            if (isInit) {
              fillLabel(markerOpt);
            }
            each(markerOpt.data, function(item) {
              if (item instanceof Array) {
                fillLabel(item[0]);
                fillLabel(item[1]);
              } else {
                fillLabel(item);
              }
            });
            markerModel = this.createMarkerModelFromSeries(markerOpt, this, ecModel);
            extend(markerModel, {
              mainType: this.mainType,
              // Use the same series index and name
              seriesIndex: seriesModel.seriesIndex,
              name: seriesModel.name,
              createdBySelf: true
            });
            markerModel.__hostSeries = seriesModel;
          } else {
            markerModel._mergeOption(markerOpt, ecModel, true);
          }
          inner(seriesModel)[componentType] = markerModel;
        }, this);
      }
    };
    MarkerModel2.prototype.formatTooltip = function(dataIndex, multipleSeries, dataType) {
      var data = this.getData();
      var value = this.getRawValue(dataIndex);
      var itemName = data.getName(dataIndex);
      return createTooltipMarkup("section", {
        header: this.name,
        blocks: [createTooltipMarkup("nameValue", {
          name: itemName,
          value,
          noName: !itemName,
          noValue: value == null
        })]
      });
    };
    MarkerModel2.prototype.getData = function() {
      return this._data;
    };
    MarkerModel2.prototype.setData = function(data) {
      this._data = data;
    };
    MarkerModel2.prototype.getDataParams = function(dataIndex, dataType) {
      var params = DataFormatMixin.prototype.getDataParams.call(this, dataIndex, dataType);
      var hostSeries = this.__hostSeries;
      if (hostSeries) {
        params.seriesId = hostSeries.id;
        params.seriesName = hostSeries.name;
        params.seriesType = hostSeries.subType;
      }
      return params;
    };
    MarkerModel2.getMarkerModelFromSeries = function(seriesModel, componentType) {
      return inner(seriesModel)[componentType];
    };
    MarkerModel2.type = "marker";
    MarkerModel2.dependencies = ["series", "grid", "polar", "geo"];
    return MarkerModel2;
  }(Component_default)
);
mixin(MarkerModel, DataFormatMixin.prototype);
var MarkerModel_default = MarkerModel;

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/markerHelper.js
function hasXOrY(item) {
  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));
}
function hasXAndY(item) {
  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));
}
function markerTypeCalculatorWithExtent(markerType, data, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {
  var coordArr = [];
  var stacked = isDimensionStacked(
    data,
    targetDataDim
    /* , otherDataDim */
  );
  var calcDataDim = stacked ? data.getCalculationInfo("stackResultDimension") : targetDataDim;
  var value = numCalculate(data, calcDataDim, markerType);
  var dataIndex = data.indicesOfNearest(calcDataDim, value)[0];
  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);
  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);
  var coordArrValue = data.get(targetDataDim, dataIndex);
  var precision = getPrecision(data.get(targetDataDim, dataIndex));
  precision = Math.min(precision, 20);
  if (precision >= 0) {
    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);
  }
  return [coordArr, coordArrValue];
}
var markerTypeCalculator = {
  min: curry(markerTypeCalculatorWithExtent, "min"),
  max: curry(markerTypeCalculatorWithExtent, "max"),
  average: curry(markerTypeCalculatorWithExtent, "average"),
  median: curry(markerTypeCalculatorWithExtent, "median")
};
function dataTransform(seriesModel, item) {
  if (!item) {
    return;
  }
  var data = seriesModel.getData();
  var coordSys = seriesModel.coordinateSystem;
  var dims = coordSys && coordSys.dimensions;
  if (!hasXAndY(item) && !isArray(item.coord) && isArray(dims)) {
    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);
    item = clone(item);
    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {
      var otherCoordIndex = indexOf(dims, axisInfo.baseAxis.dim);
      var targetCoordIndex = indexOf(dims, axisInfo.valueAxis.dim);
      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);
      item.coord = coordInfo[0];
      item.value = coordInfo[1];
    } else {
      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];
    }
  }
  if (item.coord == null || !isArray(dims)) {
    item.coord = [];
  } else {
    var coord = item.coord;
    for (var i = 0; i < 2; i++) {
      if (markerTypeCalculator[coord[i]]) {
        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);
      }
    }
  }
  return item;
}
function getAxisInfo(item, data, coordSys, seriesModel) {
  var ret = {};
  if (item.valueIndex != null || item.valueDim != null) {
    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;
    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));
    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);
    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);
  } else {
    ret.baseAxis = seriesModel.getBaseAxis();
    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);
    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);
    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);
  }
  return ret;
}
function dataDimToCoordDim(seriesModel, dataDim) {
  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);
  return dimItem && dimItem.coordDim;
}
function dataFilter(coordSys, item) {
  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;
}
function zoneFilter(coordSys, item1, item2) {
  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;
}
function createMarkerDimValueGetter(inCoordSys, dims) {
  return inCoordSys ? function(item, dimName, dataIndex, dimIndex) {
    var rawVal = dimIndex < 2 ? item.coord && item.coord[dimIndex] : item.value;
    return parseDataValue(rawVal, dims[dimIndex]);
  } : function(item, dimName, dataIndex, dimIndex) {
    return parseDataValue(item.value, dims[dimIndex]);
  };
}
function numCalculate(data, valueDataDim, type) {
  if (type === "average") {
    var sum_1 = 0;
    var count_1 = 0;
    data.each(valueDataDim, function(val, idx) {
      if (!isNaN(val)) {
        sum_1 += val;
        count_1++;
      }
    });
    return sum_1 / count_1;
  } else if (type === "median") {
    return data.getMedian(valueDataDim);
  } else {
    return data.getDataExtent(valueDataDim)[type === "max" ? 1 : 0];
  }
}

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/lib/component/marker/MarkerView.js
var inner2 = makeInner();
var MarkerView = (
  /** @class */
  function(_super) {
    __extends(MarkerView2, _super);
    function MarkerView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkerView2.type;
      return _this;
    }
    MarkerView2.prototype.init = function() {
      this.markerGroupMap = createHashMap();
    };
    MarkerView2.prototype.render = function(markerModel, ecModel, api) {
      var _this = this;
      var markerGroupMap = this.markerGroupMap;
      markerGroupMap.each(function(item) {
        inner2(item).keep = false;
      });
      ecModel.eachSeries(function(seriesModel) {
        var markerModel2 = MarkerModel_default.getMarkerModelFromSeries(seriesModel, _this.type);
        markerModel2 && _this.renderSeries(seriesModel, markerModel2, ecModel, api);
      });
      markerGroupMap.each(function(item) {
        !inner2(item).keep && _this.group.remove(item.group);
      });
    };
    MarkerView2.prototype.markKeep = function(drawGroup) {
      inner2(drawGroup).keep = true;
    };
    MarkerView2.prototype.toggleBlurSeries = function(seriesModelList, isBlur) {
      var _this = this;
      each(seriesModelList, function(seriesModel) {
        var markerModel = MarkerModel_default.getMarkerModelFromSeries(seriesModel, _this.type);
        if (markerModel) {
          var data = markerModel.getData();
          data.eachItemGraphicEl(function(el) {
            if (el) {
              isBlur ? enterBlur(el) : leaveBlur(el);
            }
          });
        }
      });
    };
    MarkerView2.type = "marker";
    return MarkerView2;
  }(Component_default2)
);
var MarkerView_default = MarkerView;

export {
  checkMarkerInSeries,
  MarkerModel_default,
  dataTransform,
  getAxisInfo,
  dataFilter,
  zoneFilter,
  createMarkerDimValueGetter,
  numCalculate,
  MarkerView_default
};
//# sourceMappingURL=chunk-ZR4V7UZP.js.map
