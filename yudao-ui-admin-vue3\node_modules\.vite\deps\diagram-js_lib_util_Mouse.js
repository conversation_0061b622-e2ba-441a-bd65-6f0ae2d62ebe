import {
  getOriginal
} from "./chunk-M4AQHOXJ.js";
import "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Platform.js
function isMac() {
  return /mac/i.test(navigator.platform);
}

// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Mouse.js
function isButton(event, button) {
  return (getOriginal(event) || event).button === button;
}
function isPrimaryButton(event) {
  return isButton(event, 0);
}
function isAuxiliaryButton(event) {
  return isButton(event, 1);
}
function isSecondaryButton(event) {
  return isButton(event, 2);
}
function hasPrimaryModifier(event) {
  var originalEvent = getOriginal(event) || event;
  if (!isPrimaryButton(event)) {
    return false;
  }
  if (isMac()) {
    return originalEvent.metaKey;
  } else {
    return originalEvent.ctrlKey;
  }
}
function hasSecondaryModifier(event) {
  var originalEvent = getOriginal(event) || event;
  return isPrimaryButton(event) && originalEvent.shiftKey;
}
export {
  hasPrimaryModifier,
  hasSecondaryModifier,
  isAuxiliaryButton,
  isButton,
  isMac,
  isPrimaryButton,
  isSecondaryButton
};
//# sourceMappingURL=diagram-js_lib_util_Mouse.js.map
