{"version": 3, "sources": ["../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Platform.js", "../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Mouse.js"], "sourcesContent": ["export function isMac() {\n  return (/mac/i).test(navigator.platform);\n}", "import {\n  getOriginal as getOriginalEvent\n} from './Event';\n\nimport {\n  isMac\n} from './Platform';\n\nexport {\n  isMac\n} from './Platform';\n\n/**\n * @param {MouseEvent} event\n * @param {string} button\n *\n * @return {boolean}\n */\nexport function isButton(event, button) {\n  return (getOriginalEvent(event) || event).button === button;\n}\n\n/**\n * @param {MouseEvent} event\n *\n * @return {boolean}\n */\nexport function isPrimaryButton(event) {\n\n  // button === 0 -> left áka primary mouse button\n  return isButton(event, 0);\n}\n\n/**\n * @param {MouseEvent} event\n *\n * @return {boolean}\n */\nexport function isAuxiliaryButton(event) {\n\n  // button === 1 -> auxiliary áka wheel button\n  return isButton(event, 1);\n}\n\n/**\n * @param {MouseEvent} event\n *\n * @return {boolean}\n */\nexport function isSecondaryButton(event) {\n\n  // button === 2 -> right áka secondary button\n  return isButton(event, 2);\n}\n\n/**\n * @param {MouseEvent} event\n *\n * @return {boolean}\n */\nexport function hasPrimaryModifier(event) {\n  var originalEvent = getOriginalEvent(event) || event;\n\n  if (!isPrimaryButton(event)) {\n    return false;\n  }\n\n  // Use cmd as primary modifier key for mac OS\n  if (isMac()) {\n    return originalEvent.metaKey;\n  } else {\n    return originalEvent.ctrlKey;\n  }\n}\n\n/**\n * @param {MouseEvent} event\n *\n * @return {boolean}\n */\nexport function hasSecondaryModifier(event) {\n  var originalEvent = getOriginalEvent(event) || event;\n\n  return isPrimaryButton(event) && originalEvent.shiftKey;\n}\n"], "mappings": ";;;;;;AAAO,SAAS,QAAQ;AACtB,SAAQ,OAAQ,KAAK,UAAU,QAAQ;AACzC;;;ACgBO,SAAS,SAAS,OAAO,QAAQ;AACtC,UAAQ,YAAiB,KAAK,KAAK,OAAO,WAAW;AACvD;AAOO,SAAS,gBAAgB,OAAO;AAGrC,SAAO,SAAS,OAAO,CAAC;AAC1B;AAOO,SAAS,kBAAkB,OAAO;AAGvC,SAAO,SAAS,OAAO,CAAC;AAC1B;AAOO,SAAS,kBAAkB,OAAO;AAGvC,SAAO,SAAS,OAAO,CAAC;AAC1B;AAOO,SAAS,mBAAmB,OAAO;AACxC,MAAI,gBAAgB,YAAiB,KAAK,KAAK;AAE/C,MAAI,CAAC,gBAAgB,KAAK,GAAG;AAC3B,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,GAAG;AACX,WAAO,cAAc;AAAA,EACvB,OAAO;AACL,WAAO,cAAc;AAAA,EACvB;AACF;AAOO,SAAS,qBAAqB,OAAO;AAC1C,MAAI,gBAAgB,YAAiB,KAAK,KAAK;AAE/C,SAAO,gBAAgB,KAAK,KAAK,cAAc;AACjD;", "names": []}