{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/echarts.js", "../../.pnpm/echarts-wordcloud@2.1.0_echarts@5.5.1/node_modules/echarts-wordcloud/src/WordCloudSeries.js", "../../.pnpm/echarts-wordcloud@2.1.0_echarts@5.5.1/node_modules/echarts-wordcloud/src/WordCloudView.js", "../../.pnpm/echarts-wordcloud@2.1.0_echarts@5.5.1/node_modules/echarts-wordcloud/src/layout.js", "../../.pnpm/echarts-wordcloud@2.1.0_echarts@5.5.1/node_modules/echarts-wordcloud/src/wordCloud.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport * from './export/core.js';\nimport { use } from './extension.js';\nimport { init } from './core/echarts.js';\nimport { install as CanvasRenderer } from './renderer/installCanvasRenderer.js';\nimport { install as DatasetComponent } from './component/dataset/install.js';\n// Default to have canvas renderer and dataset for compitatble reason.\nuse([CanvasRenderer, DatasetComponent]);\n// TODO: Compatitable with the following code\n// import echarts from 'echarts/lib/echarts.js'\nexport default {\n  init: function () {\n    if (process.env.NODE_ENV !== 'production') {\n      /* eslint-disable-next-line */\n      console.error(\"\\\"import echarts from 'echarts/lib/echarts.js'\\\" is not supported anymore. Use \\\"import * as echarts from 'echarts/lib/echarts.js'\\\" instead;\");\n    }\n    // @ts-ignore\n    return init.apply(null, arguments);\n  }\n};\n// Import label layout by default.\n// TODO remove\nimport { installLabelLayout } from './label/installLabelLayout.js';\nuse(installLabelLayout);", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendSeriesModel({\n  type: 'series.wordCloud',\n\n  visualStyleAccessPath: 'textStyle',\n  visualStyleMapper: function (model) {\n    return {\n      fill: model.get('color')\n    };\n  },\n  visualDrawType: 'fill',\n\n  optionUpdated: function () {\n    var option = this.option;\n    option.gridSize = Math.max(Math.floor(option.gridSize), 4);\n  },\n\n  getInitialData: function (option, ecModel) {\n    var dimensions = echarts.helper.createDimensions(option.data, {\n      coordDimensions: ['value']\n    });\n    var list = new echarts.List(dimensions, this);\n    list.initData(option.data);\n    return list;\n  },\n\n  // Most of options are from https://github.com/timdream/wordcloud2.js/blob/gh-pages/API.md\n  defaultOption: {\n    maskImage: null,\n\n    // Shape can be 'circle', 'cardioid', 'diamond', 'triangle-forward', 'triangle', 'pentagon', 'star'\n    shape: 'circle',\n    keepAspect: false,\n\n    left: 'center',\n\n    top: 'center',\n\n    width: '70%',\n\n    height: '80%',\n\n    sizeRange: [12, 60],\n\n    rotationRange: [-90, 90],\n\n    rotationStep: 45,\n\n    gridSize: 8,\n\n    drawOutOfBound: false,\n    shrinkToFit: false,\n\n    textStyle: {\n      fontWeight: 'normal'\n    }\n  }\n});\n", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendChartView({\n  type: 'wordCloud',\n\n  render: function (seriesModel, ecModel, api) {\n    var group = this.group;\n    group.removeAll();\n\n    var data = seriesModel.getData();\n\n    var gridSize = seriesModel.get('gridSize');\n\n    seriesModel.layoutInstance.ondraw = function (text, size, dataIdx, drawn) {\n      var itemModel = data.getItemModel(dataIdx);\n      var textStyleModel = itemModel.getModel('textStyle');\n\n      var textEl = new echarts.graphic.Text({\n        style: echarts.helper.createTextStyle(textStyleModel),\n        scaleX: 1 / drawn.info.mu,\n        scaleY: 1 / drawn.info.mu,\n        x: (drawn.gx + drawn.info.gw / 2) * gridSize,\n        y: (drawn.gy + drawn.info.gh / 2) * gridSize,\n        rotation: drawn.rot\n      });\n      textEl.setStyle({\n        x: drawn.info.fillTextOffsetX,\n        y: drawn.info.fillTextOffsetY + size * 0.5,\n        text: text,\n        verticalAlign: 'middle',\n        fill: data.getItemVisual(dataIdx, 'style').fill,\n        fontSize: size\n      });\n\n      group.add(textEl);\n\n      data.setItemGraphicEl(dataIdx, textEl);\n\n      textEl.ensureState('emphasis').style = echarts.helper.createTextStyle(\n        itemModel.getModel(['emphasis', 'textStyle']),\n        {\n          state: 'emphasis'\n        }\n      );\n      textEl.ensureState('blur').style = echarts.helper.createTextStyle(\n        itemModel.getModel(['blur', 'textStyle']),\n        {\n          state: 'blur'\n        }\n      );\n\n      echarts.helper.enableHoverEmphasis(\n        textEl,\n        itemModel.get(['emphasis', 'focus']),\n        itemModel.get(['emphasis', 'blurScope'])\n      );\n\n      textEl.stateTransition = {\n        duration: seriesModel.get('animation')\n          ? seriesModel.get(['stateAnimation', 'duration'])\n          : 0,\n        easing: seriesModel.get(['stateAnimation', 'easing'])\n      };\n      // TODO\n      textEl.__highDownDispatcher = true;\n    };\n\n    this._model = seriesModel;\n  },\n\n  remove: function () {\n    this.group.removeAll();\n\n    this._model.layoutInstance.dispose();\n  },\n\n  dispose: function () {\n    this._model.layoutInstance.dispose();\n  }\n});\n", "/*!\n * wordcloud2.js\n * http://timdream.org/wordcloud2.js/\n *\n * Copyright 2011 - 2019 <PERSON> and contributors.\n * Released under the MIT license\n */\n\n'use strict';\n\n// setImmediate\nif (!window.setImmediate) {\n  window.setImmediate = (function setupSetImmediate() {\n    return (\n      window.msSetImmediate ||\n      window.webkitSetImmediate ||\n      window.mozSetImmediate ||\n      window.oSetImmediate ||\n      (function setupSetZeroTimeout() {\n        if (!window.postMessage || !window.addEventListener) {\n          return null;\n        }\n\n        var callbacks = [undefined];\n        var message = 'zero-timeout-message';\n\n        // Like setTimeout, but only takes a function argument.  There's\n        // no time argument (always zero) and no arguments (you have to\n        // use a closure).\n        var setZeroTimeout = function setZeroTimeout(callback) {\n          var id = callbacks.length;\n          callbacks.push(callback);\n          window.postMessage(message + id.toString(36), '*');\n\n          return id;\n        };\n\n        window.addEventListener(\n          'message',\n          function setZeroTimeoutMessage(evt) {\n            // Skipping checking event source, retarded IE confused this window\n            // object with another in the presence of iframe\n            if (\n              typeof evt.data !== 'string' ||\n              evt.data.substr(0, message.length) !== message /* ||\n            evt.source !== window */\n            ) {\n              return;\n            }\n\n            evt.stopImmediatePropagation();\n\n            var id = parseInt(evt.data.substr(message.length), 36);\n            if (!callbacks[id]) {\n              return;\n            }\n\n            callbacks[id]();\n            callbacks[id] = undefined;\n          },\n          true\n        );\n\n        /* specify clearImmediate() here since we need the scope */\n        window.clearImmediate = function clearZeroTimeout(id) {\n          if (!callbacks[id]) {\n            return;\n          }\n\n          callbacks[id] = undefined;\n        };\n\n        return setZeroTimeout;\n      })() ||\n      // fallback\n      function setImmediateFallback(fn) {\n        window.setTimeout(fn, 0);\n      }\n    );\n  })();\n}\n\nif (!window.clearImmediate) {\n  window.clearImmediate = (function setupClearImmediate() {\n    return (\n      window.msClearImmediate ||\n      window.webkitClearImmediate ||\n      window.mozClearImmediate ||\n      window.oClearImmediate ||\n      // \"clearZeroTimeout\" is implement on the previous block ||\n      // fallback\n      function clearImmediateFallback(timer) {\n        window.clearTimeout(timer);\n      }\n    );\n  })();\n}\n\n// Check if WordCloud can run on this browser\nvar isSupported = (function isSupported() {\n  var canvas = document.createElement('canvas');\n  if (!canvas || !canvas.getContext) {\n    return false;\n  }\n\n  var ctx = canvas.getContext('2d');\n  if (!ctx) {\n    return false;\n  }\n  if (!ctx.getImageData) {\n    return false;\n  }\n  if (!ctx.fillText) {\n    return false;\n  }\n\n  if (!Array.prototype.some) {\n    return false;\n  }\n  if (!Array.prototype.push) {\n    return false;\n  }\n\n  return true;\n})();\n\n// Find out if the browser impose minium font size by\n// drawing small texts on a canvas and measure it's width.\nvar minFontSize = (function getMinFontSize() {\n  if (!isSupported) {\n    return;\n  }\n\n  var ctx = document.createElement('canvas').getContext('2d');\n\n  // start from 20\n  var size = 20;\n\n  // two sizes to measure\n  var hanWidth, mWidth;\n\n  while (size) {\n    ctx.font = size.toString(10) + 'px sans-serif';\n    if (\n      ctx.measureText('\\uFF37').width === hanWidth &&\n      ctx.measureText('m').width === mWidth\n    ) {\n      return size + 1;\n    }\n\n    hanWidth = ctx.measureText('\\uFF37').width;\n    mWidth = ctx.measureText('m').width;\n\n    size--;\n  }\n\n  return 0;\n})();\n\nvar getItemExtraData = function (item) {\n  if (Array.isArray(item)) {\n    var itemCopy = item.slice();\n    // remove data we already have (word and weight)\n    itemCopy.splice(0, 2);\n    return itemCopy;\n  } else {\n    return [];\n  }\n};\n\n// Based on http://jsfromhell.com/array/shuffle\nvar shuffleArray = function shuffleArray(arr) {\n  for (var j, x, i = arr.length; i; ) {\n    j = Math.floor(Math.random() * i);\n    x = arr[--i];\n    arr[i] = arr[j];\n    arr[j] = x;\n  }\n  return arr;\n};\n\nvar timer = {};\nvar WordCloud = function WordCloud(elements, options) {\n  if (!isSupported) {\n    return;\n  }\n\n  var timerId = Math.floor(Math.random() * Date.now());\n\n  if (!Array.isArray(elements)) {\n    elements = [elements];\n  }\n\n  elements.forEach(function (el, i) {\n    if (typeof el === 'string') {\n      elements[i] = document.getElementById(el);\n      if (!elements[i]) {\n        throw new Error('The element id specified is not found.');\n      }\n    } else if (!el.tagName && !el.appendChild) {\n      throw new Error(\n        'You must pass valid HTML elements, or ID of the element.'\n      );\n    }\n  });\n\n  /* Default values to be overwritten by options object */\n  var settings = {\n    list: [],\n    fontFamily:\n      '\"Trebuchet MS\", \"Heiti TC\", \"微軟正黑體\", ' +\n      '\"Arial Unicode MS\", \"Droid Fallback Sans\", sans-serif',\n    fontWeight: 'normal',\n    color: 'random-dark',\n    minSize: 0, // 0 to disable\n    weightFactor: 1,\n    clearCanvas: true,\n    backgroundColor: '#fff', // opaque white = rgba(255, 255, 255, 1)\n\n    gridSize: 8,\n    drawOutOfBound: false,\n    shrinkToFit: false,\n    origin: null,\n\n    drawMask: false,\n    maskColor: 'rgba(255,0,0,0.3)',\n    maskGapWidth: 0.3,\n\n    layoutAnimation: true,\n\n    wait: 0,\n    abortThreshold: 0, // disabled\n    abort: function noop() {},\n\n    minRotation: -Math.PI / 2,\n    maxRotation: Math.PI / 2,\n    rotationStep: 0.1,\n\n    shuffle: true,\n    rotateRatio: 0.1,\n\n    shape: 'circle',\n    ellipticity: 0.65,\n\n    classes: null,\n\n    hover: null,\n    click: null\n  };\n\n  if (options) {\n    for (var key in options) {\n      if (key in settings) {\n        settings[key] = options[key];\n      }\n    }\n  }\n\n  /* Convert weightFactor into a function */\n  if (typeof settings.weightFactor !== 'function') {\n    var factor = settings.weightFactor;\n    settings.weightFactor = function weightFactor(pt) {\n      return pt * factor; // in px\n    };\n  }\n\n  /* Convert shape into a function */\n  if (typeof settings.shape !== 'function') {\n    switch (settings.shape) {\n      case 'circle':\n      /* falls through */\n      default:\n        // 'circle' is the default and a shortcut in the code loop.\n        settings.shape = 'circle';\n        break;\n\n      case 'cardioid':\n        settings.shape = function shapeCardioid(theta) {\n          return 1 - Math.sin(theta);\n        };\n        break;\n\n      /*\n        To work out an X-gon, one has to calculate \"m\",\n        where 1/(cos(2*PI/X)+m*sin(2*PI/X)) = 1/(cos(0)+m*sin(0))\n        http://www.wolframalpha.com/input/?i=1%2F%28cos%282*PI%2FX%29%2Bm*sin%28\n        2*PI%2FX%29%29+%3D+1%2F%28cos%280%29%2Bm*sin%280%29%29\n        Copy the solution into polar equation r = 1/(cos(t') + m*sin(t'))\n        where t' equals to mod(t, 2PI/X);\n        */\n\n      case 'diamond':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n        // %28t%2C+PI%2F2%29%29%2Bsin%28mod+%28t%2C+PI%2F2%29%29%29%2C+t+%3D\n        // +0+..+2*PI\n        settings.shape = function shapeSquare(theta) {\n          var thetaPrime = theta % ((2 * Math.PI) / 4);\n          return 1 / (Math.cos(thetaPrime) + Math.sin(thetaPrime));\n        };\n        break;\n\n      case 'square':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+min(1%2Fabs(cos(t\n        // )),1%2Fabs(sin(t)))),+t+%3D+0+..+2*PI\n        settings.shape = function shapeSquare(theta) {\n          return Math.min(\n            1 / Math.abs(Math.cos(theta)),\n            1 / Math.abs(Math.sin(theta))\n          );\n        };\n        break;\n\n      case 'triangle-forward':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n        // %28t%2C+2*PI%2F3%29%29%2Bsqrt%283%29sin%28mod+%28t%2C+2*PI%2F3%29\n        // %29%29%2C+t+%3D+0+..+2*PI\n        settings.shape = function shapeTriangle(theta) {\n          var thetaPrime = theta % ((2 * Math.PI) / 3);\n          return (\n            1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime))\n          );\n        };\n        break;\n\n      case 'triangle':\n      case 'triangle-upright':\n        settings.shape = function shapeTriangle(theta) {\n          var thetaPrime = (theta + (Math.PI * 3) / 2) % ((2 * Math.PI) / 3);\n          return (\n            1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime))\n          );\n        };\n        break;\n\n      case 'pentagon':\n        settings.shape = function shapePentagon(theta) {\n          var thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 5);\n          return 1 / (Math.cos(thetaPrime) + 0.726543 * Math.sin(thetaPrime));\n        };\n        break;\n\n      case 'star':\n        settings.shape = function shapeStar(theta) {\n          var thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 10);\n          if (\n            ((theta + 0.955) % ((2 * Math.PI) / 5)) - (2 * Math.PI) / 10 >=\n            0\n          ) {\n            return (\n              1 /\n              (Math.cos((2 * Math.PI) / 10 - thetaPrime) +\n                3.07768 * Math.sin((2 * Math.PI) / 10 - thetaPrime))\n            );\n          } else {\n            return 1 / (Math.cos(thetaPrime) + 3.07768 * Math.sin(thetaPrime));\n          }\n        };\n        break;\n    }\n  }\n\n  /* Make sure gridSize is a whole number and is not smaller than 4px */\n  settings.gridSize = Math.max(Math.floor(settings.gridSize), 4);\n\n  /* shorthand */\n  var g = settings.gridSize;\n  var maskRectWidth = g - settings.maskGapWidth;\n\n  /* normalize rotation settings */\n  var rotationRange = Math.abs(settings.maxRotation - settings.minRotation);\n  var minRotation = Math.min(settings.maxRotation, settings.minRotation);\n  var rotationStep = settings.rotationStep;\n\n  /* information/object available to all functions, set when start() */\n  var grid, // 2d array containing filling information\n    ngx,\n    ngy, // width and height of the grid\n    center, // position of the center of the cloud\n    maxRadius;\n\n  /* timestamp for measuring each putWord() action */\n  var escapeTime;\n\n  /* function for getting the color of the text */\n  var getTextColor;\n  function randomHslColor(min, max) {\n    return (\n      'hsl(' +\n      (Math.random() * 360).toFixed() +\n      ',' +\n      (Math.random() * 30 + 70).toFixed() +\n      '%,' +\n      (Math.random() * (max - min) + min).toFixed() +\n      '%)'\n    );\n  }\n  switch (settings.color) {\n    case 'random-dark':\n      getTextColor = function getRandomDarkColor() {\n        return randomHslColor(10, 50);\n      };\n      break;\n\n    case 'random-light':\n      getTextColor = function getRandomLightColor() {\n        return randomHslColor(50, 90);\n      };\n      break;\n\n    default:\n      if (typeof settings.color === 'function') {\n        getTextColor = settings.color;\n      }\n      break;\n  }\n\n  /* function for getting the font-weight of the text */\n  var getTextFontWeight;\n  if (typeof settings.fontWeight === 'function') {\n    getTextFontWeight = settings.fontWeight;\n  }\n\n  /* function for getting the classes of the text */\n  var getTextClasses = null;\n  if (typeof settings.classes === 'function') {\n    getTextClasses = settings.classes;\n  }\n\n  /* Interactive */\n  var interactive = false;\n  var infoGrid = [];\n  var hovered;\n\n  var getInfoGridFromMouseTouchEvent = function getInfoGridFromMouseTouchEvent(\n    evt\n  ) {\n    var canvas = evt.currentTarget;\n    var rect = canvas.getBoundingClientRect();\n    var clientX;\n    var clientY;\n    /** Detect if touches are available */\n    if (evt.touches) {\n      clientX = evt.touches[0].clientX;\n      clientY = evt.touches[0].clientY;\n    } else {\n      clientX = evt.clientX;\n      clientY = evt.clientY;\n    }\n    var eventX = clientX - rect.left;\n    var eventY = clientY - rect.top;\n\n    var x = Math.floor((eventX * (canvas.width / rect.width || 1)) / g);\n    var y = Math.floor((eventY * (canvas.height / rect.height || 1)) / g);\n\n    if (!infoGrid[x]) {\n      return null\n    }\n\n    return infoGrid[x][y];\n  };\n\n  var wordcloudhover = function wordcloudhover(evt) {\n    var info = getInfoGridFromMouseTouchEvent(evt);\n\n    if (hovered === info) {\n      return;\n    }\n\n    hovered = info;\n    if (!info) {\n      settings.hover(undefined, undefined, evt);\n\n      return;\n    }\n\n    settings.hover(info.item, info.dimension, evt);\n  };\n\n  var wordcloudclick = function wordcloudclick(evt) {\n    var info = getInfoGridFromMouseTouchEvent(evt);\n    if (!info) {\n      return;\n    }\n\n    settings.click(info.item, info.dimension, evt);\n    evt.preventDefault();\n  };\n\n  /* Get points on the grid for a given radius away from the center */\n  var pointsAtRadius = [];\n  var getPointsAtRadius = function getPointsAtRadius(radius) {\n    if (pointsAtRadius[radius]) {\n      return pointsAtRadius[radius];\n    }\n\n    // Look for these number of points on each radius\n    var T = radius * 8;\n\n    // Getting all the points at this radius\n    var t = T;\n    var points = [];\n\n    if (radius === 0) {\n      points.push([center[0], center[1], 0]);\n    }\n\n    while (t--) {\n      // distort the radius to put the cloud in shape\n      var rx = 1;\n      if (settings.shape !== 'circle') {\n        rx = settings.shape((t / T) * 2 * Math.PI); // 0 to 1\n      }\n\n      // Push [x, y, t]; t is used solely for getTextColor()\n      points.push([\n        center[0] + radius * rx * Math.cos((-t / T) * 2 * Math.PI),\n        center[1] +\n          radius * rx * Math.sin((-t / T) * 2 * Math.PI) * settings.ellipticity,\n        (t / T) * 2 * Math.PI\n      ]);\n    }\n\n    pointsAtRadius[radius] = points;\n    return points;\n  };\n\n  /* Return true if we had spent too much time */\n  var exceedTime = function exceedTime() {\n    return (\n      settings.abortThreshold > 0 &&\n      new Date().getTime() - escapeTime > settings.abortThreshold\n    );\n  };\n\n  /* Get the deg of rotation according to settings, and luck. */\n  var getRotateDeg = function getRotateDeg() {\n    if (settings.rotateRatio === 0) {\n      return 0;\n    }\n\n    if (Math.random() > settings.rotateRatio) {\n      return 0;\n    }\n\n    if (rotationRange === 0) {\n      return minRotation;\n    }\n\n    return minRotation + Math.round(Math.random() * rotationRange / rotationStep) * rotationStep;\n  };\n\n  var getTextInfo = function getTextInfo(\n    word,\n    weight,\n    rotateDeg,\n    extraDataArray\n  ) {\n    // calculate the acutal font size\n    // fontSize === 0 means weightFactor function wants the text skipped,\n    // and size < minSize means we cannot draw the text.\n    var debug = false;\n    var fontSize = settings.weightFactor(weight);\n    if (fontSize <= settings.minSize) {\n      return false;\n    }\n\n    // Scale factor here is to make sure fillText is not limited by\n    // the minium font size set by browser.\n    // It will always be 1 or 2n.\n    var mu = 1;\n    if (fontSize < minFontSize) {\n      mu = (function calculateScaleFactor() {\n        var mu = 2;\n        while (mu * fontSize < minFontSize) {\n          mu += 2;\n        }\n        return mu;\n      })();\n    }\n\n    // Get fontWeight that will be used to set fctx.font\n    var fontWeight;\n    if (getTextFontWeight) {\n      fontWeight = getTextFontWeight(word, weight, fontSize, extraDataArray);\n    } else {\n      fontWeight = settings.fontWeight;\n    }\n\n    var fcanvas = document.createElement('canvas');\n    var fctx = fcanvas.getContext('2d', { willReadFrequently: true });\n\n    fctx.font =\n      fontWeight +\n      ' ' +\n      (fontSize * mu).toString(10) +\n      'px ' +\n      settings.fontFamily;\n\n    // Estimate the dimension of the text with measureText().\n    var fw = fctx.measureText(word).width / mu;\n    var fh =\n      Math.max(\n        fontSize * mu,\n        fctx.measureText('m').width,\n        fctx.measureText('\\uFF37').width\n      ) / mu;\n\n    // Create a boundary box that is larger than our estimates,\n    // so text don't get cut of (it sill might)\n    var boxWidth = fw + fh * 2;\n    var boxHeight = fh * 3;\n    var fgw = Math.ceil(boxWidth / g);\n    var fgh = Math.ceil(boxHeight / g);\n    boxWidth = fgw * g;\n    boxHeight = fgh * g;\n\n    // Calculate the proper offsets to make the text centered at\n    // the preferred position.\n\n    // This is simply half of the width.\n    var fillTextOffsetX = -fw / 2;\n    // Instead of moving the box to the exact middle of the preferred\n    // position, for Y-offset we move 0.4 instead, so Latin alphabets look\n    // vertical centered.\n    var fillTextOffsetY = -fh * 0.4;\n\n    // Calculate the actual dimension of the canvas, considering the rotation.\n    var cgh = Math.ceil(\n      (boxWidth * Math.abs(Math.sin(rotateDeg)) +\n        boxHeight * Math.abs(Math.cos(rotateDeg))) /\n        g\n    );\n    var cgw = Math.ceil(\n      (boxWidth * Math.abs(Math.cos(rotateDeg)) +\n        boxHeight * Math.abs(Math.sin(rotateDeg))) /\n        g\n    );\n    var width = cgw * g;\n    var height = cgh * g;\n\n    fcanvas.setAttribute('width', width);\n    fcanvas.setAttribute('height', height);\n\n    if (debug) {\n      // Attach fcanvas to the DOM\n      document.body.appendChild(fcanvas);\n      // Save it's state so that we could restore and draw the grid correctly.\n      fctx.save();\n    }\n\n    // Scale the canvas with |mu|.\n    fctx.scale(1 / mu, 1 / mu);\n    fctx.translate((width * mu) / 2, (height * mu) / 2);\n    fctx.rotate(-rotateDeg);\n\n    // Once the width/height is set, ctx info will be reset.\n    // Set it again here.\n    fctx.font =\n      fontWeight +\n      ' ' +\n      (fontSize * mu).toString(10) +\n      'px ' +\n      settings.fontFamily;\n\n    // Fill the text into the fcanvas.\n    // XXX: We cannot because textBaseline = 'top' here because\n    // Firefox and Chrome uses different default line-height for canvas.\n    // Please read https://bugzil.la/737852#c6.\n    // Here, we use textBaseline = 'middle' and draw the text at exactly\n    // 0.5 * fontSize lower.\n    fctx.fillStyle = '#000';\n    fctx.textBaseline = 'middle';\n    fctx.fillText(\n      word,\n      fillTextOffsetX * mu,\n      (fillTextOffsetY + fontSize * 0.5) * mu\n    );\n\n    // Get the pixels of the text\n    var imageData = fctx.getImageData(0, 0, width, height).data;\n\n    if (exceedTime()) {\n      return false;\n    }\n\n    if (debug) {\n      // Draw the box of the original estimation\n      fctx.strokeRect(fillTextOffsetX * mu, fillTextOffsetY, fw * mu, fh * mu);\n      fctx.restore();\n    }\n\n    // Read the pixels and save the information to the occupied array\n    var occupied = [];\n    var gx = cgw;\n    var gy, x, y;\n    var bounds = [cgh / 2, cgw / 2, cgh / 2, cgw / 2];\n    while (gx--) {\n      gy = cgh;\n      while (gy--) {\n        y = g;\n        /* eslint no-labels: ['error', { 'allowLoop': true }] */\n        singleGridLoop: while (y--) {\n          x = g;\n          while (x--) {\n            if (imageData[((gy * g + y) * width + (gx * g + x)) * 4 + 3]) {\n              occupied.push([gx, gy]);\n\n              if (gx < bounds[3]) {\n                bounds[3] = gx;\n              }\n              if (gx > bounds[1]) {\n                bounds[1] = gx;\n              }\n              if (gy < bounds[0]) {\n                bounds[0] = gy;\n              }\n              if (gy > bounds[2]) {\n                bounds[2] = gy;\n              }\n\n              if (debug) {\n                fctx.fillStyle = 'rgba(255, 0, 0, 0.5)';\n                fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n              }\n              break singleGridLoop;\n            }\n          }\n        }\n        if (debug) {\n          fctx.fillStyle = 'rgba(0, 0, 255, 0.5)';\n          fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n        }\n      }\n    }\n\n    if (debug) {\n      fctx.fillStyle = 'rgba(0, 255, 0, 0.5)';\n      fctx.fillRect(\n        bounds[3] * g,\n        bounds[0] * g,\n        (bounds[1] - bounds[3] + 1) * g,\n        (bounds[2] - bounds[0] + 1) * g\n      );\n    }\n\n    // Return information needed to create the text on the real canvas\n    return {\n      mu: mu,\n      occupied: occupied,\n      bounds: bounds,\n      gw: cgw,\n      gh: cgh,\n      fillTextOffsetX: fillTextOffsetX,\n      fillTextOffsetY: fillTextOffsetY,\n      fillTextWidth: fw,\n      fillTextHeight: fh,\n      fontSize: fontSize\n    };\n  };\n\n  /* Determine if there is room available in the given dimension */\n  var canFitText = function canFitText(gx, gy, gw, gh, occupied) {\n    // Go through the occupied points,\n    // return false if the space is not available.\n    var i = occupied.length;\n    while (i--) {\n      var px = gx + occupied[i][0];\n      var py = gy + occupied[i][1];\n\n      if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n        if (!settings.drawOutOfBound) {\n          return false;\n        }\n        continue;\n      }\n\n      if (!grid[px][py]) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /* Actually draw the text on the grid */\n  var drawText = function drawText(\n    gx,\n    gy,\n    info,\n    word,\n    weight,\n    distance,\n    theta,\n    rotateDeg,\n    attributes,\n    extraDataArray\n  ) {\n    var fontSize = info.fontSize;\n    var color;\n    if (getTextColor) {\n      color = getTextColor(\n        word,\n        weight,\n        fontSize,\n        distance,\n        theta,\n        extraDataArray\n      );\n    } else {\n      color = settings.color;\n    }\n\n    // get fontWeight that will be used to set ctx.font and font style rule\n    var fontWeight;\n    if (getTextFontWeight) {\n      fontWeight = getTextFontWeight(word, weight, fontSize, extraDataArray);\n    } else {\n      fontWeight = settings.fontWeight;\n    }\n\n    var classes;\n    if (getTextClasses) {\n      classes = getTextClasses(word, weight, fontSize, extraDataArray);\n    } else {\n      classes = settings.classes;\n    }\n\n    elements.forEach(function (el) {\n      if (el.getContext) {\n        var ctx = el.getContext('2d');\n        var mu = info.mu;\n\n        // Save the current state before messing it\n        ctx.save();\n        ctx.scale(1 / mu, 1 / mu);\n\n        ctx.font =\n          fontWeight +\n          ' ' +\n          (fontSize * mu).toString(10) +\n          'px ' +\n          settings.fontFamily;\n        ctx.fillStyle = color;\n\n        // Translate the canvas position to the origin coordinate of where\n        // the text should be put.\n        ctx.translate((gx + info.gw / 2) * g * mu, (gy + info.gh / 2) * g * mu);\n\n        if (rotateDeg !== 0) {\n          ctx.rotate(-rotateDeg);\n        }\n\n        // Finally, fill the text.\n\n        // XXX: We cannot because textBaseline = 'top' here because\n        // Firefox and Chrome uses different default line-height for canvas.\n        // Please read https://bugzil.la/737852#c6.\n        // Here, we use textBaseline = 'middle' and draw the text at exactly\n        // 0.5 * fontSize lower.\n        ctx.textBaseline = 'middle';\n        ctx.fillText(\n          word,\n          info.fillTextOffsetX * mu,\n          (info.fillTextOffsetY + fontSize * 0.5) * mu\n        );\n\n        // The below box is always matches how <span>s are positioned\n        /* ctx.strokeRect(info.fillTextOffsetX, info.fillTextOffsetY,\n            info.fillTextWidth, info.fillTextHeight); */\n\n        // Restore the state.\n        ctx.restore();\n      } else {\n        // drawText on DIV element\n        var span = document.createElement('span');\n        var transformRule = '';\n        transformRule = 'rotate(' + (-rotateDeg / Math.PI) * 180 + 'deg) ';\n        if (info.mu !== 1) {\n          transformRule +=\n            'translateX(-' +\n            info.fillTextWidth / 4 +\n            'px) ' +\n            'scale(' +\n            1 / info.mu +\n            ')';\n        }\n        var styleRules = {\n          position: 'absolute',\n          display: 'block',\n          font:\n            fontWeight + ' ' + fontSize * info.mu + 'px ' + settings.fontFamily,\n          left: (gx + info.gw / 2) * g + info.fillTextOffsetX + 'px',\n          top: (gy + info.gh / 2) * g + info.fillTextOffsetY + 'px',\n          width: info.fillTextWidth + 'px',\n          height: info.fillTextHeight + 'px',\n          lineHeight: fontSize + 'px',\n          whiteSpace: 'nowrap',\n          transform: transformRule,\n          webkitTransform: transformRule,\n          msTransform: transformRule,\n          transformOrigin: '50% 40%',\n          webkitTransformOrigin: '50% 40%',\n          msTransformOrigin: '50% 40%'\n        };\n        if (color) {\n          styleRules.color = color;\n        }\n        span.textContent = word;\n        for (var cssProp in styleRules) {\n          span.style[cssProp] = styleRules[cssProp];\n        }\n        if (attributes) {\n          for (var attribute in attributes) {\n            span.setAttribute(attribute, attributes[attribute]);\n          }\n        }\n        if (classes) {\n          span.className += classes;\n        }\n        el.appendChild(span);\n      }\n    });\n  };\n\n  /* Help function to updateGrid */\n  var fillGridAt = function fillGridAt(x, y, drawMask, dimension, item) {\n    if (x >= ngx || y >= ngy || x < 0 || y < 0) {\n      return;\n    }\n\n    grid[x][y] = false;\n\n    if (drawMask) {\n      var ctx = elements[0].getContext('2d');\n      ctx.fillRect(x * g, y * g, maskRectWidth, maskRectWidth);\n    }\n\n    if (interactive) {\n      infoGrid[x][y] = { item: item, dimension: dimension };\n    }\n  };\n\n  /* Update the filling information of the given space with occupied points.\n       Draw the mask on the canvas if necessary. */\n  var updateGrid = function updateGrid(gx, gy, gw, gh, info, item) {\n    var occupied = info.occupied;\n    var drawMask = settings.drawMask;\n    var ctx;\n    if (drawMask) {\n      ctx = elements[0].getContext('2d');\n      ctx.save();\n      ctx.fillStyle = settings.maskColor;\n    }\n\n    var dimension;\n    if (interactive) {\n      var bounds = info.bounds;\n      dimension = {\n        x: (gx + bounds[3]) * g,\n        y: (gy + bounds[0]) * g,\n        w: (bounds[1] - bounds[3] + 1) * g,\n        h: (bounds[2] - bounds[0] + 1) * g\n      };\n    }\n\n    var i = occupied.length;\n    while (i--) {\n      var px = gx + occupied[i][0];\n      var py = gy + occupied[i][1];\n\n      if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n        continue;\n      }\n\n      fillGridAt(px, py, drawMask, dimension, item);\n    }\n\n    if (drawMask) {\n      ctx.restore();\n    }\n  };\n\n  /* putWord() processes each item on the list,\n       calculate it's size and determine it's position, and actually\n       put it on the canvas. */\n  var putWord = function putWord(item, loopIndex) {\n    if (loopIndex > 20) {\n      return null;\n    }\n\n    var word, weight, attributes;\n    if (Array.isArray(item)) {\n      word = item[0];\n      weight = item[1];\n    } else {\n      word = item.word;\n      weight = item.weight;\n      attributes = item.attributes;\n    }\n    var rotateDeg = getRotateDeg();\n\n    var extraDataArray = getItemExtraData(item);\n\n    // get info needed to put the text onto the canvas\n    var info = getTextInfo(word, weight, rotateDeg, extraDataArray);\n\n    // not getting the info means we shouldn't be drawing this one.\n    if (!info) {\n      return false;\n    }\n\n    if (exceedTime()) {\n      return false;\n    }\n\n    // If drawOutOfBound is set to false,\n    // skip the loop if we have already know the bounding box of\n    // word is larger than the canvas.\n    if (!settings.drawOutOfBound && !settings.shrinkToFit) {\n      var bounds = info.bounds;\n      if (bounds[1] - bounds[3] + 1 > ngx || bounds[2] - bounds[0] + 1 > ngy) {\n        return false;\n      }\n    }\n\n    // Determine the position to put the text by\n    // start looking for the nearest points\n    var r = maxRadius + 1;\n\n    var tryToPutWordAtPoint = function (gxy) {\n      var gx = Math.floor(gxy[0] - info.gw / 2);\n      var gy = Math.floor(gxy[1] - info.gh / 2);\n      var gw = info.gw;\n      var gh = info.gh;\n\n      // If we cannot fit the text at this position, return false\n      // and go to the next position.\n      if (!canFitText(gx, gy, gw, gh, info.occupied)) {\n        return false;\n      }\n\n      // Actually put the text on the canvas\n      drawText(\n        gx,\n        gy,\n        info,\n        word,\n        weight,\n        maxRadius - r,\n        gxy[2],\n        rotateDeg,\n        attributes,\n        extraDataArray\n      );\n\n      // Mark the spaces on the grid as filled\n      updateGrid(gx, gy, gw, gh, info, item);\n\n      return {\n        gx: gx,\n        gy: gy,\n        rot: rotateDeg,\n        info: info\n      };\n    };\n\n    while (r--) {\n      var points = getPointsAtRadius(maxRadius - r);\n\n      if (settings.shuffle) {\n        points = [].concat(points);\n        shuffleArray(points);\n      }\n\n      // Try to fit the words by looking at each point.\n      // array.some() will stop and return true\n      // when putWordAtPoint() returns true.\n      for (var i = 0; i < points.length; i++) {\n        var res = tryToPutWordAtPoint(points[i]);\n        if (res) {\n          return res;\n        }\n      }\n\n      // var drawn = points.some(tryToPutWordAtPoint);\n      // if (drawn) {\n      //   // leave putWord() and return true\n      //   return true;\n      // }\n    }\n\n    if (settings.shrinkToFit) {\n      if (Array.isArray(item)) {\n        item[1] = (item[1] * 3) / 4;\n      } else {\n        item.weight = (item.weight * 3) / 4;\n      }\n      return putWord(item, loopIndex + 1);\n    }\n\n    // we tried all distances but text won't fit, return null\n    return null;\n  };\n\n  /* Send DOM event to all elements. Will stop sending event and return\n       if the previous one is canceled (for cancelable events). */\n  var sendEvent = function sendEvent(type, cancelable, details) {\n    if (cancelable) {\n      return !elements.some(function (el) {\n        var event = new CustomEvent(type, {\n          detail: details || {}\n        });\n        return !el.dispatchEvent(event);\n      }, this);\n    } else {\n      elements.forEach(function (el) {\n        var event = new CustomEvent(type, {\n          detail: details || {}\n        });\n        el.dispatchEvent(event);\n      }, this);\n    }\n  };\n\n  /* Start drawing on a canvas */\n  var start = function start() {\n    // For dimensions, clearCanvas etc.,\n    // we only care about the first element.\n    var canvas = elements[0];\n\n    if (canvas.getContext) {\n      ngx = Math.ceil(canvas.width / g);\n      ngy = Math.ceil(canvas.height / g);\n    } else {\n      var rect = canvas.getBoundingClientRect();\n      ngx = Math.ceil(rect.width / g);\n      ngy = Math.ceil(rect.height / g);\n    }\n\n    // Sending a wordcloudstart event which cause the previous loop to stop.\n    // Do nothing if the event is canceled.\n    if (!sendEvent('wordcloudstart', true)) {\n      return;\n    }\n\n    // Determine the center of the word cloud\n    center = settings.origin\n      ? [settings.origin[0] / g, settings.origin[1] / g]\n      : [ngx / 2, ngy / 2];\n\n    // Maxium radius to look for space\n    maxRadius = Math.floor(Math.sqrt(ngx * ngx + ngy * ngy));\n\n    /* Clear the canvas only if the clearCanvas is set,\n         if not, update the grid to the current canvas state */\n    grid = [];\n\n    var gx, gy, i;\n    if (!canvas.getContext || settings.clearCanvas) {\n      elements.forEach(function (el) {\n        if (el.getContext) {\n          var ctx = el.getContext('2d');\n          ctx.fillStyle = settings.backgroundColor;\n          ctx.clearRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n          ctx.fillRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n        } else {\n          el.textContent = '';\n          el.style.backgroundColor = settings.backgroundColor;\n          el.style.position = 'relative';\n        }\n      });\n\n      /* fill the grid with empty state */\n      gx = ngx;\n      while (gx--) {\n        grid[gx] = [];\n        gy = ngy;\n        while (gy--) {\n          grid[gx][gy] = true;\n        }\n      }\n    } else {\n      /* Determine bgPixel by creating\n           another canvas and fill the specified background color. */\n      var bctx = document.createElement('canvas').getContext('2d');\n\n      bctx.fillStyle = settings.backgroundColor;\n      bctx.fillRect(0, 0, 1, 1);\n      var bgPixel = bctx.getImageData(0, 0, 1, 1).data;\n\n      /* Read back the pixels of the canvas we got to tell which part of the\n           canvas is empty.\n           (no clearCanvas only works with a canvas, not divs) */\n      var imageData = canvas\n        .getContext('2d')\n        .getImageData(0, 0, ngx * g, ngy * g).data;\n\n      gx = ngx;\n      var x, y;\n      while (gx--) {\n        grid[gx] = [];\n        gy = ngy;\n        while (gy--) {\n          y = g;\n          /* eslint no-labels: ['error', { 'allowLoop': true }] */\n          singleGridLoop: while (y--) {\n            x = g;\n            while (x--) {\n              i = 4;\n              while (i--) {\n                if (\n                  imageData[((gy * g + y) * ngx * g + (gx * g + x)) * 4 + i] !==\n                  bgPixel[i]\n                ) {\n                  grid[gx][gy] = false;\n                  break singleGridLoop;\n                }\n              }\n            }\n          }\n          if (grid[gx][gy] !== false) {\n            grid[gx][gy] = true;\n          }\n        }\n      }\n\n      imageData = bctx = bgPixel = undefined;\n    }\n\n    // fill the infoGrid with empty state if we need it\n    if (settings.hover || settings.click) {\n      interactive = true;\n\n      /* fill the grid with empty state */\n      gx = ngx + 1;\n      while (gx--) {\n        infoGrid[gx] = [];\n      }\n\n      if (settings.hover) {\n        canvas.addEventListener('mousemove', wordcloudhover);\n      }\n\n      if (settings.click) {\n        canvas.addEventListener('click', wordcloudclick);\n        canvas.addEventListener('touchstart', wordcloudclick);\n        canvas.addEventListener('touchend', function (e) {\n          e.preventDefault();\n        });\n        canvas.style.webkitTapHighlightColor = 'rgba(0, 0, 0, 0)';\n      }\n\n      canvas.addEventListener('wordcloudstart', function stopInteraction() {\n        canvas.removeEventListener('wordcloudstart', stopInteraction);\n\n        canvas.removeEventListener('mousemove', wordcloudhover);\n        canvas.removeEventListener('click', wordcloudclick);\n        hovered = undefined;\n      });\n    }\n\n    i = 0;\n    var loopingFunction, stoppingFunction;\n    var layouting = true;\n    if (!settings.layoutAnimation) {\n      loopingFunction = function (cb) {\n        cb();\n      };\n      stoppingFunction = function () {\n        layouting = false;\n      };\n    } else if (settings.wait !== 0) {\n      loopingFunction = window.setTimeout;\n      stoppingFunction = window.clearTimeout;\n    } else {\n      loopingFunction = window.setImmediate;\n      stoppingFunction = window.clearImmediate;\n    }\n\n    var addEventListener = function addEventListener(type, listener) {\n      elements.forEach(function (el) {\n        el.addEventListener(type, listener);\n      }, this);\n    };\n\n    var removeEventListener = function removeEventListener(type, listener) {\n      elements.forEach(function (el) {\n        el.removeEventListener(type, listener);\n      }, this);\n    };\n\n    var anotherWordCloudStart = function anotherWordCloudStart() {\n      removeEventListener('wordcloudstart', anotherWordCloudStart);\n      stoppingFunction(timer[timerId]);\n    };\n\n    addEventListener('wordcloudstart', anotherWordCloudStart);\n\n    // At least wait the following code before call the first iteration.\n    timer[timerId] = (settings.layoutAnimation ? loopingFunction : setTimeout)(\n      function loop() {\n        if (!layouting) {\n          return;\n        }\n        if (i >= settings.list.length) {\n          stoppingFunction(timer[timerId]);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n          delete timer[timerId];\n          return;\n        }\n        escapeTime = new Date().getTime();\n        var drawn = putWord(settings.list[i], 0);\n        var canceled = !sendEvent('wordclouddrawn', true, {\n          item: settings.list[i],\n          drawn: drawn\n        });\n        if (exceedTime() || canceled) {\n          stoppingFunction(timer[timerId]);\n          settings.abort();\n          sendEvent('wordcloudabort', false);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n          return;\n        }\n        i++;\n        timer[timerId] = loopingFunction(loop, settings.wait);\n      },\n      settings.wait\n    );\n  };\n\n  // All set, start the drawing\n  start();\n};\n\nWordCloud.isSupported = isSupported;\nWordCloud.minFontSize = minFontSize;\n\nexport default WordCloud;\n", "import * as echarts from 'echarts/lib/echarts';\n\nimport './WordCloudSeries';\nimport './WordCloudView';\n\nimport wordCloudLayoutHelper from './layout';\n\nif (!wordCloudLayoutHelper.isSupported) {\n  throw new Error('Sorry your browser not support wordCloud');\n}\n\n// https://github.com/timdream/wordcloud2.js/blob/c236bee60436e048949f9becc4f0f67bd832dc5c/index.js#L233\nfunction updateCanvasMask(maskCanvas) {\n  var ctx = maskCanvas.getContext('2d');\n  var imageData = ctx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);\n  var newImageData = ctx.createImageData(imageData);\n\n  var toneSum = 0;\n  var toneCnt = 0;\n  for (var i = 0; i < imageData.data.length; i += 4) {\n    var alpha = imageData.data[i + 3];\n    if (alpha > 128) {\n      var tone =\n        imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2];\n      toneSum += tone;\n      ++toneCnt;\n    }\n  }\n  var threshold = toneSum / toneCnt;\n\n  for (var i = 0; i < imageData.data.length; i += 4) {\n    var tone =\n      imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2];\n    var alpha = imageData.data[i + 3];\n\n    if (alpha < 128 || tone > threshold) {\n      // Area not to draw\n      newImageData.data[i] = 0;\n      newImageData.data[i + 1] = 0;\n      newImageData.data[i + 2] = 0;\n      newImageData.data[i + 3] = 0;\n    } else {\n      // Area to draw\n      // The color must be same with backgroundColor\n      newImageData.data[i] = 255;\n      newImageData.data[i + 1] = 255;\n      newImageData.data[i + 2] = 255;\n      newImageData.data[i + 3] = 255;\n    }\n  }\n\n  ctx.putImageData(newImageData, 0, 0);\n}\n\necharts.registerLayout(function (ecModel, api) {\n  ecModel.eachSeriesByType('wordCloud', function (seriesModel) {\n    var gridRect = echarts.helper.getLayoutRect(\n      seriesModel.getBoxLayoutParams(),\n      {\n        width: api.getWidth(),\n        height: api.getHeight()\n      }\n    );\n\n    var keepAspect = seriesModel.get('keepAspect');\n    var maskImage = seriesModel.get('maskImage');\n    var ratio = maskImage ? maskImage.width / maskImage.height : 1;\n    keepAspect && adjustRectAspect(gridRect, ratio);\n\n    var data = seriesModel.getData();\n\n    var canvas = document.createElement('canvas');\n    canvas.width = gridRect.width;\n    canvas.height = gridRect.height;\n\n    var ctx = canvas.getContext('2d');\n    if (maskImage) {\n      try {\n        ctx.drawImage(maskImage, 0, 0, canvas.width, canvas.height);\n        updateCanvasMask(canvas);\n      } catch (e) {\n        console.error('Invalid mask image');\n        console.error(e.toString());\n      }\n    }\n\n    var sizeRange = seriesModel.get('sizeRange');\n    var rotationRange = seriesModel.get('rotationRange');\n    var valueExtent = data.getDataExtent('value');\n\n    var DEGREE_TO_RAD = Math.PI / 180;\n    var gridSize = seriesModel.get('gridSize');\n    wordCloudLayoutHelper(canvas, {\n      list: data\n        .mapArray('value', function (value, idx) {\n          var itemModel = data.getItemModel(idx);\n          return [\n            data.getName(idx),\n            itemModel.get('textStyle.fontSize', true) ||\n              echarts.number.linearMap(value, valueExtent, sizeRange),\n            idx\n          ];\n        })\n        .sort(function (a, b) {\n          // Sort from large to small in case there is no more room for more words\n          return b[1] - a[1];\n        }),\n      fontFamily:\n        seriesModel.get('textStyle.fontFamily') ||\n        seriesModel.get('emphasis.textStyle.fontFamily') ||\n        ecModel.get('textStyle.fontFamily'),\n      fontWeight:\n        seriesModel.get('textStyle.fontWeight') ||\n        seriesModel.get('emphasis.textStyle.fontWeight') ||\n        ecModel.get('textStyle.fontWeight'),\n\n      gridSize: gridSize,\n\n      ellipticity: gridRect.height / gridRect.width,\n\n      minRotation: rotationRange[0] * DEGREE_TO_RAD,\n      maxRotation: rotationRange[1] * DEGREE_TO_RAD,\n\n      clearCanvas: !maskImage,\n\n      rotateRatio: 1,\n\n      rotationStep: seriesModel.get('rotationStep') * DEGREE_TO_RAD,\n\n      drawOutOfBound: seriesModel.get('drawOutOfBound'),\n      shrinkToFit: seriesModel.get('shrinkToFit'),\n\n      layoutAnimation: seriesModel.get('layoutAnimation'),\n\n      shuffle: false,\n\n      shape: seriesModel.get('shape')\n    });\n\n    function onWordCloudDrawn(e) {\n      var item = e.detail.item;\n      if (e.detail.drawn && seriesModel.layoutInstance.ondraw) {\n        e.detail.drawn.gx += gridRect.x / gridSize;\n        e.detail.drawn.gy += gridRect.y / gridSize;\n        seriesModel.layoutInstance.ondraw(\n          item[0],\n          item[1],\n          item[2],\n          e.detail.drawn\n        );\n      }\n    }\n\n    canvas.addEventListener('wordclouddrawn', onWordCloudDrawn);\n\n    if (seriesModel.layoutInstance) {\n      // Dispose previous\n      seriesModel.layoutInstance.dispose();\n    }\n\n    seriesModel.layoutInstance = {\n      ondraw: null,\n\n      dispose: function () {\n        canvas.removeEventListener('wordclouddrawn', onWordCloudDrawn);\n        // Abort\n        canvas.addEventListener('wordclouddrawn', function (e) {\n          // Prevent default to cancle the event and stop the loop\n          e.preventDefault();\n        });\n      }\n    };\n  });\n});\n\necharts.registerPreprocessor(function (option) {\n  var series = (option || {}).series;\n  !echarts.util.isArray(series) && (series = series ? [series] : []);\n\n  var compats = ['shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\n\n  echarts.util.each(series, function (seriesItem) {\n    if (seriesItem && seriesItem.type === 'wordCloud') {\n      var textStyle = seriesItem.textStyle || {};\n\n      compatTextStyle(textStyle.normal);\n      compatTextStyle(textStyle.emphasis);\n    }\n  });\n\n  function compatTextStyle(textStyle) {\n    textStyle &&\n      echarts.util.each(compats, function (key) {\n        if (textStyle.hasOwnProperty(key)) {\n          textStyle['text' + echarts.format.capitalFirst(key)] = textStyle[key];\n        }\n      });\n  }\n});\n\nfunction adjustRectAspect(gridRect, aspect) {\n  // var outerWidth = gridRect.width + gridRect.x * 2;\n  // var outerHeight = gridRect.height + gridRect.y * 2;\n  var width = gridRect.width;\n  var height = gridRect.height;\n  if (width > height * aspect) {\n    gridRect.x += (width - height * aspect) / 2;\n    gridRect.width = height * aspect;\n  } else {\n    gridRect.y += (height - width / aspect) / 2;\n    gridRect.height = width / aspect;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,IAAI,CAACA,UAAgB,OAAgB,CAAC;AAgBtC,IAAI,kBAAkB;;;AC/Dd,kBAAkB;AAAA,EACxB,MAAM;AAAA,EAEN,uBAAuB;AAAA,EACvB,mBAAmB,SAAU,OAAO;AAClC,WAAO;AAAA,MACL,MAAM,MAAM,IAAI,OAAO;AAAA,IACzB;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,EAEhB,eAAe,WAAY;AACzB,QAAI,SAAS,KAAK;AAClB,WAAO,WAAW,KAAK,IAAI,KAAK,MAAM,OAAO,QAAQ,GAAG,CAAC;AAAA,EAC3D;AAAA,EAEA,gBAAgB,SAAU,QAAQ,SAAS;AACzC,QAAI,aAAqB,eAAO,iBAAiB,OAAO,MAAM;AAAA,MAC5D,iBAAiB,CAAC,OAAO;AAAA,IAC3B,CAAC;AACD,QAAI,OAAO,IAAY,mBAAK,YAAY,IAAI;AAC5C,SAAK,SAAS,OAAO,IAAI;AACzB,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,eAAe;AAAA,IACb,WAAW;AAAA;AAAA,IAGX,OAAO;AAAA,IACP,YAAY;AAAA,IAEZ,MAAM;AAAA,IAEN,KAAK;AAAA,IAEL,OAAO;AAAA,IAEP,QAAQ;AAAA,IAER,WAAW,CAAC,IAAI,EAAE;AAAA,IAElB,eAAe,CAAC,KAAK,EAAE;AAAA,IAEvB,cAAc;AAAA,IAEd,UAAU;AAAA,IAEV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IAEb,WAAW;AAAA,MACT,YAAY;AAAA,IACd;AAAA,EACF;AACF,CAAC;;;ACxDO,gBAAgB;AAAA,EACtB,MAAM;AAAA,EAEN,QAAQ,SAAU,aAAa,SAAS,KAAK;AAC3C,QAAI,QAAQ,KAAK;AACjB,UAAM,UAAU;AAEhB,QAAI,OAAO,YAAY,QAAQ;AAE/B,QAAI,WAAW,YAAY,IAAI,UAAU;AAEzC,gBAAY,eAAe,SAAS,SAAU,MAAM,MAAM,SAAS,OAAO;AACxE,UAAI,YAAY,KAAK,aAAa,OAAO;AACzC,UAAI,iBAAiB,UAAU,SAAS,WAAW;AAEnD,UAAI,SAAS,IAAY,gBAAQ,KAAK;AAAA,QACpC,OAAe,eAAO,gBAAgB,cAAc;AAAA,QACpD,QAAQ,IAAI,MAAM,KAAK;AAAA,QACvB,QAAQ,IAAI,MAAM,KAAK;AAAA,QACvB,IAAI,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,QACpC,IAAI,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,QACpC,UAAU,MAAM;AAAA,MAClB,CAAC;AACD,aAAO,SAAS;AAAA,QACd,GAAG,MAAM,KAAK;AAAA,QACd,GAAG,MAAM,KAAK,kBAAkB,OAAO;AAAA,QACvC;AAAA,QACA,eAAe;AAAA,QACf,MAAM,KAAK,cAAc,SAAS,OAAO,EAAE;AAAA,QAC3C,UAAU;AAAA,MACZ,CAAC;AAED,YAAM,IAAI,MAAM;AAEhB,WAAK,iBAAiB,SAAS,MAAM;AAErC,aAAO,YAAY,UAAU,EAAE,QAAgB,eAAO;AAAA,QACpD,UAAU,SAAS,CAAC,YAAY,WAAW,CAAC;AAAA,QAC5C;AAAA,UACE,OAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,YAAY,MAAM,EAAE,QAAgB,eAAO;AAAA,QAChD,UAAU,SAAS,CAAC,QAAQ,WAAW,CAAC;AAAA,QACxC;AAAA,UACE,OAAO;AAAA,QACT;AAAA,MACF;AAEA,MAAQ,eAAO;AAAA,QACb;AAAA,QACA,UAAU,IAAI,CAAC,YAAY,OAAO,CAAC;AAAA,QACnC,UAAU,IAAI,CAAC,YAAY,WAAW,CAAC;AAAA,MACzC;AAEA,aAAO,kBAAkB;AAAA,QACvB,UAAU,YAAY,IAAI,WAAW,IACjC,YAAY,IAAI,CAAC,kBAAkB,UAAU,CAAC,IAC9C;AAAA,QACJ,QAAQ,YAAY,IAAI,CAAC,kBAAkB,QAAQ,CAAC;AAAA,MACtD;AAEA,aAAO,uBAAuB;AAAA,IAChC;AAEA,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,QAAQ,WAAY;AAClB,SAAK,MAAM,UAAU;AAErB,SAAK,OAAO,eAAe,QAAQ;AAAA,EACrC;AAAA,EAEA,SAAS,WAAY;AACnB,SAAK,OAAO,eAAe,QAAQ;AAAA,EACrC;AACF,CAAC;;;ACpED,IAAI,CAAC,OAAO,cAAc;AACxB,SAAO,eAAgB,SAAS,oBAAoB;AAClD,WACE,OAAO,kBACP,OAAO,sBACP,OAAO,mBACP,OAAO,iBACN,SAAS,sBAAsB;AAC9B,UAAI,CAAC,OAAO,eAAe,CAAC,OAAO,kBAAkB;AACnD,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,CAAC,MAAS;AAC1B,UAAI,UAAU;AAKd,UAAI,iBAAiB,SAASC,gBAAe,UAAU;AACrD,YAAI,KAAK,UAAU;AACnB,kBAAU,KAAK,QAAQ;AACvB,eAAO,YAAY,UAAU,GAAG,SAAS,EAAE,GAAG,GAAG;AAEjD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL;AAAA,QACA,SAAS,sBAAsB,KAAK;AAGlC,cACE,OAAO,IAAI,SAAS,YACpB,IAAI,KAAK,OAAO,GAAG,QAAQ,MAAM,MAAM,SAEvC;AACA;AAAA,UACF;AAEA,cAAI,yBAAyB;AAE7B,cAAI,KAAK,SAAS,IAAI,KAAK,OAAO,QAAQ,MAAM,GAAG,EAAE;AACrD,cAAI,CAAC,UAAU,EAAE,GAAG;AAClB;AAAA,UACF;AAEA,oBAAU,EAAE,EAAE;AACd,oBAAU,EAAE,IAAI;AAAA,QAClB;AAAA,QACA;AAAA,MACF;AAGA,aAAO,iBAAiB,SAAS,iBAAiB,IAAI;AACpD,YAAI,CAAC,UAAU,EAAE,GAAG;AAClB;AAAA,QACF;AAEA,kBAAU,EAAE,IAAI;AAAA,MAClB;AAEA,aAAO;AAAA,IACT,EAAG;AAAA,IAEH,SAAS,qBAAqB,IAAI;AAChC,aAAO,WAAW,IAAI,CAAC;AAAA,IACzB;AAAA,EAEJ,EAAG;AACL;AAEA,IAAI,CAAC,OAAO,gBAAgB;AAC1B,SAAO,iBAAkB,SAAS,sBAAsB;AACtD,WACE,OAAO,oBACP,OAAO,wBACP,OAAO,qBACP,OAAO;AAAA;AAAA,IAGP,SAAS,uBAAuBC,QAAO;AACrC,aAAO,aAAaA,MAAK;AAAA,IAC3B;AAAA,EAEJ,EAAG;AACL;AAGA,IAAI,cAAe,SAASC,eAAc;AACxC,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,MAAI,CAAC,UAAU,CAAC,OAAO,YAAY;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,WAAW,IAAI;AAChC,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI,CAAC,IAAI,cAAc;AACrB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,IAAI,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,UAAU,MAAM;AACzB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,MAAM,UAAU,MAAM;AACzB,WAAO;AAAA,EACT;AAEA,SAAO;AACT,EAAG;AAIH,IAAI,cAAe,SAAS,iBAAiB;AAC3C,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AAEA,MAAI,MAAM,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAG1D,MAAI,OAAO;AAGX,MAAI,UAAU;AAEd,SAAO,MAAM;AACX,QAAI,OAAO,KAAK,SAAS,EAAE,IAAI;AAC/B,QACE,IAAI,YAAY,GAAQ,EAAE,UAAU,YACpC,IAAI,YAAY,GAAG,EAAE,UAAU,QAC/B;AACA,aAAO,OAAO;AAAA,IAChB;AAEA,eAAW,IAAI,YAAY,GAAQ,EAAE;AACrC,aAAS,IAAI,YAAY,GAAG,EAAE;AAE9B;AAAA,EACF;AAEA,SAAO;AACT,EAAG;AAEH,IAAI,mBAAmB,SAAU,MAAM;AACrC,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,QAAI,WAAW,KAAK,MAAM;AAE1B,aAAS,OAAO,GAAG,CAAC;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,CAAC;AAAA,EACV;AACF;AAGA,IAAI,eAAe,SAASC,cAAa,KAAK;AAC5C,WAAS,GAAG,GAAG,IAAI,IAAI,QAAQ,KAAK;AAClC,QAAI,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC;AAChC,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,CAAC,IAAI,IAAI,CAAC;AACd,QAAI,CAAC,IAAI;AAAA,EACX;AACA,SAAO;AACT;AAEA,IAAI,QAAQ,CAAC;AACb,IAAI,YAAY,SAASC,WAAU,UAAU,SAAS;AACpD,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AAEA,MAAI,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC;AAEnD,MAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,eAAW,CAAC,QAAQ;AAAA,EACtB;AAEA,WAAS,QAAQ,SAAU,IAAI,GAAG;AAChC,QAAI,OAAO,OAAO,UAAU;AAC1B,eAAS,CAAC,IAAI,SAAS,eAAe,EAAE;AACxC,UAAI,CAAC,SAAS,CAAC,GAAG;AAChB,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC1D;AAAA,IACF,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa;AACzC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,MAAI,WAAW;AAAA,IACb,MAAM,CAAC;AAAA,IACP,YACE;AAAA,IAEF,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,SAAS;AAAA;AAAA,IACT,cAAc;AAAA,IACd,aAAa;AAAA,IACb,iBAAiB;AAAA;AAAA,IAEjB,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,QAAQ;AAAA,IAER,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IAEd,iBAAiB;AAAA,IAEjB,MAAM;AAAA,IACN,gBAAgB;AAAA;AAAA,IAChB,OAAO,SAAS,OAAO;AAAA,IAAC;AAAA,IAExB,aAAa,CAAC,KAAK,KAAK;AAAA,IACxB,aAAa,KAAK,KAAK;AAAA,IACvB,cAAc;AAAA,IAEd,SAAS;AAAA,IACT,aAAa;AAAA,IAEb,OAAO;AAAA,IACP,aAAa;AAAA,IAEb,SAAS;AAAA,IAET,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AAEA,MAAI,SAAS;AACX,aAAS,OAAO,SAAS;AACvB,UAAI,OAAO,UAAU;AACnB,iBAAS,GAAG,IAAI,QAAQ,GAAG;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAGA,MAAI,OAAO,SAAS,iBAAiB,YAAY;AAC/C,QAAI,SAAS,SAAS;AACtB,aAAS,eAAe,SAAS,aAAa,IAAI;AAChD,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAGA,MAAI,OAAO,SAAS,UAAU,YAAY;AACxC,YAAQ,SAAS,OAAO;AAAA,MACtB,KAAK;AAAA,MAEL;AAEE,iBAAS,QAAQ;AACjB;AAAA,MAEF,KAAK;AACH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,iBAAO,IAAI,KAAK,IAAI,KAAK;AAAA,QAC3B;AACA;AAAA,MAWF,KAAK;AAIH,iBAAS,QAAQ,SAAS,YAAY,OAAO;AAC3C,cAAI,aAAa,SAAU,IAAI,KAAK,KAAM;AAC1C,iBAAO,KAAK,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,UAAU;AAAA,QACxD;AACA;AAAA,MAEF,KAAK;AAGH,iBAAS,QAAQ,SAAS,YAAY,OAAO;AAC3C,iBAAO,KAAK;AAAA,YACV,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,YAC5B,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,UAC9B;AAAA,QACF;AACA;AAAA,MAEF,KAAK;AAIH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,cAAI,aAAa,SAAU,IAAI,KAAK,KAAM;AAC1C,iBACE,KAAK,KAAK,IAAI,UAAU,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,UAAU;AAAA,QAElE;AACA;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,cAAI,cAAc,QAAS,KAAK,KAAK,IAAK,MAAO,IAAI,KAAK,KAAM;AAChE,iBACE,KAAK,KAAK,IAAI,UAAU,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,UAAU;AAAA,QAElE;AACA;AAAA,MAEF,KAAK;AACH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,cAAI,cAAc,QAAQ,UAAW,IAAI,KAAK,KAAM;AACpD,iBAAO,KAAK,KAAK,IAAI,UAAU,IAAI,WAAW,KAAK,IAAI,UAAU;AAAA,QACnE;AACA;AAAA,MAEF,KAAK;AACH,iBAAS,QAAQ,SAAS,UAAU,OAAO;AACzC,cAAI,cAAc,QAAQ,UAAW,IAAI,KAAK,KAAM;AACpD,eACI,QAAQ,UAAW,IAAI,KAAK,KAAM,KAAO,IAAI,KAAK,KAAM,MAC1D,GACA;AACA,mBACE,KACC,KAAK,IAAK,IAAI,KAAK,KAAM,KAAK,UAAU,IACvC,UAAU,KAAK,IAAK,IAAI,KAAK,KAAM,KAAK,UAAU;AAAA,UAExD,OAAO;AACL,mBAAO,KAAK,KAAK,IAAI,UAAU,IAAI,UAAU,KAAK,IAAI,UAAU;AAAA,UAClE;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AAGA,WAAS,WAAW,KAAK,IAAI,KAAK,MAAM,SAAS,QAAQ,GAAG,CAAC;AAG7D,MAAI,IAAI,SAAS;AACjB,MAAI,gBAAgB,IAAI,SAAS;AAGjC,MAAI,gBAAgB,KAAK,IAAI,SAAS,cAAc,SAAS,WAAW;AACxE,MAAI,cAAc,KAAK,IAAI,SAAS,aAAa,SAAS,WAAW;AACrE,MAAI,eAAe,SAAS;AAG5B,MAAI,MACF,KACA,KACA,QACA;AAGF,MAAI;AAGJ,MAAI;AACJ,WAAS,eAAe,KAAK,KAAK;AAChC,WACE,UACC,KAAK,OAAO,IAAI,KAAK,QAAQ,IAC9B,OACC,KAAK,OAAO,IAAI,KAAK,IAAI,QAAQ,IAClC,QACC,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,QAAQ,IAC5C;AAAA,EAEJ;AACA,UAAQ,SAAS,OAAO;AAAA,IACtB,KAAK;AACH,qBAAe,SAAS,qBAAqB;AAC3C,eAAO,eAAe,IAAI,EAAE;AAAA,MAC9B;AACA;AAAA,IAEF,KAAK;AACH,qBAAe,SAAS,sBAAsB;AAC5C,eAAO,eAAe,IAAI,EAAE;AAAA,MAC9B;AACA;AAAA,IAEF;AACE,UAAI,OAAO,SAAS,UAAU,YAAY;AACxC,uBAAe,SAAS;AAAA,MAC1B;AACA;AAAA,EACJ;AAGA,MAAI;AACJ,MAAI,OAAO,SAAS,eAAe,YAAY;AAC7C,wBAAoB,SAAS;AAAA,EAC/B;AAGA,MAAI,iBAAiB;AACrB,MAAI,OAAO,SAAS,YAAY,YAAY;AAC1C,qBAAiB,SAAS;AAAA,EAC5B;AAGA,MAAI,cAAc;AAClB,MAAI,WAAW,CAAC;AAChB,MAAI;AAEJ,MAAI,iCAAiC,SAASC,gCAC5C,KACA;AACA,QAAI,SAAS,IAAI;AACjB,QAAI,OAAO,OAAO,sBAAsB;AACxC,QAAI;AACJ,QAAI;AAEJ,QAAI,IAAI,SAAS;AACf,gBAAU,IAAI,QAAQ,CAAC,EAAE;AACzB,gBAAU,IAAI,QAAQ,CAAC,EAAE;AAAA,IAC3B,OAAO;AACL,gBAAU,IAAI;AACd,gBAAU,IAAI;AAAA,IAChB;AACA,QAAI,SAAS,UAAU,KAAK;AAC5B,QAAI,SAAS,UAAU,KAAK;AAE5B,QAAI,IAAI,KAAK,MAAO,UAAU,OAAO,QAAQ,KAAK,SAAS,KAAM,CAAC;AAClE,QAAI,IAAI,KAAK,MAAO,UAAU,OAAO,SAAS,KAAK,UAAU,KAAM,CAAC;AAEpE,QAAI,CAAC,SAAS,CAAC,GAAG;AAChB,aAAO;AAAA,IACT;AAEA,WAAO,SAAS,CAAC,EAAE,CAAC;AAAA,EACtB;AAEA,MAAI,iBAAiB,SAASC,gBAAe,KAAK;AAChD,QAAI,OAAO,+BAA+B,GAAG;AAE7C,QAAI,YAAY,MAAM;AACpB;AAAA,IACF;AAEA,cAAU;AACV,QAAI,CAAC,MAAM;AACT,eAAS,MAAM,QAAW,QAAW,GAAG;AAExC;AAAA,IACF;AAEA,aAAS,MAAM,KAAK,MAAM,KAAK,WAAW,GAAG;AAAA,EAC/C;AAEA,MAAI,iBAAiB,SAASC,gBAAe,KAAK;AAChD,QAAI,OAAO,+BAA+B,GAAG;AAC7C,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AAEA,aAAS,MAAM,KAAK,MAAM,KAAK,WAAW,GAAG;AAC7C,QAAI,eAAe;AAAA,EACrB;AAGA,MAAI,iBAAiB,CAAC;AACtB,MAAI,oBAAoB,SAASC,mBAAkB,QAAQ;AACzD,QAAI,eAAe,MAAM,GAAG;AAC1B,aAAO,eAAe,MAAM;AAAA,IAC9B;AAGA,QAAI,IAAI,SAAS;AAGjB,QAAI,IAAI;AACR,QAAI,SAAS,CAAC;AAEd,QAAI,WAAW,GAAG;AAChB,aAAO,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,IACvC;AAEA,WAAO,KAAK;AAEV,UAAI,KAAK;AACT,UAAI,SAAS,UAAU,UAAU;AAC/B,aAAK,SAAS,MAAO,IAAI,IAAK,IAAI,KAAK,EAAE;AAAA,MAC3C;AAGA,aAAO,KAAK;AAAA,QACV,OAAO,CAAC,IAAI,SAAS,KAAK,KAAK,IAAK,CAAC,IAAI,IAAK,IAAI,KAAK,EAAE;AAAA,QACzD,OAAO,CAAC,IACN,SAAS,KAAK,KAAK,IAAK,CAAC,IAAI,IAAK,IAAI,KAAK,EAAE,IAAI,SAAS;AAAA,QAC3D,IAAI,IAAK,IAAI,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAEA,mBAAe,MAAM,IAAI;AACzB,WAAO;AAAA,EACT;AAGA,MAAI,aAAa,SAASC,cAAa;AACrC,WACE,SAAS,iBAAiB,MAC1B,oBAAI,KAAK,GAAE,QAAQ,IAAI,aAAa,SAAS;AAAA,EAEjD;AAGA,MAAI,eAAe,SAASC,gBAAe;AACzC,QAAI,SAAS,gBAAgB,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,OAAO,IAAI,SAAS,aAAa;AACxC,aAAO;AAAA,IACT;AAEA,QAAI,kBAAkB,GAAG;AACvB,aAAO;AAAA,IACT;AAEA,WAAO,cAAc,KAAK,MAAM,KAAK,OAAO,IAAI,gBAAgB,YAAY,IAAI;AAAA,EAClF;AAEA,MAAI,cAAc,SAASC,aACzB,MACA,QACA,WACA,gBACA;AAIA,QAAI,QAAQ;AACZ,QAAI,WAAW,SAAS,aAAa,MAAM;AAC3C,QAAI,YAAY,SAAS,SAAS;AAChC,aAAO;AAAA,IACT;AAKA,QAAI,KAAK;AACT,QAAI,WAAW,aAAa;AAC1B,WAAM,SAAS,uBAAuB;AACpC,YAAIC,MAAK;AACT,eAAOA,MAAK,WAAW,aAAa;AAClC,UAAAA,OAAM;AAAA,QACR;AACA,eAAOA;AAAA,MACT,EAAG;AAAA,IACL;AAGA,QAAI;AACJ,QAAI,mBAAmB;AACrB,mBAAa,kBAAkB,MAAM,QAAQ,UAAU,cAAc;AAAA,IACvE,OAAO;AACL,mBAAa,SAAS;AAAA,IACxB;AAEA,QAAI,UAAU,SAAS,cAAc,QAAQ;AAC7C,QAAI,OAAO,QAAQ,WAAW,MAAM,EAAE,oBAAoB,KAAK,CAAC;AAEhE,SAAK,OACH,aACA,OACC,WAAW,IAAI,SAAS,EAAE,IAC3B,QACA,SAAS;AAGX,QAAI,KAAK,KAAK,YAAY,IAAI,EAAE,QAAQ;AACxC,QAAI,KACF,KAAK;AAAA,MACH,WAAW;AAAA,MACX,KAAK,YAAY,GAAG,EAAE;AAAA,MACtB,KAAK,YAAY,GAAQ,EAAE;AAAA,IAC7B,IAAI;AAIN,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,YAAY,KAAK;AACrB,QAAI,MAAM,KAAK,KAAK,WAAW,CAAC;AAChC,QAAI,MAAM,KAAK,KAAK,YAAY,CAAC;AACjC,eAAW,MAAM;AACjB,gBAAY,MAAM;AAMlB,QAAI,kBAAkB,CAAC,KAAK;AAI5B,QAAI,kBAAkB,CAAC,KAAK;AAG5B,QAAI,MAAM,KAAK;AAAA,OACZ,WAAW,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,IACtC,YAAY,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,KACxC;AAAA,IACJ;AACA,QAAI,MAAM,KAAK;AAAA,OACZ,WAAW,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,IACtC,YAAY,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,KACxC;AAAA,IACJ;AACA,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AAEnB,YAAQ,aAAa,SAAS,KAAK;AACnC,YAAQ,aAAa,UAAU,MAAM;AAErC,QAAI,OAAO;AAET,eAAS,KAAK,YAAY,OAAO;AAEjC,WAAK,KAAK;AAAA,IACZ;AAGA,SAAK,MAAM,IAAI,IAAI,IAAI,EAAE;AACzB,SAAK,UAAW,QAAQ,KAAM,GAAI,SAAS,KAAM,CAAC;AAClD,SAAK,OAAO,CAAC,SAAS;AAItB,SAAK,OACH,aACA,OACC,WAAW,IAAI,SAAS,EAAE,IAC3B,QACA,SAAS;AAQX,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK;AAAA,MACH;AAAA,MACA,kBAAkB;AAAA,OACjB,kBAAkB,WAAW,OAAO;AAAA,IACvC;AAGA,QAAI,YAAY,KAAK,aAAa,GAAG,GAAG,OAAO,MAAM,EAAE;AAEvD,QAAI,WAAW,GAAG;AAChB,aAAO;AAAA,IACT;AAEA,QAAI,OAAO;AAET,WAAK,WAAW,kBAAkB,IAAI,iBAAiB,KAAK,IAAI,KAAK,EAAE;AACvE,WAAK,QAAQ;AAAA,IACf;AAGA,QAAI,WAAW,CAAC;AAChB,QAAI,KAAK;AACT,QAAI,IAAI,GAAG;AACX,QAAI,SAAS,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAChD,WAAO,MAAM;AACX,WAAK;AACL,aAAO,MAAM;AACX,YAAI;AAEJ;AAAgB,iBAAO,KAAK;AAC1B,gBAAI;AACJ,mBAAO,KAAK;AACV,kBAAI,YAAY,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,MAAM,IAAI,CAAC,GAAG;AAC5D,yBAAS,KAAK,CAAC,IAAI,EAAE,CAAC;AAEtB,oBAAI,KAAK,OAAO,CAAC,GAAG;AAClB,yBAAO,CAAC,IAAI;AAAA,gBACd;AACA,oBAAI,KAAK,OAAO,CAAC,GAAG;AAClB,yBAAO,CAAC,IAAI;AAAA,gBACd;AACA,oBAAI,KAAK,OAAO,CAAC,GAAG;AAClB,yBAAO,CAAC,IAAI;AAAA,gBACd;AACA,oBAAI,KAAK,OAAO,CAAC,GAAG;AAClB,yBAAO,CAAC,IAAI;AAAA,gBACd;AAEA,oBAAI,OAAO;AACT,uBAAK,YAAY;AACjB,uBAAK,SAAS,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,gBAChD;AACA,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,YAAI,OAAO;AACT,eAAK,YAAY;AACjB,eAAK,SAAS,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO;AACT,WAAK,YAAY;AACjB,WAAK;AAAA,QACH,OAAO,CAAC,IAAI;AAAA,QACZ,OAAO,CAAC,IAAI;AAAA,SACX,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,SAC7B,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,MAChC;AAAA,IACF;AAGA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,aAAa,SAASC,YAAW,IAAI,IAAI,IAAI,IAAI,UAAU;AAG7D,QAAI,IAAI,SAAS;AACjB,WAAO,KAAK;AACV,UAAI,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;AAC3B,UAAI,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;AAE3B,UAAI,MAAM,OAAO,MAAM,OAAO,KAAK,KAAK,KAAK,GAAG;AAC9C,YAAI,CAAC,SAAS,gBAAgB;AAC5B,iBAAO;AAAA,QACT;AACA;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAGA,MAAI,WAAW,SAASC,UACtB,IACA,IACA,MACA,MACA,QACA,UACA,OACA,WACA,YACA,gBACA;AACA,QAAI,WAAW,KAAK;AACpB,QAAI;AACJ,QAAI,cAAc;AAChB,cAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,cAAQ,SAAS;AAAA,IACnB;AAGA,QAAI;AACJ,QAAI,mBAAmB;AACrB,mBAAa,kBAAkB,MAAM,QAAQ,UAAU,cAAc;AAAA,IACvE,OAAO;AACL,mBAAa,SAAS;AAAA,IACxB;AAEA,QAAI;AACJ,QAAI,gBAAgB;AAClB,gBAAU,eAAe,MAAM,QAAQ,UAAU,cAAc;AAAA,IACjE,OAAO;AACL,gBAAU,SAAS;AAAA,IACrB;AAEA,aAAS,QAAQ,SAAU,IAAI;AAC7B,UAAI,GAAG,YAAY;AACjB,YAAI,MAAM,GAAG,WAAW,IAAI;AAC5B,YAAI,KAAK,KAAK;AAGd,YAAI,KAAK;AACT,YAAI,MAAM,IAAI,IAAI,IAAI,EAAE;AAExB,YAAI,OACF,aACA,OACC,WAAW,IAAI,SAAS,EAAE,IAC3B,QACA,SAAS;AACX,YAAI,YAAY;AAIhB,YAAI,WAAW,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE;AAEtE,YAAI,cAAc,GAAG;AACnB,cAAI,OAAO,CAAC,SAAS;AAAA,QACvB;AASA,YAAI,eAAe;AACnB,YAAI;AAAA,UACF;AAAA,UACA,KAAK,kBAAkB;AAAA,WACtB,KAAK,kBAAkB,WAAW,OAAO;AAAA,QAC5C;AAOA,YAAI,QAAQ;AAAA,MACd,OAAO;AAEL,YAAI,OAAO,SAAS,cAAc,MAAM;AACxC,YAAI,gBAAgB;AACpB,wBAAgB,YAAa,CAAC,YAAY,KAAK,KAAM,MAAM;AAC3D,YAAI,KAAK,OAAO,GAAG;AACjB,2BACE,iBACA,KAAK,gBAAgB,IACrB,eAEA,IAAI,KAAK,KACT;AAAA,QACJ;AACA,YAAI,aAAa;AAAA,UACf,UAAU;AAAA,UACV,SAAS;AAAA,UACT,MACE,aAAa,MAAM,WAAW,KAAK,KAAK,QAAQ,SAAS;AAAA,UAC3D,OAAO,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,kBAAkB;AAAA,UACtD,MAAM,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,kBAAkB;AAAA,UACrD,OAAO,KAAK,gBAAgB;AAAA,UAC5B,QAAQ,KAAK,iBAAiB;AAAA,UAC9B,YAAY,WAAW;AAAA,UACvB,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,iBAAiB;AAAA,UACjB,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,uBAAuB;AAAA,UACvB,mBAAmB;AAAA,QACrB;AACA,YAAI,OAAO;AACT,qBAAW,QAAQ;AAAA,QACrB;AACA,aAAK,cAAc;AACnB,iBAAS,WAAW,YAAY;AAC9B,eAAK,MAAM,OAAO,IAAI,WAAW,OAAO;AAAA,QAC1C;AACA,YAAI,YAAY;AACd,mBAAS,aAAa,YAAY;AAChC,iBAAK,aAAa,WAAW,WAAW,SAAS,CAAC;AAAA,UACpD;AAAA,QACF;AACA,YAAI,SAAS;AACX,eAAK,aAAa;AAAA,QACpB;AACA,WAAG,YAAY,IAAI;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAGA,MAAI,aAAa,SAASC,YAAW,GAAG,GAAG,UAAU,WAAW,MAAM;AACpE,QAAI,KAAK,OAAO,KAAK,OAAO,IAAI,KAAK,IAAI,GAAG;AAC1C;AAAA,IACF;AAEA,SAAK,CAAC,EAAE,CAAC,IAAI;AAEb,QAAI,UAAU;AACZ,UAAI,MAAM,SAAS,CAAC,EAAE,WAAW,IAAI;AACrC,UAAI,SAAS,IAAI,GAAG,IAAI,GAAG,eAAe,aAAa;AAAA,IACzD;AAEA,QAAI,aAAa;AACf,eAAS,CAAC,EAAE,CAAC,IAAI,EAAE,MAAY,UAAqB;AAAA,IACtD;AAAA,EACF;AAIA,MAAI,aAAa,SAASC,YAAW,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM;AAC/D,QAAI,WAAW,KAAK;AACpB,QAAI,WAAW,SAAS;AACxB,QAAI;AACJ,QAAI,UAAU;AACZ,YAAM,SAAS,CAAC,EAAE,WAAW,IAAI;AACjC,UAAI,KAAK;AACT,UAAI,YAAY,SAAS;AAAA,IAC3B;AAEA,QAAI;AACJ,QAAI,aAAa;AACf,UAAI,SAAS,KAAK;AAClB,kBAAY;AAAA,QACV,IAAI,KAAK,OAAO,CAAC,KAAK;AAAA,QACtB,IAAI,KAAK,OAAO,CAAC,KAAK;AAAA,QACtB,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,QACjC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,MACnC;AAAA,IACF;AAEA,QAAI,IAAI,SAAS;AACjB,WAAO,KAAK;AACV,UAAI,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;AAC3B,UAAI,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;AAE3B,UAAI,MAAM,OAAO,MAAM,OAAO,KAAK,KAAK,KAAK,GAAG;AAC9C;AAAA,MACF;AAEA,iBAAW,IAAI,IAAI,UAAU,WAAW,IAAI;AAAA,IAC9C;AAEA,QAAI,UAAU;AACZ,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AAKA,MAAI,UAAU,SAASC,SAAQ,MAAM,WAAW;AAC9C,QAAI,YAAY,IAAI;AAClB,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,QAAQ;AAClB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAO,KAAK,CAAC;AACb,eAAS,KAAK,CAAC;AAAA,IACjB,OAAO;AACL,aAAO,KAAK;AACZ,eAAS,KAAK;AACd,mBAAa,KAAK;AAAA,IACpB;AACA,QAAI,YAAY,aAAa;AAE7B,QAAI,iBAAiB,iBAAiB,IAAI;AAG1C,QAAI,OAAO,YAAY,MAAM,QAAQ,WAAW,cAAc;AAG9D,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,GAAG;AAChB,aAAO;AAAA,IACT;AAKA,QAAI,CAAC,SAAS,kBAAkB,CAAC,SAAS,aAAa;AACrD,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK;AACtE,eAAO;AAAA,MACT;AAAA,IACF;AAIA,QAAI,IAAI,YAAY;AAEpB,QAAI,sBAAsB,SAAU,KAAK;AACvC,UAAI,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC;AACxC,UAAI,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC;AACxC,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AAId,UAAI,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,GAAG;AAC9C,eAAO;AAAA,MACT;AAGA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,IAAI,CAAC;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,iBAAW,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI;AAErC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,WAAO,KAAK;AACV,UAAI,SAAS,kBAAkB,YAAY,CAAC;AAE5C,UAAI,SAAS,SAAS;AACpB,iBAAS,CAAC,EAAE,OAAO,MAAM;AACzB,qBAAa,MAAM;AAAA,MACrB;AAKA,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,MAAM,oBAAoB,OAAO,CAAC,CAAC;AACvC,YAAI,KAAK;AACP,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IAOF;AAEA,QAAI,SAAS,aAAa;AACxB,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAK,CAAC,IAAK,KAAK,CAAC,IAAI,IAAK;AAAA,MAC5B,OAAO;AACL,aAAK,SAAU,KAAK,SAAS,IAAK;AAAA,MACpC;AACA,aAAOA,SAAQ,MAAM,YAAY,CAAC;AAAA,IACpC;AAGA,WAAO;AAAA,EACT;AAIA,MAAI,YAAY,SAASC,WAAU,MAAM,YAAY,SAAS;AAC5D,QAAI,YAAY;AACd,aAAO,CAAC,SAAS,KAAK,SAAU,IAAI;AAClC,YAAI,QAAQ,IAAI,YAAY,MAAM;AAAA,UAChC,QAAQ,WAAW,CAAC;AAAA,QACtB,CAAC;AACD,eAAO,CAAC,GAAG,cAAc,KAAK;AAAA,MAChC,GAAG,IAAI;AAAA,IACT,OAAO;AACL,eAAS,QAAQ,SAAU,IAAI;AAC7B,YAAI,QAAQ,IAAI,YAAY,MAAM;AAAA,UAChC,QAAQ,WAAW,CAAC;AAAA,QACtB,CAAC;AACD,WAAG,cAAc,KAAK;AAAA,MACxB,GAAG,IAAI;AAAA,IACT;AAAA,EACF;AAGA,MAAI,QAAQ,SAASC,SAAQ;AAG3B,QAAI,SAAS,SAAS,CAAC;AAEvB,QAAI,OAAO,YAAY;AACrB,YAAM,KAAK,KAAK,OAAO,QAAQ,CAAC;AAChC,YAAM,KAAK,KAAK,OAAO,SAAS,CAAC;AAAA,IACnC,OAAO;AACL,UAAI,OAAO,OAAO,sBAAsB;AACxC,YAAM,KAAK,KAAK,KAAK,QAAQ,CAAC;AAC9B,YAAM,KAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IACjC;AAIA,QAAI,CAAC,UAAU,kBAAkB,IAAI,GAAG;AACtC;AAAA,IACF;AAGA,aAAS,SAAS,SACd,CAAC,SAAS,OAAO,CAAC,IAAI,GAAG,SAAS,OAAO,CAAC,IAAI,CAAC,IAC/C,CAAC,MAAM,GAAG,MAAM,CAAC;AAGrB,gBAAY,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG,CAAC;AAIvD,WAAO,CAAC;AAER,QAAI,IAAI,IAAI;AACZ,QAAI,CAAC,OAAO,cAAc,SAAS,aAAa;AAC9C,eAAS,QAAQ,SAAU,IAAI;AAC7B,YAAI,GAAG,YAAY;AACjB,cAAI,MAAM,GAAG,WAAW,IAAI;AAC5B,cAAI,YAAY,SAAS;AACzB,cAAI,UAAU,GAAG,GAAG,OAAO,IAAI,IAAI,OAAO,IAAI,EAAE;AAChD,cAAI,SAAS,GAAG,GAAG,OAAO,IAAI,IAAI,OAAO,IAAI,EAAE;AAAA,QACjD,OAAO;AACL,aAAG,cAAc;AACjB,aAAG,MAAM,kBAAkB,SAAS;AACpC,aAAG,MAAM,WAAW;AAAA,QACtB;AAAA,MACF,CAAC;AAGD,WAAK;AACL,aAAO,MAAM;AACX,aAAK,EAAE,IAAI,CAAC;AACZ,aAAK;AACL,eAAO,MAAM;AACX,eAAK,EAAE,EAAE,EAAE,IAAI;AAAA,QACjB;AAAA,MACF;AAAA,IACF,OAAO;AAGL,UAAI,OAAO,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAE3D,WAAK,YAAY,SAAS;AAC1B,WAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,UAAI,UAAU,KAAK,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE;AAK5C,UAAI,YAAY,OACb,WAAW,IAAI,EACf,aAAa,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC,EAAE;AAExC,WAAK;AACL,UAAI,GAAG;AACP,aAAO,MAAM;AACX,aAAK,EAAE,IAAI,CAAC;AACZ,aAAK;AACL,eAAO,MAAM;AACX,cAAI;AAEJ;AAAgB,mBAAO,KAAK;AAC1B,kBAAI;AACJ,qBAAO,KAAK;AACV,oBAAI;AACJ,uBAAO,KAAK;AACV,sBACE,YAAY,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC,MACzD,QAAQ,CAAC,GACT;AACA,yBAAK,EAAE,EAAE,EAAE,IAAI;AACf,0BAAM;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,cAAI,KAAK,EAAE,EAAE,EAAE,MAAM,OAAO;AAC1B,iBAAK,EAAE,EAAE,EAAE,IAAI;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAEA,kBAAY,OAAO,UAAU;AAAA,IAC/B;AAGA,QAAI,SAAS,SAAS,SAAS,OAAO;AACpC,oBAAc;AAGd,WAAK,MAAM;AACX,aAAO,MAAM;AACX,iBAAS,EAAE,IAAI,CAAC;AAAA,MAClB;AAEA,UAAI,SAAS,OAAO;AAClB,eAAO,iBAAiB,aAAa,cAAc;AAAA,MACrD;AAEA,UAAI,SAAS,OAAO;AAClB,eAAO,iBAAiB,SAAS,cAAc;AAC/C,eAAO,iBAAiB,cAAc,cAAc;AACpD,eAAO,iBAAiB,YAAY,SAAU,GAAG;AAC/C,YAAE,eAAe;AAAA,QACnB,CAAC;AACD,eAAO,MAAM,0BAA0B;AAAA,MACzC;AAEA,aAAO,iBAAiB,kBAAkB,SAAS,kBAAkB;AACnE,eAAO,oBAAoB,kBAAkB,eAAe;AAE5D,eAAO,oBAAoB,aAAa,cAAc;AACtD,eAAO,oBAAoB,SAAS,cAAc;AAClD,kBAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,QAAI;AACJ,QAAI,iBAAiB;AACrB,QAAI,YAAY;AAChB,QAAI,CAAC,SAAS,iBAAiB;AAC7B,wBAAkB,SAAU,IAAI;AAC9B,WAAG;AAAA,MACL;AACA,yBAAmB,WAAY;AAC7B,oBAAY;AAAA,MACd;AAAA,IACF,WAAW,SAAS,SAAS,GAAG;AAC9B,wBAAkB,OAAO;AACzB,yBAAmB,OAAO;AAAA,IAC5B,OAAO;AACL,wBAAkB,OAAO;AACzB,yBAAmB,OAAO;AAAA,IAC5B;AAEA,QAAI,mBAAmB,SAASC,kBAAiB,MAAM,UAAU;AAC/D,eAAS,QAAQ,SAAU,IAAI;AAC7B,WAAG,iBAAiB,MAAM,QAAQ;AAAA,MACpC,GAAG,IAAI;AAAA,IACT;AAEA,QAAI,sBAAsB,SAASC,qBAAoB,MAAM,UAAU;AACrE,eAAS,QAAQ,SAAU,IAAI;AAC7B,WAAG,oBAAoB,MAAM,QAAQ;AAAA,MACvC,GAAG,IAAI;AAAA,IACT;AAEA,QAAI,wBAAwB,SAASC,yBAAwB;AAC3D,0BAAoB,kBAAkBA,sBAAqB;AAC3D,uBAAiB,MAAM,OAAO,CAAC;AAAA,IACjC;AAEA,qBAAiB,kBAAkB,qBAAqB;AAGxD,UAAM,OAAO,KAAK,SAAS,kBAAkB,kBAAkB;AAAA,MAC7D,SAAS,OAAO;AACd,YAAI,CAAC,WAAW;AACd;AAAA,QACF;AACA,YAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,2BAAiB,MAAM,OAAO,CAAC;AAC/B,oBAAU,iBAAiB,KAAK;AAChC,8BAAoB,kBAAkB,qBAAqB;AAC3D,iBAAO,MAAM,OAAO;AACpB;AAAA,QACF;AACA,sBAAa,oBAAI,KAAK,GAAE,QAAQ;AAChC,YAAI,QAAQ,QAAQ,SAAS,KAAK,CAAC,GAAG,CAAC;AACvC,YAAI,WAAW,CAAC,UAAU,kBAAkB,MAAM;AAAA,UAChD,MAAM,SAAS,KAAK,CAAC;AAAA,UACrB;AAAA,QACF,CAAC;AACD,YAAI,WAAW,KAAK,UAAU;AAC5B,2BAAiB,MAAM,OAAO,CAAC;AAC/B,mBAAS,MAAM;AACf,oBAAU,kBAAkB,KAAK;AACjC,oBAAU,iBAAiB,KAAK;AAChC,8BAAoB,kBAAkB,qBAAqB;AAC3D;AAAA,QACF;AACA;AACA,cAAM,OAAO,IAAI,gBAAgB,MAAM,SAAS,IAAI;AAAA,MACtD;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AAGA,QAAM;AACR;AAEA,UAAU,cAAc;AACxB,UAAU,cAAc;AAExB,IAAO,iBAAQ;;;ACnzCf,IAAI,CAAC,eAAsB,aAAa;AACtC,QAAM,IAAI,MAAM,0CAA0C;AAC5D;AAGA,SAAS,iBAAiB,YAAY;AACpC,MAAI,MAAM,WAAW,WAAW,IAAI;AACpC,MAAI,YAAY,IAAI,aAAa,GAAG,GAAG,WAAW,OAAO,WAAW,MAAM;AAC1E,MAAI,eAAe,IAAI,gBAAgB,SAAS;AAEhD,MAAI,UAAU;AACd,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,QAAQ,KAAK,GAAG;AACjD,QAAI,QAAQ,UAAU,KAAK,IAAI,CAAC;AAChC,QAAI,QAAQ,KAAK;AACf,UAAI,OACF,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC;AAClE,iBAAW;AACX,QAAE;AAAA,IACJ;AAAA,EACF;AACA,MAAI,YAAY,UAAU;AAE1B,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,QAAQ,KAAK,GAAG;AACjD,QAAI,OACF,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC;AAClE,QAAI,QAAQ,UAAU,KAAK,IAAI,CAAC;AAEhC,QAAI,QAAQ,OAAO,OAAO,WAAW;AAEnC,mBAAa,KAAK,CAAC,IAAI;AACvB,mBAAa,KAAK,IAAI,CAAC,IAAI;AAC3B,mBAAa,KAAK,IAAI,CAAC,IAAI;AAC3B,mBAAa,KAAK,IAAI,CAAC,IAAI;AAAA,IAC7B,OAAO;AAGL,mBAAa,KAAK,CAAC,IAAI;AACvB,mBAAa,KAAK,IAAI,CAAC,IAAI;AAC3B,mBAAa,KAAK,IAAI,CAAC,IAAI;AAC3B,mBAAa,KAAK,IAAI,CAAC,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,aAAa,cAAc,GAAG,CAAC;AACrC;AAEQ,eAAe,SAAU,SAAS,KAAK;AAC7C,UAAQ,iBAAiB,aAAa,SAAU,aAAa;AAC3D,QAAI,WAAmB,eAAO;AAAA,MAC5B,YAAY,mBAAmB;AAAA,MAC/B;AAAA,QACE,OAAO,IAAI,SAAS;AAAA,QACpB,QAAQ,IAAI,UAAU;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,aAAa,YAAY,IAAI,YAAY;AAC7C,QAAI,YAAY,YAAY,IAAI,WAAW;AAC3C,QAAI,QAAQ,YAAY,UAAU,QAAQ,UAAU,SAAS;AAC7D,kBAAc,iBAAiB,UAAU,KAAK;AAE9C,QAAI,OAAO,YAAY,QAAQ;AAE/B,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,WAAO,QAAQ,SAAS;AACxB,WAAO,SAAS,SAAS;AAEzB,QAAI,MAAM,OAAO,WAAW,IAAI;AAChC,QAAI,WAAW;AACb,UAAI;AACF,YAAI,UAAU,WAAW,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC1D,yBAAiB,MAAM;AAAA,MACzB,SAAS,GAAG;AACV,gBAAQ,MAAM,oBAAoB;AAClC,gBAAQ,MAAM,EAAE,SAAS,CAAC;AAAA,MAC5B;AAAA,IACF;AAEA,QAAI,YAAY,YAAY,IAAI,WAAW;AAC3C,QAAI,gBAAgB,YAAY,IAAI,eAAe;AACnD,QAAI,cAAc,KAAK,cAAc,OAAO;AAE5C,QAAI,gBAAgB,KAAK,KAAK;AAC9B,QAAI,WAAW,YAAY,IAAI,UAAU;AACzC,mBAAsB,QAAQ;AAAA,MAC5B,MAAM,KACH,SAAS,SAAS,SAAU,OAAO,KAAK;AACvC,YAAI,YAAY,KAAK,aAAa,GAAG;AACrC,eAAO;AAAA,UACL,KAAK,QAAQ,GAAG;AAAA,UAChB,UAAU,IAAI,sBAAsB,IAAI,KAC9B,eAAO,UAAU,OAAO,aAAa,SAAS;AAAA,UACxD;AAAA,QACF;AAAA,MACF,CAAC,EACA,KAAK,SAAU,GAAG,GAAG;AAEpB,eAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACnB,CAAC;AAAA,MACH,YACE,YAAY,IAAI,sBAAsB,KACtC,YAAY,IAAI,+BAA+B,KAC/C,QAAQ,IAAI,sBAAsB;AAAA,MACpC,YACE,YAAY,IAAI,sBAAsB,KACtC,YAAY,IAAI,+BAA+B,KAC/C,QAAQ,IAAI,sBAAsB;AAAA,MAEpC;AAAA,MAEA,aAAa,SAAS,SAAS,SAAS;AAAA,MAExC,aAAa,cAAc,CAAC,IAAI;AAAA,MAChC,aAAa,cAAc,CAAC,IAAI;AAAA,MAEhC,aAAa,CAAC;AAAA,MAEd,aAAa;AAAA,MAEb,cAAc,YAAY,IAAI,cAAc,IAAI;AAAA,MAEhD,gBAAgB,YAAY,IAAI,gBAAgB;AAAA,MAChD,aAAa,YAAY,IAAI,aAAa;AAAA,MAE1C,iBAAiB,YAAY,IAAI,iBAAiB;AAAA,MAElD,SAAS;AAAA,MAET,OAAO,YAAY,IAAI,OAAO;AAAA,IAChC,CAAC;AAED,aAAS,iBAAiB,GAAG;AAC3B,UAAI,OAAO,EAAE,OAAO;AACpB,UAAI,EAAE,OAAO,SAAS,YAAY,eAAe,QAAQ;AACvD,UAAE,OAAO,MAAM,MAAM,SAAS,IAAI;AAClC,UAAE,OAAO,MAAM,MAAM,SAAS,IAAI;AAClC,oBAAY,eAAe;AAAA,UACzB,KAAK,CAAC;AAAA,UACN,KAAK,CAAC;AAAA,UACN,KAAK,CAAC;AAAA,UACN,EAAE,OAAO;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAEA,WAAO,iBAAiB,kBAAkB,gBAAgB;AAE1D,QAAI,YAAY,gBAAgB;AAE9B,kBAAY,eAAe,QAAQ;AAAA,IACrC;AAEA,gBAAY,iBAAiB;AAAA,MAC3B,QAAQ;AAAA,MAER,SAAS,WAAY;AACnB,eAAO,oBAAoB,kBAAkB,gBAAgB;AAE7D,eAAO,iBAAiB,kBAAkB,SAAU,GAAG;AAErD,YAAE,eAAe;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AAEO,qBAAqB,SAAU,QAAQ;AAC7C,MAAI,UAAU,UAAU,CAAC,GAAG;AAC5B,GAAS,aAAK,QAAQ,MAAM,MAAM,SAAS,SAAS,CAAC,MAAM,IAAI,CAAC;AAEhE,MAAI,UAAU,CAAC,eAAe,cAAc,iBAAiB,eAAe;AAE5E,EAAQ,aAAK,KAAK,QAAQ,SAAU,YAAY;AAC9C,QAAI,cAAc,WAAW,SAAS,aAAa;AACjD,UAAI,YAAY,WAAW,aAAa,CAAC;AAEzC,sBAAgB,UAAU,MAAM;AAChC,sBAAgB,UAAU,QAAQ;AAAA,IACpC;AAAA,EACF,CAAC;AAED,WAAS,gBAAgB,WAAW;AAClC,iBACU,aAAK,KAAK,SAAS,SAAU,KAAK;AACxC,UAAI,UAAU,eAAe,GAAG,GAAG;AACjC,kBAAU,SAAiB,eAAO,aAAa,GAAG,CAAC,IAAI,UAAU,GAAG;AAAA,MACtE;AAAA,IACF,CAAC;AAAA,EACL;AACF,CAAC;AAED,SAAS,iBAAiB,UAAU,QAAQ;AAG1C,MAAI,QAAQ,SAAS;AACrB,MAAI,SAAS,SAAS;AACtB,MAAI,QAAQ,SAAS,QAAQ;AAC3B,aAAS,MAAM,QAAQ,SAAS,UAAU;AAC1C,aAAS,QAAQ,SAAS;AAAA,EAC5B,OAAO;AACL,aAAS,MAAM,SAAS,QAAQ,UAAU;AAC1C,aAAS,SAAS,QAAQ;AAAA,EAC5B;AACF;", "names": ["install", "setZeroTimeout", "timer", "isSupported", "shuffle<PERSON><PERSON><PERSON>", "WordCloud", "getInfoGridFromMouseTouchEvent", "wordcloudhover", "wordcloudclick", "getPointsAtRadius", "exceedTime", "getRotateDeg", "getTextInfo", "mu", "canFitText", "drawText", "fillGridAt", "updateGrid", "putWord", "sendEvent", "start", "addEventListener", "removeEventListener", "anotherWordCloudStart"]}