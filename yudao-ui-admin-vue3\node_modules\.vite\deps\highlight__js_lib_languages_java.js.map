{"version": 3, "sources": ["../../.pnpm/highlight.js@11.10.0/node_modules/highlight.js/es/languages/java.js"], "sourcesContent": ["// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\nLanguage: Java\nAuthor: Vsevolod Solovyov <<EMAIL>>\nCategory: common, enterprise\nWebsite: https://www.java.com/\n*/\n\n\n/**\n * Allows recursive regex expressions to a given depth\n *\n * ie: recurRegex(\"(abc~~~)\", /~~~/g, 2) becomes:\n * (abc(abc(abc)))\n *\n * @param {string} re\n * @param {RegExp} substitution (should be a g mode regex)\n * @param {number} depth\n * @returns {string}``\n */\nfunction recurRegex(re, substitution, depth) {\n  if (depth === -1) return \"\";\n\n  return re.replace(substitution, _ => {\n    return recurRegex(re, substitution, depth - 1);\n  });\n}\n\n/** @type LanguageFn */\nfunction java(hljs) {\n  const regex = hljs.regex;\n  const JAVA_IDENT_RE = '[\\u00C0-\\u02B8a-zA-Z_$][\\u00C0-\\u02B8a-zA-Z_$0-9]*';\n  const GENERIC_IDENT_RE = JAVA_IDENT_RE\n    + recurRegex('(?:<' + JAVA_IDENT_RE + '~~~(?:\\\\s*,\\\\s*' + JAVA_IDENT_RE + '~~~)*>)?', /~~~/g, 2);\n  const MAIN_KEYWORDS = [\n    'synchronized',\n    'abstract',\n    'private',\n    'var',\n    'static',\n    'if',\n    'const ',\n    'for',\n    'while',\n    'strictfp',\n    'finally',\n    'protected',\n    'import',\n    'native',\n    'final',\n    'void',\n    'enum',\n    'else',\n    'break',\n    'transient',\n    'catch',\n    'instanceof',\n    'volatile',\n    'case',\n    'assert',\n    'package',\n    'default',\n    'public',\n    'try',\n    'switch',\n    'continue',\n    'throws',\n    'protected',\n    'public',\n    'private',\n    'module',\n    'requires',\n    'exports',\n    'do',\n    'sealed',\n    'yield',\n    'permits',\n    'goto'\n  ];\n\n  const BUILT_INS = [\n    'super',\n    'this'\n  ];\n\n  const LITERALS = [\n    'false',\n    'true',\n    'null'\n  ];\n\n  const TYPES = [\n    'char',\n    'boolean',\n    'long',\n    'float',\n    'int',\n    'byte',\n    'short',\n    'double'\n  ];\n\n  const KEYWORDS = {\n    keyword: MAIN_KEYWORDS,\n    literal: LITERALS,\n    type: TYPES,\n    built_in: BUILT_INS\n  };\n\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@' + JAVA_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [ \"self\" ] // allow nested () inside our annotation\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    relevance: 0,\n    contains: [ hljs.C_BLOCK_COMMENT_MODE ],\n    endsParent: true\n  };\n\n  return {\n    name: 'Java',\n    aliases: [ 'jsp' ],\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/,\n              relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      // relevance boost\n      {\n        begin: /import java\\.[a-z]+\\./,\n        keywords: \"import\",\n        relevance: 2\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        begin: /\"\"\"/,\n        end: /\"\"\"/,\n        className: \"string\",\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        match: [\n          /\\b(?:class|interface|enum|extends|implements|new)/,\n          /\\s+/,\n          JAVA_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n      {\n        // Exceptions for hyphenated keywords\n        match: /non-sealed/,\n        scope: \"keyword\"\n      },\n      {\n        begin: [\n          regex.concat(/(?!else)/, JAVA_IDENT_RE),\n          /\\s+/,\n          JAVA_IDENT_RE,\n          /\\s+/,\n          /=(?!=)/\n        ],\n        className: {\n          1: \"type\",\n          3: \"variable\",\n          5: \"operator\"\n        }\n      },\n      {\n        begin: [\n          /record/,\n          /\\s+/,\n          JAVA_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        },\n        contains: [\n          PARAMS,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new throw return else',\n        relevance: 0\n      },\n      {\n        begin: [\n          '(?:' + GENERIC_IDENT_RE + '\\\\s+)',\n          hljs.UNDERSCORE_IDENT_RE,\n          /\\s*(?=\\()/\n        ],\n        className: { 2: \"title.function\" },\n        keywords: KEYWORDS,\n        contains: [\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              ANNOTATION,\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              NUMERIC,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      NUMERIC,\n      ANNOTATION\n    ]\n  };\n}\n\nexport { java as default };\n"], "mappings": ";;;AACA,IAAI,gBAAgB;AACpB,IAAI,OAAO,OAAO,aAAa;AAC/B,IAAI,YAAY;AAChB,IAAI,UAAU;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA;AAAA;AAAA,IAGR,EAAE,OAAO,QAAQ,aAAa,MAAM,IAAI,YAAY,IAAI,eACzC,aAAa,cAAc;AAAA;AAAA,IAE1C,EAAE,OAAO,OAAO,aAAa,MAAM,IAAI,+BAA+B;AAAA,IACtE,EAAE,OAAO,IAAI,IAAI,cAAc;AAAA,IAC/B,EAAE,OAAO,OAAO,aAAa,aAAa;AAAA;AAAA,IAG1C,EAAE,OAAO,aAAa,SAAS,UAAU,SAAS,SAAS,SAAS,eACrD,aAAa,cAAc;AAAA;AAAA,IAG1C,EAAE,OAAO,iCAAiC;AAAA;AAAA,IAG1C,EAAE,OAAO,YAAY,SAAS,YAAY;AAAA;AAAA,IAG1C,EAAE,OAAO,yBAAyB;AAAA;AAAA,IAGlC,EAAE,OAAO,gCAAgC;AAAA,EAC3C;AAAA,EACA,WAAW;AACb;AAqBA,SAAS,WAAW,IAAI,cAAc,OAAO;AAC3C,MAAI,UAAU;AAAI,WAAO;AAEzB,SAAO,GAAG,QAAQ,cAAc,OAAK;AACnC,WAAO,WAAW,IAAI,cAAc,QAAQ,CAAC;AAAA,EAC/C,CAAC;AACH;AAGA,SAAS,KAAK,MAAM;AAClB,QAAM,QAAQ,KAAK;AACnB,QAAM,gBAAgB;AACtB,QAAM,mBAAmB,gBACrB,WAAW,SAAS,gBAAgB,oBAAoB,gBAAgB,YAAY,QAAQ,CAAC;AACjG,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAW;AAAA,IACf,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAEA,QAAM,aAAa;AAAA,IACjB,WAAW;AAAA,IACX,OAAO,MAAM;AAAA,IACb,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,MAAO;AAAA;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU,CAAE,KAAK,oBAAqB;AAAA,IACtC,YAAY;AAAA,EACd;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,CAAE,KAAM;AAAA,IACjB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,MACR,KAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA;AAAA,cAEE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,QACP,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU,CAAE,KAAK,gBAAiB;AAAA,MACpC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAAA,MACA;AAAA;AAAA,QAEE,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL,MAAM,OAAO,YAAY,aAAa;AAAA,UACtC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA;AAAA;AAAA;AAAA,QAGE,eAAe;AAAA,QACf,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL,QAAQ,mBAAmB;AAAA,UAC3B,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,WAAW,EAAE,GAAG,iBAAiB;AAAA,QACjC,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}